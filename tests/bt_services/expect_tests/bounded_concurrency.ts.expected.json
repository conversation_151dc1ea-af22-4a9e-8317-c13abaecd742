[{"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c8415c5040a15c6812f498861a9f8f1c28c1739cb632a21cd62f46660017e63e", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "262fdb2babe3a56de858d5261f716cb35979794e4f799d6666a5123920e5cebd", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}]