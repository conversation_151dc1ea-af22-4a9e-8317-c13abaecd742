from braintrust import BaseExperiment, Eval

PROJECT_NAME = "Base experiment test synchronous"


def main():
    base_kwargs = dict(
        data=[{"input": "a"}],
        task=lambda input: f"foo = {input}",
        scores=[],
    )

    base = Eval(PROJECT_NAME, **base_kwargs)
    base_experiment_name, base_experiment_id = (
        base.summary.experiment_name,
        base.summary.experiment_id,
    )

    using_base_name_kwargs = {
        **base_kwargs,
        "base_experiment_name": base_experiment_name,
    }
    using_base_name = Eval(PROJECT_NAME, **using_base_name_kwargs)
    assert using_base_name.summary.comparison_experiment_name == base_experiment_name

    using_base_id_kwargs = {
        **base_kwargs,
        "base_experiment_name": using_base_name.summary.experiment_name,
        "base_experiment_id": base_experiment_id,
    }
    using_base_id = Eval(PROJECT_NAME, **using_base_id_kwargs)
    assert using_base_id.summary.comparison_experiment_name == base_experiment_name

    using_base_name_with_data_kwargs = {
        **base_kwargs,
        "data": BaseExperiment(name=using_base_id.summary.experiment_name),
        "base_experiment_name": base_experiment_name,
    }
    using_base_name_with_data = Eval(PROJECT_NAME, **using_base_name_with_data_kwargs)
    assert using_base_name_with_data.summary.comparison_experiment_name == base_experiment_name

    using_base_id_with_data_kwargs = {
        **base_kwargs,
        "data": BaseExperiment(name=using_base_id.summary.experiment_name),
        "base_experiment_id": base_experiment_id,
    }
    using_base_id_with_data = Eval(PROJECT_NAME, **using_base_id_with_data_kwargs)
    assert using_base_id_with_data.summary.comparison_experiment_name == base_experiment_name


if __name__ == "__main__":
    main()
