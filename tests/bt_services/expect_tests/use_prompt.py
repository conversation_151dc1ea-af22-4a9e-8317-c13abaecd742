import os

import braintrust
import openai
import requests

if __name__ == "__main__":
    logger = braintrust.init_logger(project="calculator")
    prompt_data = requests.post(
        f"{logger._get_state().api_url}/v1/prompt",
        json=dict(
            project_id=logger.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "You are a calculator. Return results in JSON"},
                        {"role": "user", "content": "{{formula}}"},
                    ],
                },
                "options": {"params": {"response_format": {"type": "json_object"}}},
            },
            name="calculator",
            slug="calculator",
        ),
        headers=dict(Authorization=f"Bearer {os.environ['BRAINTRUST_API_KEY']}"),
    )
    print(prompt_data.text)

    prompt = braintrust.load_prompt(
        project=logger.project.name, slug="calculator", defaults=dict(model="gpt-3.5-turbo")
    )

    client = braintrust.wrap_openai(openai.OpenAI())
    print(client.chat.completions.create(**prompt.build(formula="1+1")))

    print(
        client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "system", "content": "You are a calculator"}, {"role": "user", "content": "1+1"}],
        )
    )

    prompt_data = requests.post(
        f"{logger._get_state().api_url}/v1/prompt",
        json=dict(
            project_id=logger.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "You are a calculator"},
                        {"role": "user", "content": "{{formula}}"},
                    ],
                    "tools": """[{
            "type": "function",
            "function": {
              "description": "Returns the sum of two numbers.",
              "name": "add",
              "parameters": {
                "type": "object",
                "properties": {
                  "a": {
                    "type": "number",
                    "description": "The first number"
                  },
                  "b": {
                    "type": "number",
                    "description": "The second number"
                  }
                },
                "required": ["a", "b"]
              }
            }
          }]""",
                }
            },
            name="calculator-tools",
            slug="calculator-tools",
        ),
        headers=dict(Authorization=f"Bearer {os.environ['BRAINTRUST_API_KEY']}"),
    )
    print(prompt_data.text)

    prompt = braintrust.load_prompt(
        project=logger.project.name, slug="calculator-tools", defaults=dict(model="gpt-3.5-turbo")
    )

    client = braintrust.wrap_openai(openai.OpenAI())
    print(client.chat.completions.create(**prompt.build(formula="1+1")))

    print(
        client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "system", "content": "You are a calculator"}, {"role": "user", "content": "1+1"}],
        )
    )

    def _build_empty_tools_prompt(slug, tools):
        empty_tools_prompt_data = requests.post(
            f"{logger._get_state().api_url}/v1/prompt",
            json=dict(
                project_id=logger.project.id,
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {"role": "system", "content": "You are a calculator"},
                            {"role": "user", "content": "{{formula}}"},
                        ],
                        "tools": tools,
                    }
                },
                name=slug,
                slug=slug,
            ),
            headers=dict(Authorization=f"Bearer {os.environ['BRAINTRUST_API_KEY']}"),
        )
        print(empty_tools_prompt_data.text)

        empty_tools_prompt = braintrust.load_prompt(
            project=logger.project.name, slug=slug, defaults=dict(model="gpt-3.5-turbo")
        )

        client = braintrust.wrap_openai(openai.OpenAI())
        print(client.chat.completions.create(**empty_tools_prompt.build(formula="1+1")))

    _build_empty_tools_prompt("empty-tools", "")
    _build_empty_tools_prompt("blank-space-tools", " ")

    prompt_data = requests.post(
        f"{logger._get_state().api_url}/v1/prompt",
        json=dict(
            project_id=logger.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "You are a calculator"},
                        {"role": "user", "content": "{{input}}"},
                    ],
                }
            },
            name="calculator-tools",
            slug="flat-input",
        ),
        headers=dict(Authorization=f"Bearer {os.environ['BRAINTRUST_API_KEY']}"),
    )
    print(prompt_data.text)

    prompt = braintrust.load_prompt(
        project=logger.project.name, slug="flat-input", defaults=dict(model="gpt-3.5-turbo")
    )

    client = braintrust.wrap_openai(openai.OpenAI())
    for chunk in client.chat.completions.create(
        **prompt.build(input="1+1"), stream=True, stream_options={"include_usage": True}
    ):
        print(chunk)
