[{"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0", "input": "a", "is_root": false, "metadata": {"message": "In task"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": "d", "id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "input": "c", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c", "input": "c", "is_root": false, "metadata": {"message": "In task"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c", "span_parents": ["28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4"], "tags": null}, {"context": {}, "expected": "f", "id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "input": "e", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "f", "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "80f3bb11cb4154e630bdffba199f645474cee3f54d11be1605da5826041c0fa7", "input": "e", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "f", "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "80f3bb11cb4154e630bdffba199f645474cee3f54d11be1605da5826041c0fa7", "span_parents": ["6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e"], "tags": null}, {"context": {}, "expected": null, "id": "f5f24e403de7b135b770dff645895bf1819ba3e63b5b35186523d3c6f42eb7ac", "input": null, "is_root": false, "metadata": {"message": "In foo"}, "metrics": {}, "origin": null, "output": null, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "foo"}, "span_id": "f5f24e403de7b135b770dff645895bf1819ba3e63b5b35186523d3c6f42eb7ac", "span_parents": ["95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0"], "tags": null}, {"context": {}, "expected": null, "id": "f5f24e403de7b135b770dff645895bf1819ba3e63b5b35186523d3c6f42eb7ac", "input": null, "is_root": false, "metadata": {"message": "In foo"}, "metrics": {}, "origin": null, "output": null, "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "foo"}, "span_id": "f5f24e403de7b135b770dff645895bf1819ba3e63b5b35186523d3c6f42eb7ac", "span_parents": ["0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c"], "tags": null}, {"context": {}, "expected": null, "id": "bb38c58cdc2088305a247fedf0ea4983e2b0c22ba52dd4c9094d97ab23c26087", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "bb38c58cdc2088305a247fedf0ea4983e2b0c22ba52dd4c9094d97ab23c26087", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "e5345e1044b629dcbd16a963b1cbf3987e6f7522210a4860adbccfef06eac456", "input": {"expected": "d", "input": "c", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e5345e1044b629dcbd16a963b1cbf3987e6f7522210a4860adbccfef06eac456", "span_parents": ["28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4"], "tags": null}, {"context": {}, "expected": null, "id": "01c81375dbe71837b422ef69dbefcfb8a5e8af70fddff715680ddf311cb56226", "input": {"expected": "f", "input": "e", "metadata": {}, "output": "f"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "01c81375dbe71837b422ef69dbefcfb8a5e8af70fddff715680ddf311cb56226", "span_parents": ["6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e"], "tags": null}]