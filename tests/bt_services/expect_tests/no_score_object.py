from braintrust import <PERSON><PERSON>


def is_equal(expected, output):
    return expected == output


Eval(
    "foo",
    data=lambda: [
        {
            "input": "Foo",
            "expected": "Hi Foo",
        },
        {
            "input": "Bar",
            "expected": "Hello Bar",
        },
    ],  # Replace with your eval dataset
    task=lambda input: "Hi " + input,  # Replace with your LLM call
    scores=[is_equal],
)
