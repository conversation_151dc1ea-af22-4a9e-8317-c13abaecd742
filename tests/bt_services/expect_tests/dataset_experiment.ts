import { init, initDataset } from "braintrust";

async function main() {
  const dataset = initDataset("dataset_experiment");
  const records = dataset.fetch();
  let numRecords = 0;
  for await (const _ of records) {
    numRecords++;
  }

  if (numRecords === 0) {
    for (let i = 0; i < 10; i++) {
      dataset.insert({ input: i, expected: i * 2 });
      numRecords++;
    }
    await dataset.flush();
  }
  const outputs = new Array(numRecords);
  for await (const row of dataset.fetch()) {
    outputs[row.input] = row.expected;
  }
  console.log(await dataset.summarize());

  const experiment = init("dataset_experiment", { dataset });
  for await (const row of dataset.fetch()) {
    const output = outputs[row.input];
    experiment.log({
      input: row.input,
      output,
      expected: row.expected,
      scores: { closeness: row.expected === 0 ? 1 : output / row.expected },
      datasetRecordId: row.id,
    });
  }
  console.log(await experiment.summarize());
}

main();
