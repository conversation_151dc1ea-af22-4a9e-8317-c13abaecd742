import braintrust

dataset = braintrust.init_dataset(project="basic_dataset_py")

for i in range(10):
    id = dataset.insert(
        input=i,
        output={"result": i + 1, "error": None},
        metadata={
            "foo": i % 2,
            "bar": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
        },
    )

dataset.flush()
for record in dataset:
    print(record)

print(dataset.summarize())
