import braintrust

if __name__ == "__main__":
    experiment = braintrust.init("test update span")

    # Log initial span
    span_id = experiment.log(input="initial input", output="initial output", scores={"accuracy": 0.8})
    experiment.flush()

    # Update the span
    experiment.update_span(id=span_id, output="updated output", scores={"accuracy": 0.9})
    experiment.flush()

    # Log a span with a logger
    logger = braintrust.init_logger("test update span logger")
    logger_span_id = logger.log(input="logger input", output="logger output", scores={"precision": 0.7})
    logger.flush()

    # Update the logger span
    logger.update_span(id=logger_span_id, output="updated logger output", scores={"precision": 0.8})
    logger.flush()

    # Use global update_span
    with logger.start_span(id="global_update_span") as span:
        span.log(input="global input", output="global output")
    logger.flush()

    exported_span = span.export()
    braintrust.update_span(exported=exported_span, output="globally updated output")
    braintrust.flush()

    print("Test completed. Check the Braintrust UI for results.")
