import { initLogger } from "braintrust";

async function main() {
  const logger = initLogger({ projectName: "S1" });

  console.log("ROW1");
  let span = logger.startSpan({ name: "name1" });
  span.setAttributes({ name: "name2" });
  span.log({ input: "row1" });
  span.end();
  await span.flush();

  console.log("\nROW2");
  span = logger.startSpan({ name: "name1" });
  span.log({ input: "row2" });
  span.setAttributes({ name: "name3" });
  span.end();
  await span.flush();

  console.log("\nROW3");
  span = logger.startSpan({ name: "name1" });
  span.log({ input: "row3" });
  await span.flush();
  span.setAttributes({ name: "name4" });
  span.end();
  await span.flush();
}

main();
