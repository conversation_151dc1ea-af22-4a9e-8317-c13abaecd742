import "../expect_test_imports/queue_drop_when_full_setenv";
import * as braintrust from "braintrust";

async function main() {
  const row0Id = "row0";
  const row1Id = "row1";

  const e = braintrust.init("p");
  e.startSpan({
    event: { id: row0Id, input: "foo", output: "bar", scores: {} },
  });
  e.startSpan({
    event: { id: row1Id, input: "foo", output: "bar", scores: {} },
  });
  await braintrust.flush();

  const rows = await e.fetchedData();
  if (rows.length !== 2) {
    console.log(rows);
    throw new Error();
  }
}

main();
