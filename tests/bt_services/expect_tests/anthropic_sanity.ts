import { initLogger, wrap<PERSON>penA<PERSON> } from "braintrust";
import OpenAI from "openai";

const logger = initLogger({ projectName: "Anthropic sanity" });
const client = wrapOpenAI(new OpenAI({ apiKey: process.env.OPENAI_API_KEY }));

async function main() {
  await logger.traced(async (span) => {
    const ret = await client.chat.completions.create({
      model: "claude-3-haiku-20240307",
      messages: [{ role: "user", content: "What is 1+1" }],
    });

    console.log(ret);
    return ret.choices[0].message.content;
  });

  await logger.traced(async (span) => {
    const ret = await client.chat.completions.create({
      model: "claude-3-haiku-20240307",
      messages: [{ role: "user", content: "What is 1+1" }],
      stream: true,
    });

    for await (const event of ret) {
      console.log(event);
    }
  });
}

main();
