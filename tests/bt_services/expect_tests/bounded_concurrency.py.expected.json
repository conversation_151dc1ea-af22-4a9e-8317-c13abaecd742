[{"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": "foo", "id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo", "id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo", "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b73a0749f44fdbe88c1a6982e6fbd7753639b0805115fd2a3ed06efaa8d8cf72", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["31bffb5c98bbc4abea88ada5bc7bc9c729dcb6bf5b9b318eb73b1138ce7685ba"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}, {"context": {}, "expected": null, "id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "input": {"expected": "foo", "input": "foo", "metadata": {}, "output": "foo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0020e1ed22d4eea0b5bb0e8db7032d3ec46c1b28dde204a3c4dbd46860075b64", "span_parents": ["be76eb7283f2a78e1d75b64b20c5f9d9fd50f0fac373722b4b9a84c91f60e6fa"], "tags": null}]