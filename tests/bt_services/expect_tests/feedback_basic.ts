import { init, initLogger } from "braintrust";

const logger = initLogger({
  projectName: "test project",
  asyncFlush: true,
});

async function main() {
  // This should be logged in one audit entry
  // NOTE: Because of how we merge rows, we'll combine the two log calls into one row
  // and it'll appear the span is created by an external user. We could solve this properly
  // by having the SDK handle "merge barriers" and log them separately. However, we expect
  // this to rarely (if ever) occur.
  const requestId = logger.log({ input: 1, output: 1 });
  logger.logFeedback({
    id: requestId,
    scores: { foo: 0.5 },
    comment: "Foo bar bing",
  });
  await logger.flush();

  // This should be logged in two audit entries
  const requestId2 = logger.log({ input: 1, output: 2 });
  await logger.flush();
  logger.logFeedback({
    id: requestId2,
    scores: { foo: 0.7 },
    comment: "This is great!",
  });
  await logger.flush();

  // This should be logged in three audit entries
  const requestId3 = logger.log({ input: 1, output: 3 });
  await logger.flush();
  logger.logFeedback({
    id: requestId3,
    scores: { foo: 1 },
  });
  await logger.flush();
  logger.logFeedback({
    id: requestId3,
    comment: "You rock!",
  });
  await logger.flush();

  logger.logFeedback({
    id: requestId3,
    expected: 4,
  });

  const experiment = init("test experiment");
  const requestId4 = experiment.log({ input: 1, output: 3, scores: {} });
  experiment.logFeedback({
    id: requestId4,
    scores: { foo: 0.5 },
    comment: "Foo bar bing",
  });
  console.log(await experiment.summarize());
}

main();
