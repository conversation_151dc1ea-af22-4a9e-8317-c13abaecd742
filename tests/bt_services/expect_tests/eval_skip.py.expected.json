[{"context": {}, "expected": "b", "id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "5fa2e5bdbba124f7f82d577e6aff1c60e5b7b72e735c6d3d23fd55e374cb2782", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": null}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"null_score": null}, "span_attributes": {"name": "null_score", "type": "score"}, "span_id": "5fa2e5bdbba124f7f82d577e6aff1c60e5b7b72e735c6d3d23fd55e374cb2782", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "f70067dde15a3dbc1e192925b4e09914209acbb0f69a63a8b5471089dc814512", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"number_score": 0.5}, "span_attributes": {"name": "number_score", "type": "score"}, "span_id": "f70067dde15a3dbc1e192925b4e09914209acbb0f69a63a8b5471089dc814512", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}]