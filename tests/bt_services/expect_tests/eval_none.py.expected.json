[{"context": {}, "expected": "b", "id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "3609f5315296fb04a7e1caed1927140223e4daa5d96a43ce8b9c513778dd9f22", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": null}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"foo": null}, "span_attributes": {"name": "foo", "type": "score"}, "span_id": "3609f5315296fb04a7e1caed1927140223e4daa5d96a43ce8b9c513778dd9f22", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}]