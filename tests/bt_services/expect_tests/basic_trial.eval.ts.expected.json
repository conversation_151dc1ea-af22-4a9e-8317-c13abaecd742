[{"context": {}, "expected": "0", "id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": "1", "id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": "2", "id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": "3", "id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": "4", "id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "input": 4, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}, {"context": {}, "expected": null, "id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "7ce38129a370af2002962fc3fb130a2714d1222d3ef9b645a69bb538a712a4b0", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "7ce38129a370af2002962fc3fb130a2714d1222d3ef9b645a69bb538a712a4b0", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": null, "id": "b4b51b610b527de28c6fa75739230042bd9eeabf992c61ed0ed86739850bfb63", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "b4b51b610b527de28c6fa75739230042bd9eeabf992c61ed0ed86739850bfb63", "span_parents": ["6731806a2f416233a71d3283fd73849ad9b6bdbe3dbf6a796dc9cc438c8520dd"], "tags": null}, {"context": {}, "expected": null, "id": "5fb347b5546567b2dc06b3c6c0001c771f9f720b64d31cb3f52cd518c9b81500", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "5fb347b5546567b2dc06b3c6c0001c771f9f720b64d31cb3f52cd518c9b81500", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "fd5fea7f56f45358357fa9e50f37cbd97356bf669448c2bf64dab11c3014c39b", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "fd5fea7f56f45358357fa9e50f37cbd97356bf669448c2bf64dab11c3014c39b", "span_parents": ["18be428db55c260fdaf751474a15b71a91ea04c8a191d5b8dadbcdf87373c984"], "tags": null}, {"context": {}, "expected": null, "id": "4891dadd5173b67f03f92ac4054403ff45b30a54561f9e237396132c65127e39", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "4891dadd5173b67f03f92ac4054403ff45b30a54561f9e237396132c65127e39", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "b745dea83c7157b22c440acb6b3cc75d44425e9c970cfeceddade0509a75c815", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "b745dea83c7157b22c440acb6b3cc75d44425e9c970cfeceddade0509a75c815", "span_parents": ["80eaee9a9a0587f365049f34797e1e27a544fcf7a255942e6a8163067298fd20"], "tags": null}, {"context": {}, "expected": null, "id": "6048ef90d8e2b46244de102329a8894ab45db6e4a415006ff9a0e59b80620d2e", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "6048ef90d8e2b46244de102329a8894ab45db6e4a415006ff9a0e59b80620d2e", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "a77ad6975cb7d465c7ec3017df7e8fcb49ffd698465ac34c971917a6e7a629b0", "input": {"expected": "0", "input": 0, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a77ad6975cb7d465c7ec3017df7e8fcb49ffd698465ac34c971917a6e7a629b0", "span_parents": ["c80ca4a6915dcace3591a8aec572ec990d5d48eb7b21c741352c39312a0fea37"], "tags": null}, {"context": {}, "expected": null, "id": "91983f07cd00a79fb8fe6ff16569398553ae3b30064872afed1a1bd6938b887e", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "91983f07cd00a79fb8fe6ff16569398553ae3b30064872afed1a1bd6938b887e", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": null, "id": "c1cd5be91be4adbe053b9ab0ae61938c0edd7459bebf154eca40d3605cc0c9e0", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "c1cd5be91be4adbe053b9ab0ae61938c0edd7459bebf154eca40d3605cc0c9e0", "span_parents": ["75a86b0e7af8079a103e6406b858e0fa4815da7296c5e85d24c5242216f91085"], "tags": null}, {"context": {}, "expected": null, "id": "815981edf9f0438db69514a82eec1e224b794e624c0061a5a356589898863112", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "815981edf9f0438db69514a82eec1e224b794e624c0061a5a356589898863112", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "dc2abdacc65f9c60b7814c0c3dcfda8d4abceca2eccc09b92511027d1e847cbe", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "dc2abdacc65f9c60b7814c0c3dcfda8d4abceca2eccc09b92511027d1e847cbe", "span_parents": ["db4385f14da64b4766670ade3c2e376c85897d8f3ddd9c4b51ff68332a925203"], "tags": null}, {"context": {}, "expected": null, "id": "7d7a0f016739901bd90ce98b5894984aea3d61f6606a476edcb1a4ce1f6e2f39", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "7d7a0f016739901bd90ce98b5894984aea3d61f6606a476edcb1a4ce1f6e2f39", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "ee339f4edd4e4ba7b8ff5c0fa8fa531ed650ca551316c6f7e016fd99996626b5", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ee339f4edd4e4ba7b8ff5c0fa8fa531ed650ca551316c6f7e016fd99996626b5", "span_parents": ["1144c638ccc89029786decf2b071448fcda941b5dd1e999d1020a0b62f3fa60d"], "tags": null}, {"context": {}, "expected": null, "id": "b1560542447d5bca9c0c5afabef999869a550722eac020c2fc282f396493c3e9", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "b1560542447d5bca9c0c5afabef999869a550722eac020c2fc282f396493c3e9", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "e9199cfef99ca30444b4c13b595e527cb95bc58c5fa92ddd7ad375bac78b6b69", "input": {"expected": "1", "input": 1, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e9199cfef99ca30444b4c13b595e527cb95bc58c5fa92ddd7ad375bac78b6b69", "span_parents": ["68268a8e35028b25b9ebbfdb72af02331566446e0b9036337a8bc11ea77bf786"], "tags": null}, {"context": {}, "expected": null, "id": "6720233251f59580a646d1aeb98582a37ec7687b1afabb79ccd08c0d82eb7669", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "6720233251f59580a646d1aeb98582a37ec7687b1afabb79ccd08c0d82eb7669", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "74d6e157e1d8a43ccdacb229ea8b2293d7d36fc5a77932c2665670f1b18df588", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "74d6e157e1d8a43ccdacb229ea8b2293d7d36fc5a77932c2665670f1b18df588", "span_parents": ["2ab8019cc31278fc836687a703f9d44d6294a9d590a528e671c1b6842e19239a"], "tags": null}, {"context": {}, "expected": null, "id": "6f1f19e16e86dc641d5b9a72b1d00914e377152401062a63bba23cf2661f9e86", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "6f1f19e16e86dc641d5b9a72b1d00914e377152401062a63bba23cf2661f9e86", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": null, "id": "7b6fae9933f7ab27ff7fef99e0f91933a782cc743d5f96edce69ad4e33f1a860", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "7b6fae9933f7ab27ff7fef99e0f91933a782cc743d5f96edce69ad4e33f1a860", "span_parents": ["db5a61d8c3065f00dd3dc9c06571d5e715a38e87760314ef22cb1c7282764162"], "tags": null}, {"context": {}, "expected": null, "id": "6e9d3c2b59902372c666ec32ad716c83c9f77263a533033c441bddb44126a52a", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "6e9d3c2b59902372c666ec32ad716c83c9f77263a533033c441bddb44126a52a", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "fe61633597acd0266ea5a0f855a591b4f7d5d4c0af2e9e2ff3703539429e20c5", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "fe61633597acd0266ea5a0f855a591b4f7d5d4c0af2e9e2ff3703539429e20c5", "span_parents": ["44dcedb5c71d701c140d22cbc8a57dcfe43bd75f48660b361d46ee5c9a39a915"], "tags": null}, {"context": {}, "expected": null, "id": "4ed77688f8a64f3a02cb84028ea121c03d92d408874a561056176e139f93af3e", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "4ed77688f8a64f3a02cb84028ea121c03d92d408874a561056176e139f93af3e", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "8c48d9043d8a9bf6e6bfc9ada08d0dc421e07ad9bbf7efb91d24ea441f5bb2a2", "input": {"expected": "2", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "8c48d9043d8a9bf6e6bfc9ada08d0dc421e07ad9bbf7efb91d24ea441f5bb2a2", "span_parents": ["3e07269e8538fcfa7570e37674d1706ca5992e96d10220df751a70ce695ef5aa"], "tags": null}, {"context": {}, "expected": null, "id": "1308f109309a0b07db27b1b67ad189545fa58d18d42efd57241dbe98252d27b0", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "1308f109309a0b07db27b1b67ad189545fa58d18d42efd57241dbe98252d27b0", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "3c98910c024fc21adb191da5239f8dc67211ff34419fb069c4c6821347e2cf60", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "3c98910c024fc21adb191da5239f8dc67211ff34419fb069c4c6821347e2cf60", "span_parents": ["6570d92f7b15ca08228fcb7435434772b577f6a1853fbf0a8d8dbe4e66e547db"], "tags": null}, {"context": {}, "expected": null, "id": "00b07104f3234ab88ee8e7806bbf181593dc6327810fa22a941fa2b35deace81", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "00b07104f3234ab88ee8e7806bbf181593dc6327810fa22a941fa2b35deace81", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": null, "id": "222f4227f3cbfc38cb6fdca4f3c6883b0f26c1bcda052605cd32b22609ecbb26", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "222f4227f3cbfc38cb6fdca4f3c6883b0f26c1bcda052605cd32b22609ecbb26", "span_parents": ["6ff0d23b9f050c25bdab3730ef0b4fcef420072e8487b5f8be9dfe364dc8807b"], "tags": null}, {"context": {}, "expected": null, "id": "afd90fa70da429e5988b9bd8777b14796ffbf86956868c8e599a16f135ac4e3b", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "afd90fa70da429e5988b9bd8777b14796ffbf86956868c8e599a16f135ac4e3b", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "d231a5779575886dfcc9069bf6f983aeb8e6dd97859dd4e34712fb7501b0fcf6", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "d231a5779575886dfcc9069bf6f983aeb8e6dd97859dd4e34712fb7501b0fcf6", "span_parents": ["1373e55abd4f549408bd44e00410e5066d22ef530f0f852d4c1c1fae9a8ee322"], "tags": null}, {"context": {}, "expected": null, "id": "31784610594b98fbed33dd85c5ecf9d725011597cb8619a04d189424c3af4452", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "31784610594b98fbed33dd85c5ecf9d725011597cb8619a04d189424c3af4452", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "54008e6c5664f8770ff1acdd2c8bdb0ae779547fa72f61e0b77dc4aa94456821", "input": {"expected": "3", "input": 3, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "54008e6c5664f8770ff1acdd2c8bdb0ae779547fa72f61e0b77dc4aa94456821", "span_parents": ["5c3caebcf192d25650c114e2b7bd5b04c4cdb0925eff451a7741c7d119ec9e71"], "tags": null}, {"context": {}, "expected": null, "id": "88cb370e889babae988a066a71df39580e45c50cfa2b851af530e0c064c03018", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "88cb370e889babae988a066a71df39580e45c50cfa2b851af530e0c064c03018", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "c308beb080678cfdbfac22ebaeffba6903f0557b6ba5331e5030d6b5be1f4015", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "c308beb080678cfdbfac22ebaeffba6903f0557b6ba5331e5030d6b5be1f4015", "span_parents": ["9c4b0ab649081d25b1c5a87fee4bedb5274332a43e555bd90dc2a193603c3393"], "tags": null}, {"context": {}, "expected": null, "id": "33d675a74c13f87b0b8a4d83f24a32a62484dff9df6379515cb1f679f4b7c573", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "33d675a74c13f87b0b8a4d83f24a32a62484dff9df6379515cb1f679f4b7c573", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "bef341ee9f1c268d17f400237bc64ce9b25fc6b5c113c9b339da5deefc2c78ab", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "bef341ee9f1c268d17f400237bc64ce9b25fc6b5c113c9b339da5deefc2c78ab", "span_parents": ["d735f7e5df993f8480810f0f350db2926598777428b1ebe52419a58fd1d84a2e"], "tags": null}, {"context": {}, "expected": null, "id": "2575ad3d4f2e9c802387da0af3dcc30eda132a8ae900f4046a1cc5ce68a723a9", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2575ad3d4f2e9c802387da0af3dcc30eda132a8ae900f4046a1cc5ce68a723a9", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "fd1c2c7577a01c18c77b147bf10a92076773002002b59feecd47623925c2b31d", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "fd1c2c7577a01c18c77b147bf10a92076773002002b59feecd47623925c2b31d", "span_parents": ["7de6791da20212c740951bac4027e5c0d57f99284eeaabcd175a1d6ee6a689ae"], "tags": null}, {"context": {}, "expected": null, "id": "71ee421df7015532eda3529ca30779ce8addbbcf8bcb8c8733f7cea0160fdcf7", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": {"empty_outputs": 1}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "71ee421df7015532eda3529ca30779ce8addbbcf8bcb8c8733f7cea0160fdcf7", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}, {"context": {}, "expected": null, "id": "e3d033cc574e2c278fcb4c2cf753a54514fa82470b3b4893e9e84ad1d2e43dc7", "input": {"expected": "4", "input": 4, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e3d033cc574e2c278fcb4c2cf753a54514fa82470b3b4893e9e84ad1d2e43dc7", "span_parents": ["eaceccfac9e4cf382184360a768587b8d0dbc501d401b3946f66f911bc5f6b35"], "tags": null}]