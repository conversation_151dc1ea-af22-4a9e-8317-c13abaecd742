import braintrust

RANDOM_NUMS = [(idx + 5) * 99 / 33 for idx in range(20)]

if __name__ == "__main__":
    dataset = braintrust.init_dataset("dataset_experiment")
    if len(list(dataset.fetch())) == 0:
        for i in range(10):
            dataset.insert(input=i, output=i * 2)
        dataset.flush()
    outputs = [None] * len(list(dataset.fetch()))
    for row in dataset.fetch():
        assert "expected" in row and "input" in row
        outputs[row["input"]] = row["expected"]
    print(dataset.summarize())

    experiment = braintrust.init("dataset_experiment", dataset=dataset)
    for row in dataset:
        assert "expected" in row and "input" in row
        output = outputs[row["input"]]
        experiment.log(
            input=row["input"],
            output=output,
            expected=row["expected"],
            scores={"closeness": 1 if row["expected"] == 0 else output / row["expected"]},
            dataset_record_id=row["id"],
        )

    print(experiment.summarize())
