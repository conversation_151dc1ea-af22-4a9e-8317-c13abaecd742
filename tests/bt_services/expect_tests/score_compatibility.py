"""
We're deprecating braintrust_core and moving the Score and Scorer classes
to autoevals and SDK libraries. These tests verify that we can properly handle
Scores from any of those places.
"""

from braintrust import Eval, EvalCase
from braintrust.score import Score as BTScore
from braintrust.score import Scorer as BTScorer
from braintrust_core.score import Score as CoreScore
from braintrust_core.score import Scorer as CoreScorer

from autoevals import NumericDiff
from autoevals import Score as AEScore
from autoevals import Scorer as AEScorer


def score_func_returns_dict(input, output, expected):
    return {"name": "score_func_returns_dict", "score": 0.9}


def core_score_function(input, output, expected):
    return CoreScore(name="core_score", score=0.8)


def bt_score_function(input, output, expected):
    return BTScore(name="bt_score", score=0.7)


def ae_score_function(input, output, expected):
    return AEScore(name="ae_score", score=0.65)


def raw_score_function(input, output, expected):
    """A function that returns a raw number."""
    return 0.6


class AEScorerChild(AEScorer):
    def _run_eval_sync(self, *args, **kwargs):
        return AEScore(name="ae_score", score=0.5)


class CoreScorerChild(CoreScorer):
    def _run_eval_sync(self, *args, **kwargs):
        return CoreScore(name="core_score", score=0.4)


class BTScorerChild(BTScorer):
    def _run_eval_sync(self, *args, **kwargs):
        return BTScore(name="bt_score", score=0.3)


class CustomScorer:
    async def eval_async(self, *args, **kwargs):
        return AEScore(name="ae_score", score=0.5)


def simple_task(input, hooks=None):
    """A simple pass-through task."""
    return input


Eval(
    "score_compatibility_test",
    data=[
        EvalCase(input=1, expected=1),
        EvalCase(input=2, expected=1),
    ],
    task=simple_task,
    scores=[
        # autoevals built in
        NumericDiff,
        NumericDiff(),
        # custom funcs
        score_func_returns_dict,
        core_score_function,
        bt_score_function,
        raw_score_function,
        # derived classes
        AEScorerChild,
        CoreScorerChild,
        BTScorerChild,
        AEScorerChild(),
        CoreScorerChild(),
        BTScorerChild(),
        # custom class
        CustomScorer,
        CustomScorer(),
    ],
)
