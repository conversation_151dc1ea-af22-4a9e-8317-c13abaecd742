import { wrapOpenAI } from "braintrust";
import { OpenAI } from "openai";
import { ChatCompletionStream } from "openai/resources/beta/chat/completions";

async function main() {
  const client = wrapOpenAI(new OpenAI());

  const chatCompletionStream: ChatCompletionStream =
    client.beta.chat.completions.stream({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "What is 1+1?" }],
      temperature: 0,
    });

  chatCompletionStream.on("content", (content) => {
    // Store the chunk of data
    console.log("CONTENT", content);
  });

  chatCompletionStream.on("end", () => {
    console.log("DONE!");
  });

  chatCompletionStream.on("error", (err) => {
    console.error("ERROR", err);
  });
}

main();
