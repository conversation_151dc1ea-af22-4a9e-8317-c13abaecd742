import { Eval, initLogger, initFunction } from "braintrust";

async function main() {
  const logger = initLogger({ projectName: "function-shim" });
  const API_URL = "http://127.0.0.1:8000";
  const project = await logger.project;
  await fetch(`${API_URL}/v1/prompt`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content: 'Return "Hi {{input}}", nothing else.',
            },
          ],
        },
        options: {
          params: {
            model: "gpt-4o",
          },
        },
      },
      name: "hi-prompt",
      slug: "hi-prompt",
    }),
  });
  await fetch(`${API_URL}/v1/function`, {
    method: "POST",
    headers: {
      Authorization: `Bear<PERSON> ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      function_type: "scorer",
      function_data: {
        type: "code",
        data: {
          type: "inline",
          runtime_context: {
            runtime: "python",
            version: "3.12",
          },
          code: `def handler(output, expected):
    return 1.0 if output == expected else 0.0`,
        },
      },
      function_schema: {
        parameters: {
          type: "object",
          properties: {
            output: { type: "string" },
            expected: { type: "string" },
          },
        },
        returns: { type: "number" },
      },
      name: "exact-match-scorer",
      slug: "exact-match-scorer",
    }),
  });

  await Eval(project.name, {
    data: [
      {
        input: "Joe",
        expected: "Hi Joe",
      },
      {
        input: "Jane",
        expected: "Hello Jane",
      },
    ],
    task: initFunction({
      projectName: project.name,
      slug: "hi-prompt",
    }),
    scores: [
      initFunction({
        projectName: project.name,
        slug: "exact-match-scorer",
      }),
    ],
  });

  await fetch(`${API_URL}/v1/function`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.BRAINTRUST_API_KEY}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "Always pick choice A" }],
        },
        options: {
          model: "gpt-4o",
        },
        parser: {
          type: "llm_classifier",
          use_cot: true,
          choice_scores: {
            A: 0.1,
            B: 1,
          },
        },
      },
      name: "llm-scorer",
      slug: "llm-scorer",
      function_data: { type: "prompt" },
      function_type: "scorer",
    }),
  });

  await Eval(project.name, {
    data: [
      {
        input: "Joe",
        expected: "Hi Joe",
      },
      {
        input: "Jane",
        expected: "Hello Jane",
      },
    ],
    task: initFunction({
      projectName: project.name,
      slug: "hi-prompt",
    }),
    scores: [
      initFunction({
        projectName: project.name,
        slug: "llm-scorer",
      }),
    ],
  });
}

main();
