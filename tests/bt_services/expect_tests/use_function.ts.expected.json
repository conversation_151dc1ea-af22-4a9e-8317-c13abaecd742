[{"context": {}, "expected": null, "id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "input": "A span!", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "stream span", "type": "task"}, "span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "input": "A span!", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "stream span", "type": "task"}, "span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a255d402476df240c8918e2734d5b06ab7101b78b660f5eb57838352bd74cece", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "2 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "parallel_tool_calls": false, "prompt": {"variables": {"formula": "2 + 1"}}, "stream": true, "stream_options": {"include_usage": true}, "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 17, "prompt_tokens": 65, "tokens": 82}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":2,\"b\":1}", "name": "add"}, "id": "call_0kSsQxR7B0cMDwjEcbwSg5x2", "index": 0, "type": "function"}]}}], "root_span_id": "7a8dee601abc90027a87b0a0eefd33f5b078e969364439b44ef7aa4472ddb9b8", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "a255d402476df240c8918e2734d5b06ab7101b78b660f5eb57838352bd74cece", "span_parents": ["7a8dee601abc90027a87b0a0eefd33f5b078e969364439b44ef7aa4472ddb9b8"], "tags": null}, {"context": {}, "expected": null, "id": "3323d0c3a0c8f440af10b34f764f985b94bb556c3f9754312d4b56bfb19619a6", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "parallel_tool_calls": false, "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}, "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 17, "prompt_tokens": 65, "tokens": 82}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":4,\"b\":1}", "name": "add"}, "id": "call_kAkbSAmFjCkF0m0ngxoKmYBA", "index": 0, "type": "function"}]}}], "root_span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "3323d0c3a0c8f440af10b34f764f985b94bb556c3f9754312d4b56bfb19619a6", "span_parents": ["b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c"], "tags": null}, {"context": {}, "expected": null, "id": "cfb1789028263d232b827d06816819750ce7b5331ba914fcd8023ab1fea9b7bf", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "1 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "1 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "2", "role": "assistant"}}], "root_span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "cfb1789028263d232b827d06816819750ce7b5331ba914fcd8023ab1fea9b7bf", "span_parents": ["4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094"], "tags": null}, {"context": {}, "expected": null, "id": "b6bcef3f9a6d905100a48ee106bc8185dde6b4d6312aeb7870df4e9c151c9bef", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "2 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "2 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "3", "role": "assistant"}}], "root_span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "b6bcef3f9a6d905100a48ee106bc8185dde6b4d6312aeb7870df4e9c151c9bef", "span_parents": ["8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742"], "tags": null}, {"context": {}, "expected": null, "id": "26a7b7fc7bbfc583a604bc4618bb6de4240203cd22ecb73af114c76cced1c7e0", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "3 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "3 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "4", "role": "assistant"}}], "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "26a7b7fc7bbfc583a604bc4618bb6de4240203cd22ecb73af114c76cced1c7e0", "span_parents": ["80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b"], "tags": null}, {"context": {}, "expected": null, "id": "d6165a4822628066330aa5e685f8ccd69529aec2f995f343d6dcd7a616b1d876", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}, {"content": "8", "role": "assistant"}, {"content": "Are you sure?", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 1, "prompt_tokens": 41, "tokens": 42}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "d6165a4822628066330aa5e685f8ccd69529aec2f995f343d6dcd7a616b1d876", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "parent span", "type": "task"}, "span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "extra message span", "type": "task"}, "span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "input": {"formula": "1 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "7a8dee601abc90027a87b0a0eefd33f5b078e969364439b44ef7aa4472ddb9b8", "input": {"formula": "2 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"a": 2, "b": 1}, "root_span_id": "7a8dee601abc90027a87b0a0eefd33f5b078e969364439b44ef7aa4472ddb9b8", "scores": null, "span_attributes": {"name": "calculator-tools", "remote": true, "slug": "calculator-tools", "type": "function"}, "span_id": "7a8dee601abc90027a87b0a0eefd33f5b078e969364439b44ef7aa4472ddb9b8", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "input": {"formula": "2 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b", "input": {"formula": "3 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b", "span_parents": ["0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["79f40b6556264ff967adce566fcb30540d6d7f583bc7bd409e18076a9e1bc7ff"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f"], "tags": null}, {"context": {}, "expected": null, "id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "input": {"formula": "4 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"a": 4, "b": 1}, "root_span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "scores": null, "span_attributes": {"name": "calculator-tools", "remote": true, "slug": "calculator-tools", "type": "function"}, "span_id": "b7fd473bc92d8df8ed9a854ea2639e7b75e7ff22e93aee837f0181290715dc8c", "span_parents": null, "tags": null}]