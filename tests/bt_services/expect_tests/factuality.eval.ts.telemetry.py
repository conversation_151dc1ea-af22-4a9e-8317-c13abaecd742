from tests.helpers.datetime import iso8601
from tests.helpers.number import number
from tests.helpers.telemetry import ctx, idempotency


def task():
    return {
        "individual": (
            5
            * [
                {
                    "event_name": "LogInsertedEvent",
                    "idempotency_key": idempotency(),
                    "external_customer_id": ctx("org_id"),
                    "properties": {
                        "log_bytes": number(),
                        "org_id": ctx("org_id"),
                        "scores_count": number(),
                        "metrics_count": number(),
                        "type": "individual",
                    },
                },
            ]
        ),
        "aggregated": [
            {
                "event_name": "LogInsertedEvent",
                "idempotency_key": idempotency(),
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "log_bytes": number(gte=1),
                    "metrics_count": 0,
                    "scores_count": number(gte=1),
                    "count": number(gte=1),
                },
                "timestamp": iso8601(),
            },
        ],
    }
