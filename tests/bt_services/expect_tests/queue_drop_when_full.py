import os

os.environ["BRAINTRUST_SYNC_FLUSH"] = "1"
os.environ["BRAINTRUST_QUEUE_SIZE"] = "1"

import braintrust

if __name__ == "__main__":
    row0_id = "row0"
    row1_id = "row1"

    e = braintrust.init("p")
    e.state.enforce_queue_size_limit(True)
    # Use start_span to guarantee we only log one item.
    e.start_span(id=row0_id, input="foo", output="bar", scores=dict())
    e.start_span(id=row1_id, input="foo", output="bar", scores=dict())
    braintrust.flush()

    rows = list(e.fetch())
    assert len(rows) == 1, len(rows)
    assert rows[0]["id"] == row1_id, rows[0]["id"]
