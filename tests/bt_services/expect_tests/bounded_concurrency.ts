import { <PERSON><PERSON>htein } from "autoevals";
import { Eval } from "braintrust";

const MAX_TASKS = 5;

let activeTasks = 0;

async function task() {
  try {
    activeTasks += 1;
    if (activeTasks > MAX_TASKS) {
      throw new Error("Too many tasks");
    }
    await new Promise((resolve) => setTimeout(resolve, 100));
  } finally {
    activeTasks -= 1;
  }
  return "foo";
}

async function main() {
  let summary = await Eval("bounded concurrency", {
    data: Array.from({ length: 50 }, () => ({ input: "foo", expected: "foo" })),
    task,
    scores: [Levenshtein],
  });
  let errors = summary.results.reduce(
    (acc, curr) => acc + Number(!!curr.error),
    0,
  );
  if (errors === 0) {
    throw new Error("Expected some errors without concurrency bound");
  }

  summary = await Eval("bounded concurrency", {
    data: Array.from({ length: 50 }, () => ({ input: "foo", expected: "foo" })),
    task,
    scores: [<PERSON><PERSON><PERSON><PERSON>],
    maxConcurrency: MAX_TASKS,
  });
  errors = summary.results.reduce((acc, curr) => acc + Number(!!curr.error), 0);
  if (errors > 0) {
    throw new Error("Expected no errors with concurrency bound");
  }

  // Zero records should work
  summary = await Eval("bounded concurrency", {
    data: Array.from({ length: 0 }, () => ({ input: "foo", expected: "foo" })),
    task,
    scores: [Levenshtein],
    maxConcurrency: MAX_TASKS,
  });
  errors = summary.results.reduce((acc, curr) => acc + Number(!!curr.error), 0);
  if (errors > 0) {
    throw new Error("Expected no errors with concurrency bound");
  }
}

main();
