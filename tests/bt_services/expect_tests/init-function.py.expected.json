[{"context": {}, "expected": "Hello <PERSON>", "id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "input": "<PERSON>", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "span_parents": null, "tags": null}, {"context": {}, "expected": "Hello <PERSON>", "id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "input": "<PERSON>", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "hi-prompt", "remote": true, "slug": "hi-prompt", "type": "function"}, "span_id": "577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c", "span_parents": ["a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a"], "tags": null}, {"context": {}, "expected": null, "id": "577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "hi-prompt", "remote": true, "slug": "hi-prompt", "type": "function"}, "span_id": "577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c", "span_parents": ["a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a"], "tags": null}, {"context": {}, "expected": null, "id": "a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a", "span_parents": ["989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012"], "tags": null}, {"context": {}, "expected": null, "id": "a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a88cd32496b06072c5d284ee63a121ad3b58fb6c5afc97516c9bf522254b154a", "span_parents": ["989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012"], "tags": null}, {"context": {}, "expected": "<PERSON> <PERSON>", "id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "input": "<PERSON>", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "span_parents": null, "tags": null}, {"context": {}, "expected": "<PERSON> <PERSON>", "id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "input": "<PERSON>", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "hi-prompt", "remote": true, "slug": "hi-prompt", "type": "function"}, "span_id": "444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95", "span_parents": ["7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d"], "tags": null}, {"context": {}, "expected": null, "id": "444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "hi-prompt", "remote": true, "slug": "hi-prompt", "type": "function"}, "span_id": "444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95", "span_parents": ["7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d"], "tags": null}, {"context": {}, "expected": null, "id": "7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d", "span_parents": ["8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65"], "tags": null}, {"context": {}, "expected": null, "id": "7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON> <PERSON>", "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "7ad6a56362dc42e9ea5fef990ca4656ced9afed076877c753c2c15ce80e9782d", "span_parents": ["8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65"], "tags": null}, {"context": {}, "expected": null, "id": "a167941bb6ba461d325df22338718e7ce96efa56a61d3abbb1b9c3519d4aa7e6", "input": [{"content": "Always pick choice A", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "parallel_tool_calls": false, "prompt": {"variables": {"expected": "<PERSON> <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON> <PERSON>"}}, "stream": true, "stream_options": {"include_usage": true}, "tool_choice": {"function": {"name": "select_choice"}, "type": "function"}, "tools": [{"function": {"description": "Call this function to select a choice.", "name": "select_choice", "parameters": {"properties": {"choice": {"description": "The choice", "enum": ["A", "B"], "title": "Choice", "type": "string"}, "reasons": {"description": "Write out in a step by step manner your reasoning to be sure that your conclusion is correct. Avoid simply stating the correct answer at the outset.", "title": "Reasoning", "type": "string"}}, "required": ["reasons", "choice"], "title": "CoTResponse", "type": "object"}}, "type": "function"}]}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 31, "prompt_tokens": 113, "tokens": 144}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"reasons\":\"The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A.\",\"choice\":\"A\"}", "name": "select_choice"}, "id": "call_sBtFA35OkhhW17KSokPlKYwK", "index": 0, "type": "function"}]}}], "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "Chat Completion", "purpose": "scorer", "type": "llm"}, "span_id": "a167941bb6ba461d325df22338718e7ce96efa56a61d3abbb1b9c3519d4aa7e6", "span_parents": ["4815d5aacaa04e713c5beed0bebada36980ba160cd50511961f74cf491ba3eca"], "tags": null}, {"context": {}, "expected": null, "id": "a801482b61079b3d812866c47b5e48a6397c23c93a8781b0579c525f609ffe53", "input": [{"content": "Always pick choice A", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "parallel_tool_calls": false, "prompt": {"variables": {"expected": "Hello <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}}, "stream": true, "stream_options": {"include_usage": true}, "tool_choice": {"function": {"name": "select_choice"}, "type": "function"}, "tools": [{"function": {"description": "Call this function to select a choice.", "name": "select_choice", "parameters": {"properties": {"choice": {"description": "The choice", "enum": ["A", "B"], "title": "Choice", "type": "string"}, "reasons": {"description": "Write out in a step by step manner your reasoning to be sure that your conclusion is correct. Avoid simply stating the correct answer at the outset.", "title": "Reasoning", "type": "string"}}, "required": ["reasons", "choice"], "title": "CoTResponse", "type": "object"}}, "type": "function"}]}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 31, "prompt_tokens": 113, "tokens": 144}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"reasons\":\"The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A.\",\"choice\":\"A\"}", "name": "select_choice"}, "id": "call_sBtFA35OkhhW17KSokPlKYwK", "index": 0, "type": "function"}]}}], "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "Chat Completion", "purpose": "scorer", "type": "llm"}, "span_id": "a801482b61079b3d812866c47b5e48a6397c23c93a8781b0579c525f609ffe53", "span_parents": ["31260a553608fb4481da71cf581dca2c90b95deee766a3edde94b52ee3d5b9e8"], "tags": null}, {"context": {}, "expected": null, "id": "98681077acfd01940368ad26ca875a239a51e443f3b2d35d0ec9442d5ac16078", "input": [{"content": "Return \"<PERSON> <PERSON>\", nothing else.", "role": "system"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": "<PERSON>"}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 2, "prompt_cached_tokens": 0, "prompt_tokens": 15, "tokens": 17}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<PERSON>", "role": "assistant"}}], "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "98681077acfd01940368ad26ca875a239a51e443f3b2d35d0ec9442d5ac16078", "span_parents": ["577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c"], "tags": null}, {"context": {}, "expected": null, "id": "98681077acfd01940368ad26ca875a239a51e443f3b2d35d0ec9442d5ac16078", "input": [{"content": "Return \"<PERSON> <PERSON>\", nothing else.", "role": "system"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": "<PERSON>"}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 2, "prompt_cached_tokens": 0, "prompt_tokens": 15, "tokens": 17}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<PERSON>", "role": "assistant"}}], "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "98681077acfd01940368ad26ca875a239a51e443f3b2d35d0ec9442d5ac16078", "span_parents": ["577d986479cbaa2cf953ec575f762790d0318865c678555d82833016d208484c"], "tags": null}, {"context": {}, "expected": null, "id": "79623e0056183bfd1d08e230ffc1c89ef0bc34f18bda94d5f07075c78ac65acc", "input": [{"content": "Return \"<PERSON> <PERSON>\", nothing else.", "role": "system"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": "<PERSON>"}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 2, "prompt_cached_tokens": 0, "prompt_tokens": 15, "tokens": 17}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<PERSON> <PERSON>", "role": "assistant"}}], "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "79623e0056183bfd1d08e230ffc1c89ef0bc34f18bda94d5f07075c78ac65acc", "span_parents": ["444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95"], "tags": null}, {"context": {}, "expected": null, "id": "79623e0056183bfd1d08e230ffc1c89ef0bc34f18bda94d5f07075c78ac65acc", "input": [{"content": "Return \"<PERSON> <PERSON>\", nothing else.", "role": "system"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": "<PERSON>"}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 2, "prompt_cached_tokens": 0, "prompt_tokens": 15, "tokens": 17}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "<PERSON> <PERSON>", "role": "assistant"}}], "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "79623e0056183bfd1d08e230ffc1c89ef0bc34f18bda94d5f07075c78ac65acc", "span_parents": ["444dc291a22e328f53a2a82372d726f93aa82c6a47c284622050d02734caac95"], "tags": null}, {"context": {}, "expected": null, "id": "31260a553608fb4481da71cf581dca2c90b95deee766a3edde94b52ee3d5b9e8", "input": {"expected": "Hello <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"metadata": {"choice": "A", "rationale": "The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A."}, "name": "llm-scorer", "score": 0.1}, "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "llm-scorer", "remote": true, "slug": "llm-scorer", "type": "score"}, "span_id": "31260a553608fb4481da71cf581dca2c90b95deee766a3edde94b52ee3d5b9e8", "span_parents": ["c8b648a0aab82189a1078354c529e8f94c78e28bdc2921cde0538411373a9473"], "tags": null}, {"context": {}, "expected": null, "id": "34a5ab91f33ee21fed3b2894b72b387e12ad34d62ee788837153409bde82976d", "input": {"expected": "Hello <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": {"init_function-function-shim-exact-match-scorer-latest": 0}, "span_attributes": {"name": "init_function-function-shim-exact-match-scorer-latest", "type": "score"}, "span_id": "34a5ab91f33ee21fed3b2894b72b387e12ad34d62ee788837153409bde82976d", "span_parents": ["989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012"], "tags": null}, {"context": {}, "expected": null, "id": "9f2c5adcc7d5205b939a1a02aa2669edff61deaad22cc5f3ce03934f8dcadb07", "input": {"expected": "Hello <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 0, "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": null, "span_attributes": {"name": "exact-match-scorer", "remote": true, "slug": "exact-match-scorer", "type": "score"}, "span_id": "9f2c5adcc7d5205b939a1a02aa2669edff61deaad22cc5f3ce03934f8dcadb07", "span_parents": ["34a5ab91f33ee21fed3b2894b72b387e12ad34d62ee788837153409bde82976d"], "tags": null}, {"context": {}, "expected": null, "id": "c8b648a0aab82189a1078354c529e8f94c78e28bdc2921cde0538411373a9473", "input": {"expected": "Hello <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}, "is_root": false, "metadata": {"choice": "A", "rationale": "The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A."}, "metrics": {}, "origin": null, "output": {"score": 0.1}, "root_span_id": "989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012", "scores": {"llm-scorer": 0.1}, "span_attributes": {"name": "init_function-function-shim-llm-scorer-latest", "type": "score"}, "span_id": "c8b648a0aab82189a1078354c529e8f94c78e28bdc2921cde0538411373a9473", "span_parents": ["989f9bb58abb20c5458deef11b7ac0ea64677a61f71af13f474c7a67bb70c012"], "tags": null}, {"context": {}, "expected": null, "id": "4815d5aacaa04e713c5beed0bebada36980ba160cd50511961f74cf491ba3eca", "input": {"expected": "<PERSON> <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON> <PERSON>"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"metadata": {"choice": "A", "rationale": "The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A."}, "name": "llm-scorer", "score": 0.1}, "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "llm-scorer", "remote": true, "slug": "llm-scorer", "type": "score"}, "span_id": "4815d5aacaa04e713c5beed0bebada36980ba160cd50511961f74cf491ba3eca", "span_parents": ["9c380fb065a85fc21dcfb053e5d3fe29a5409df3f5521ebc84d354fa2ea4765b"], "tags": null}, {"context": {}, "expected": null, "id": "9c380fb065a85fc21dcfb053e5d3fe29a5409df3f5521ebc84d354fa2ea4765b", "input": {"expected": "<PERSON> <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON> <PERSON>"}, "is_root": false, "metadata": {"choice": "A", "rationale": "The instruction given was to 'always pick choice A'. Therefore, following that directive, I will select choice A."}, "metrics": {}, "origin": null, "output": {"score": 0.1}, "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": {"llm-scorer": 0.1}, "span_attributes": {"name": "init_function-function-shim-llm-scorer-latest", "type": "score"}, "span_id": "9c380fb065a85fc21dcfb053e5d3fe29a5409df3f5521ebc84d354fa2ea4765b", "span_parents": ["8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65"], "tags": null}, {"context": {}, "expected": null, "id": "a4ac0b8af289bf6fec59a7a1836e964e657aee370c9c6e6a3fbaae03349c0752", "input": {"expected": "<PERSON> <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON> <PERSON>"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 1, "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": null, "span_attributes": {"name": "exact-match-scorer", "remote": true, "slug": "exact-match-scorer", "type": "score"}, "span_id": "a4ac0b8af289bf6fec59a7a1836e964e657aee370c9c6e6a3fbaae03349c0752", "span_parents": ["a989c352db703e1602d470669517e905455e67b8a335fc0fd8fc6b0f4b1e4d28"], "tags": null}, {"context": {}, "expected": null, "id": "a989c352db703e1602d470669517e905455e67b8a335fc0fd8fc6b0f4b1e4d28", "input": {"expected": "<PERSON> <PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON> <PERSON>"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65", "scores": {"init_function-function-shim-exact-match-scorer-latest": 1}, "span_attributes": {"name": "init_function-function-shim-exact-match-scorer-latest", "type": "score"}, "span_id": "a989c352db703e1602d470669517e905455e67b8a335fc0fd8fc6b0f4b1e4d28", "span_parents": ["8b59d6dcc6e6502dc214c1a2f75f19fa4a2cbad32b35ddaf4479b5ddf5a0ed65"], "tags": null}]