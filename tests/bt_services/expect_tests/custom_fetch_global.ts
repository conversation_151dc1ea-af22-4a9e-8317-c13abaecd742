import { _internalGetGlobalState, initLogger, setFetch } from "braintrust";

const BOGUS_APP_URL = "http://bogus-app";

let apiUrlHits = 0;
const myFetch: typeof globalThis.fetch = async (url, options) => {
  let effectiveUrl = url;
  if (typeof url === "string") {
    const urlObj = new URL(url);
    const apiUrl = _internalGetGlobalState().apiUrl;
    if (urlObj.host === new URL(BOGUS_APP_URL).host) {
      urlObj.host = new URL(process.env.BRAINTRUST_APP_URL!).host;
      effectiveUrl = urlObj.toString();
    } else if (apiUrl && urlObj.host === new URL(apiUrl).host) {
      apiUrlHits++;
    }
  }
  return await fetch(effectiveUrl, options);
};

async function main() {
  setFetch(myFetch);
  const logger = initLogger({ projectName: "test" });
  logger.log({ input: "world" });
  await logger.flush();

  if (apiUrlHits === 0) {
    throw new Error("Expected API URL to be hit");
  }
}

main();
