import path from "path";
import { <PERSON>A<PERSON> } from "openai";

import { Scorer } from "braintrust/util";
import { LLMClassifierArgs, Sql } from "autoevals";
import { Eval, currentSpan, traced, wrapOpenAI } from "braintrust";
import {
  Example,
  AISearchOutput,
  extractAISearchResult,
  renderExample,
  eventsSearchCompletionArgs,
} from "@braintrust/local";
import { braintrustRootPath } from "@braintrust/local/dev";

const PROJECT_EXAMPLES_FILE = path.join(
  braintrustRootPath(),
  "evals/ai-search/project/examples.json",
);

const aiSchemaColumns = ["avg_foo_score", "avg_bar_score"];

async function loadProjectExamples(): Promise<Example[]> {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const examples = require(PROJECT_EXAMPLES_FILE).examples as Example[];
  return examples.map((e) => renderExample(e, { aiSchemaColumns }));
}

async function getClientOpts() {
  return {
    defaultHeaders: { "x-bt-use-cache": "always" },
  };
}

async function getOpenAiArgs() {
  const opts = await getClientOpts();
  return {
    openAiDefaultHeaders: opts.defaultHeaders,
  };
}

async function runAISearch(input: string): Promise<AISearchOutput> {
  const openAI = wrapOpenAI(new OpenAI(await getClientOpts()));
  const args = eventsSearchCompletionArgs({
    searchType: "projectExperiments",
    query: input,
    aiSchemaColumns,
    btql: true,
    scoreFields: [],
  });
  const completion = await openAI.chat.completions.create({
    ...args,
    seed: 123,
  });
  return extractAISearchResult(completion);
}

const AutoSearch: Scorer<
  AISearchOutput,
  LLMClassifierArgs<{ input: string }>
> = async ({ output, expected, ...args }) => {
  if (expected === undefined) {
    throw new Error("AutoSearch requires an expected value");
  }

  const name = "AutoSearch";

  if (output.match !== expected.match) {
    currentSpan().log({ scores: { functionChoice: 0 } });
    return { name, score: 0 };
  }

  currentSpan().log({ scores: { functionChoice: 1 } });
  if (expected.match) {
    return { name, score: 1 };
  }

  let filterScore: number | null = null;
  if (output.filter && expected.filter) {
    const outputFilter = output.filter;
    const expectedFilter = expected.filter;
    filterScore =
      (
        await traced(
          async (span) => {
            const sqlOutput = await Sql({
              ...args,
              output: outputFilter,
              expected: expectedFilter,
            });
            span.log({
              input: { output: output.filter, expected: expected.filter },
              output: sqlOutput,
            });
            return sqlOutput;
          },
          { name: "AutoFilter" },
        )
      ).score || 0;
  } else if (output.filter || expected.filter) {
    filterScore = 0;
  }
  currentSpan().log({ scores: { filter: filterScore } });

  let sortScore: number | null = null;
  if (output.sort && expected.sort) {
    const outputSort = output.sort;
    const expectedSort = expected.sort;
    sortScore =
      (
        await traced(
          async (span) => {
            const sqlOutput = await Sql({
              ...args,
              output: outputSort,
              expected: expectedSort,
            });
            span.log({
              input: { output: output.sort, expected: expected.sort },
              output: sqlOutput,
            });
            return sqlOutput;
          },
          { name: "AutoSort" },
        )
      ).score || 0;
  } else if (output.sort || expected.sort) {
    sortScore = 0;
  }
  currentSpan().log({ scores: { sort: sortScore } });

  const scores: number[] = [];
  for (const score of [filterScore, sortScore]) {
    if (score !== null) {
      scores.push(score);
    }
  }
  if (scores.length === 0) {
    return {
      name,
      score: 0,
      error: "Bad example: no filter or sort for expected SQL function call",
    };
  }
  return { name, score: scores.reduce((a, b) => a + b, 0) / scores.length };
};

Eval("AI Search Eval", {
  data: async () => await loadProjectExamples(),
  task: async (input: string, { span }) => {
    return await runAISearch(input);
  },
  scores: [async (args) => AutoSearch({ ...args, ...(await getOpenAiArgs()) })],
  metadata: {
    stable: "stable",
    random: ["random1", "random2"][Math.floor(Math.random() * 2)],
    ...eventsSearchCompletionArgs({
      query: "",
      searchType: "projectExperiments",
      aiSchemaColumns: [],
      scoreFields: [],
      btql: true,
    }),
  },
});
