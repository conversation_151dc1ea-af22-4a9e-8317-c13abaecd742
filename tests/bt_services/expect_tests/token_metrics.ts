import { <PERSON>l, wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import { OpenAI } from "openai";
import { Factuality } from "autoevals";

async function main() {
  // First test without an LLM call in the task, but an LLM call in the scorer. The metric counts
  const result = await <PERSON>l("test_token_metrics", {
    data: [{ input: "a", expected: "b" }],
    task: (input: string) => "b",
    scores: [Factuality],
  });

  if (
    result.summary.metrics?.prompt_tokens.metric != 0 ||
    result.summary.metrics?.completion_tokens.metric != 0 ||
    result.summary.metrics?.total_tokens.metric != 0 ||
    result.summary.metrics?.llm_duration != undefined
  ) {
    console.error(result.summary.metrics);
    throw new Error("Metrics should not be present");
  }

  const client = wrapOpenAI(new OpenAI());

  const result1 = await Eval("test_token_metrics", {
    data: [{ input: "a", expected: "b" }],
    task: async (input: string) => {
      const completion = await client.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: "next letter after " + input }],
      });
      return completion.choices[0].message.content ?? "";
    },
    scores: [Factuality],
  });

  console.log(result1);
  const promptTokens1 = result1.summary.metrics?.["prompt_tokens"]?.metric;
  if (!promptTokens1 || promptTokens1 <= 0) {
    throw new Error("Prompt tokens should be greater than 0");
  }

  const result2 = await Eval("test_token_metrics", {
    data: [{ input: "a", expected: "b" }],
    task: async (input: string) => {
      const completion = await client.chat.completions.create({
        model: "gpt-4",
        messages: [
          { role: "user", content: "print the next letter after " + input },
        ],
      });
      return completion.choices[0].message.content ?? "";
    },
    scores: [Factuality],
  });

  console.log(result2);
  const promptTokens2 = result2.summary.metrics?.["prompt_tokens"]?.metric;
  if (!promptTokens2 || promptTokens2 <= promptTokens1) {
    throw new Error("Prompt tokens should be greater than promptTokens1");
  }

  const completionTokens2 =
    result2.summary.metrics?.["completion_tokens"]?.metric;
  const totalTokens2 = result2.summary.metrics?.["total_tokens"]?.metric;
  if (!completionTokens2 || completionTokens2 <= 0) {
    throw new Error("Completion tokens should be greater than 0");
  }
  if (!totalTokens2 || totalTokens2 <= 0) {
    throw new Error("Total tokens should be greater than 0");
  }
}

main();
