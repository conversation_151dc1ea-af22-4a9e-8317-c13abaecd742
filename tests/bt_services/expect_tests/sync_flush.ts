import * as braintrust from "braintrust";

// For an async flush experiment, if we pass in a bogus project ID, it should
// only surface as an exception when we try to get the id.
async function testFailLoginAsyncFlush() {
  const experiment = braintrust.init({ projectId: "bogus" });
  experiment.log({ input: "a", output: "b", scores: {} });
  await experiment.flush();
  let error: unknown = undefined;
  try {
    await experiment.id;
  } catch (e) {
    error = e;
  }
  if (error === undefined) {
    throw new Error("Expected error");
  }
}

async function testFailingLoginSyncFlush() {
  braintrust._internalGetGlobalState().httpLogger().syncFlush = true;
  try {
    const experiment = braintrust.init({ projectId: "bogus" });
    experiment.log({ input: "a", output: "b", scores: {} });
    // Sleep to make sure no background thread tries (and fails) to flush our
    // record.
    await new Promise((resolve) => setTimeout(resolve, 100));
    let error: unknown = undefined;
    try {
      await experiment.flush();
    } catch (e) {
      error = e;
    }
    if (error === undefined) {
      throw new Error("Expected error");
    }
  } finally {
    braintrust._internalGetGlobalState().httpLogger().syncFlush = false;
  }
}

async function main() {
  await testFailLoginAsyncFlush();
  await testFailingLoginSyncFlush();
}

main();
