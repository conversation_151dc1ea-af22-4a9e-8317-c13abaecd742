[{"context": {}, "expected": null, "id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "input": "A span!", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "stream span", "type": "task"}, "span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "input": "A span!", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "stream span", "type": "task"}, "span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "cfb1789028263d232b827d06816819750ce7b5331ba914fcd8023ab1fea9b7bf", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "1 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "1 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "2", "role": "assistant"}}], "root_span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "cfb1789028263d232b827d06816819750ce7b5331ba914fcd8023ab1fea9b7bf", "span_parents": ["4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094"], "tags": null}, {"context": {}, "expected": null, "id": "b6bcef3f9a6d905100a48ee106bc8185dde6b4d6312aeb7870df4e9c151c9bef", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "2 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "2 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "3", "role": "assistant"}}], "root_span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "b6bcef3f9a6d905100a48ee106bc8185dde6b4d6312aeb7870df4e9c151c9bef", "span_parents": ["8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742"], "tags": null}, {"context": {}, "expected": null, "id": "26a7b7fc7bbfc583a604bc4618bb6de4240203cd22ecb73af114c76cced1c7e0", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "3 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "3 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "4", "role": "assistant"}}], "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "26a7b7fc7bbfc583a604bc4618bb6de4240203cd22ecb73af114c76cced1c7e0", "span_parents": ["80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b"], "tags": null}, {"context": {}, "expected": null, "id": "d6165a4822628066330aa5e685f8ccd69529aec2f995f343d6dcd7a616b1d876", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}, {"content": "8", "role": "assistant"}, {"content": "Are you sure?", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 1, "prompt_tokens": 41, "tokens": 42}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "d6165a4822628066330aa5e685f8ccd69529aec2f995f343d6dcd7a616b1d876", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "input": [{"content": "You are a calculator. Return just the answer, nothing else.", "role": "system"}, {"content": "4 + 1", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o", "prompt": {"variables": {"formula": "4 + 1"}}, "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_tokens": 1, "prompt_tokens": 28, "tokens": 29}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "5", "role": "assistant"}}], "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7adc528e72da13117699e4644d3db31dfea52ebc2fc904094fe6dea21a4b6df7", "span_parents": ["8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3"], "tags": null}, {"context": {}, "expected": null, "id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "parent span", "type": "task"}, "span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "extra message span", "type": "task"}, "span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "input": {"formula": "1 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "4764b78c975f61c88de4e154e8e2598ab4ae9ca00470e8e7ef7ff14573132094", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "input": {"formula": "2 + 1"}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8663ee3ce2b96453ebfbda8a59c4728924ef7017c983c1d9feaeec8c3aa88742", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b", "input": {"formula": "3 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "80999e57558c7ee3b0c749a1a34ee997eed56b9325cd774fbee5dbf3ca061a5b", "span_parents": ["0800c86288599448f6df34fa6040bce20b5b15bc006793d3d70b5474a85f6cff"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["bb54604a2cb5e5040aeade5c95d6262022480f4085b26c523e8de1b19ae783ac"], "tags": null}, {"context": {}, "expected": null, "id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "input": {"formula": "4 + 1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "5", "root_span_id": "ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f", "scores": null, "span_attributes": {"name": "calculator", "remote": true, "slug": "calculator", "type": "function"}, "span_id": "8dedf9e491367ed2a87d7aa1b3a05fa2bd7f6ad8d8f8fe71913ed29748a2aee3", "span_parents": ["ebfcceb3f226afe3e79de68316c4ac8e63269a40e8011cdce3ef91111922800f"], "tags": null}]