import asyncio
import inspect
import json
import os
from typing import List
from urllib.parse import quote

import braintrust
import httpx
import openai
import requests
from braintrust import ObjectMetadata
from braintrust.http_headers import BT_PARENT
from braintrust_local.otel_test_util import (
    ExpectedSpan,
    TestCase,
    get_http_client_with_capture_hook,
    verify_test_outputs,
)
from traceloop.sdk import Traceloop
from traceloop.sdk.decorators import (
    atask,
    aworkflow,
    task,
    workflow,
)

API_URL = os.environ.get("BRAINTRUST_API_URL", "http://localhost:8000")
API_KEY = os.environ.get("BRAINTRUST_API_KEY")
os.environ["TRACELOOP_BASE_URL"] = API_URL + "/otel"

# Disable metrics to reduce stdout clutter
os.environ["TRACELOOP_METRICS_ENABLED"] = "false"


def _run_population(nopenai, city):
    completion = nopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": f"What is the population of {city}?"}],
        max_tokens=20,
        temperature=0.9,
    )
    return completion.choices[0].message.content


@task()
def run_population_sf(nopenai):
    return _run_population(nopenai, "San Francisco")


@task()
def run_population_la(nopenai):
    return _run_population(nopenai, "Los Angeles")


@task()
def run_population_tokyo(nopenai):
    return _run_population(nopenai, "Tokyo")


@workflow()
def population(nopenai):
    return [run_population_sf(nopenai), run_population_la(nopenai), run_population_tokyo(nopenai)]


@task()
def run_story_stream(nopenai):
    completion = nopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": "Tell me about an adventure in San Francisco"}],
        stream=True,
    )
    content = ""
    for chunk in completion:
        content += chunk.choices[0].delta.content or ""
    return content


@workflow()
def story_stream(nopenai):
    return run_story_stream(nopenai)


@task()
def run_calculate(nopenai):
    completion = nopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "user", "content": "Calculate the sum of 123 and 456."},
        ],
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "add_numbers",
                    "description": "Add two numbers",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "First number"},
                            "b": {"type": "number", "description": "Second number"},
                        },
                        "required": ["a", "b"],
                    },
                },
            }
        ],
    )
    message = completion.choices[0].message
    assert len(message.tool_calls) == 1
    function_call = message.tool_calls[0].function

    function_name = function_call.name
    arguments = json.loads(function_call.arguments)
    if function_name != "add_numbers":
        raise Exception(f"Unexpected function name: {function_name}")
    result = arguments["a"] + arguments["b"]
    return f"The answer is {result}."


@workflow()
def calculate(nopenai):
    return run_calculate(nopenai)


@atask()
async def run_weather(aopenai):
    response = await aopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "You are an assistant that can access weather information."},
            {"role": "user", "content": "What is the weather in San Francisco and what attractions should I visit?"},
        ],
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "weather",
                    "description": "Get the weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The location to get the weather for",
                            },
                        },
                    },
                },
            },
        ],
        # NOTE(austin): Skip tool_choice because it doesn't appear in the OTEL trace
        # tool_choice={"type": "function", "function": {"name": "weather"}},
    )
    return response.choices[0].message.content


@aworkflow()
async def weather(aopenai):
    return await run_weather(aopenai)


@atask()
async def run_recipe(aopenai, food):
    completion = await aopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": f"Tell me what ingredients I need to make {food} at home."}],
    )
    return completion.choices[0].message.content


@atask()
async def run_food(aopenai):
    completion = await aopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "user",
                "content": "Give me an idea for something quick to make for breakfast. Say the name of the food only, without any comments.",
            }
        ],
    )
    food = completion.choices[0].message.content
    return await run_recipe(aopenai, food)


@aworkflow()
async def nested(aopenai):
    return await run_food(aopenai)


@atask()
async def run_weather_stream(aopenai):
    response = await aopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "You are an assistant that can access weather information."},
            {"role": "user", "content": "What is the weather in San Diego and what attractions should I visit?"},
        ],
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "weather",
                    "description": "Get the weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The location to get the weather for",
                            },
                        },
                    },
                },
            },
        ],
        # NOTE(austin): Skip tool_choice because it doesn't appear in the OTEL trace
        # tool_choice={"type": "function", "function": {"name": "weather"}},
        stream=True,
    )
    content = ""
    async for chunk in response:
        content += chunk.choices[0].delta.content or ""
    return content


@aworkflow()
async def weather_stream(aopenai):
    return await run_weather_stream(aopenai)


@atask()
async def run_multimodal(aopenai):
    completion = await aopenai.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": "Speak like a pirate. Arrrr."},
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Tell a brief story about the provided image(s).",
                    },
                    # NOTE(austin): This doesn't seem to work in Traceloop.
                    # {
                    #     "type": "image_url",
                    #     "image_url": {
                    #         "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
                    #     },
                    # },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
                        },
                    },
                ],
            },
        ],
    )
    return completion.choices[0].message.content


@aworkflow()
async def multimodal(aopenai):
    return await run_multimodal(aopenai)


# NOTE(austin): This test case is disabled because the proxy doesn't cache
# it, likely because it uses structured outputs.

# class Step(BaseModel):
#     explanation: str
#     output: str
#
#
# class MathReasoning(BaseModel):
#     steps: list[Step]
#     final_answer: str
#
#
# @atask()
# async def run_math(aopenai):
#     completion = await aopenai.beta.chat.completions.parse(
#         model="gpt-4o-mini",
#         messages=[
#             {
#                 "role": "system",
#                 "content": "You are a helpful math tutor. Guide the user through the solution step by step.",
#             },
#             {"role": "user", "content": "how can I solve 8x + 7 = -23"},
#         ],
#         response_format=MathReasoning,
#     )
#     return completion.choices[0].message.parsed
#
#
# @aworkflow()
# async def math(aopenai):
#     return await run_math(aopenai)


@task(name="entity_fields_task")
def run_entity_fields(color, flavor=None):
    return dict(content={"foo": "bar"}, color=color)


@workflow(name="entity_fields_workflow")
def entity_fields(color, flavor=None):
    return run_entity_fields(color, flavor=flavor), "zzz"


EXPECTED_CHAT_SPAN = ExpectedSpan(name="openai.chat")


TEST_CASES = [
    TestCase(
        test_name="population",
        run_test=population,
        expected_root_span=ExpectedSpan(
            name="population.workflow",
            children=[
                ExpectedSpan(
                    name="run_population_sf.task",
                    children=[EXPECTED_CHAT_SPAN],
                ),
                ExpectedSpan(
                    name="run_population_la.task",
                    children=[EXPECTED_CHAT_SPAN],
                ),
                ExpectedSpan(
                    name="run_population_tokyo.task",
                    children=[EXPECTED_CHAT_SPAN],
                ),
            ],
        ),
    ),
    TestCase(
        test_name="story_stream",
        run_test=story_stream,
        expected_root_span=ExpectedSpan(
            name="story_stream.workflow",
            children=[
                ExpectedSpan(
                    name="run_story_stream.task",
                    children=[EXPECTED_CHAT_SPAN],
                )
            ],
        ),
    ),
    TestCase(
        test_name="calculate",
        run_test=calculate,
        expected_root_span=ExpectedSpan(
            name="calculate.workflow",
            children=[
                ExpectedSpan(
                    name="run_calculate.task",
                    children=[EXPECTED_CHAT_SPAN],
                )
            ],
        ),
    ),
    TestCase(
        test_name="weather",
        run_test=weather,
        expected_root_span=ExpectedSpan(
            name="weather.workflow",
            children=[
                ExpectedSpan(
                    name="run_weather.task",
                    children=[EXPECTED_CHAT_SPAN],
                )
            ],
        ),
    ),
    TestCase(
        test_name="nested",
        run_test=nested,
        expected_root_span=ExpectedSpan(
            name="nested.workflow",
            children=[
                ExpectedSpan(
                    name="run_food.task",
                    children=[
                        EXPECTED_CHAT_SPAN,
                        ExpectedSpan(
                            name="run_recipe.task",
                            children=[EXPECTED_CHAT_SPAN],
                        ),
                    ],
                ),
            ],
        ),
    ),
    TestCase(
        test_name="weather_stream",
        run_test=weather_stream,
        expected_root_span=ExpectedSpan(
            name="weather_stream.workflow",
            children=[
                ExpectedSpan(
                    name="run_weather_stream.task",
                    children=[EXPECTED_CHAT_SPAN],
                )
            ],
        ),
    ),
    TestCase(
        test_name="multimodal",
        run_test=multimodal,
        expected_root_span=ExpectedSpan(
            name="multimodal.workflow",
            children=[
                ExpectedSpan(
                    name="run_multimodal.task",
                    children=[EXPECTED_CHAT_SPAN],
                )
            ],
        ),
    ),
    # NOTE(austin): This test case is disabled because the proxy doesn't cache
    # it, probably because it uses structured outputs.
    # TestCase(
    #     test_name="math",
    #     run_test=math,
    #     expected_root_span=ExpectedSpan(
    #         name="math.task",
    #         children=[EXPECTED_CHAT_SPAN],
    #     ),
    # ),
    TestCase(
        test_name="entity_fields_workflow",
        run_test=lambda _: entity_fields("green", flavor="spicy"),
        expected_root_span=ExpectedSpan(
            name="entity_fields_workflow.workflow",
            input=dict(args=["green"], kwargs={"flavor": "spicy"}),
            output=[{"content": {"foo": "bar"}, "color": "green"}, "zzz"],
            children=[
                ExpectedSpan(
                    name="entity_fields_task.task",
                    input=dict(args=["green"], kwargs={"flavor": "spicy"}),
                    output={"content": {"foo": "bar"}, "color": "green"},
                )
            ],
        ),
        skip_llm_spans_check=True,
    ),
]


def run_tests(
    test_cases: List[TestCase],
    project: ObjectMetadata,
):
    captured_requests = []

    http_client = get_http_client_with_capture_hook(captured_requests, async_=False)
    async_http_client = get_http_client_with_capture_hook(
        captured_requests,
        async_=True,
    )
    nopenai = openai.OpenAI(
        base_url=os.environ.get("OPENAI_BASE_URL"),
        api_key=os.environ.get("OPENAI_API_KEY"),
        http_client=http_client,
    )
    aopenai = openai.AsyncOpenAI(
        base_url=os.environ.get("OPENAI_BASE_URL"),
        api_key=os.environ.get("OPENAI_API_KEY"),
        http_client=async_http_client,
    )

    auth_str = quote(f"Bearer {API_KEY}")
    os.environ["TRACELOOP_HEADERS"] = f"Authorization={auth_str},{BT_PARENT}=project_id:{project.id}"
    Traceloop.init(disable_batch=True)

    captured_requests_by_test_name = {}
    for tc in test_cases:
        prev_length = len(captured_requests)

        if inspect.iscoroutinefunction(tc.run_test):
            asyncio.run(tc.run_test(aopenai))
        else:
            tc.run_test(nopenai)

        new_items = captured_requests[prev_length:]
        if not tc.skip_llm_spans_check and len(new_items) == 0:
            raise Exception(f"No new requests captured for test case {tc.test_name}")
        captured_requests_by_test_name[tc.test_name] = new_items

    return captured_requests_by_test_name


def main():
    project_name = "test_otel_traceloop"
    logger = braintrust.init_logger(project=project_name)
    assert project_name == logger.project.name

    captured_requests_by_test_name = run_tests(
        TEST_CASES,
        logger.project,
    )
    verify_test_outputs(
        API_URL,
        API_KEY,
        TEST_CASES,
        logger.project,
        captured_requests_by_test_name,
        test_name_metadata_key="traceloop.workflow.name",
    )
    print(f"Tests passed for {project_name}")


if __name__ == "__main__":
    main()
