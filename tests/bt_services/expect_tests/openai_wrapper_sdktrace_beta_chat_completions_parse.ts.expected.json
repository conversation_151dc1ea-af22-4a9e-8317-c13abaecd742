[{"context": {}, "expected": null, "id": "1e0cb3e1a06e05a971f7a0aae4543dcdd3099ba35eeafec07b054b3bdf49a6b4", "input": "Give me a genre and the best examples in that genre", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "Science Fiction", "root_span_id": "1e0cb3e1a06e05a971f7a0aae4543dcdd3099ba35eeafec07b054b3bdf49a6b4", "scores": null, "span_attributes": {"name": "async_query1", "type": "eval"}, "span_id": "1e0cb3e1a06e05a971f7a0aae4543dcdd3099ba35eeafec07b054b3bdf49a6b4", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a558620ce490a20f768dc6a2caa3c1cda52ac2a294c9d43303d1c0f4a7286bd0", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me a genre and the best examples in that genre", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-4o-mini", "provider": "openai", "response_format": {"json_schema": {"name": "genre_examples", "schema": {"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"examples": {"items": {"type": "string"}, "type": "array"}, "genre": {"type": "string"}}, "required": ["genre", "examples"], "type": "object"}, "strict": true}, "type": "json_schema"}}, "metrics": {"completion_tokens": 48, "prompt_tokens": 69, "tokens": 117}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"genre\":\"Science Fiction\",\"examples\":[\"<PERSON>ne by <PERSON>\",\"Neuromancer by <PERSON>\",\"The Left Hand of Darkness by <PERSON>\",\"Foundation by <PERSON>\",\"Snow Crash by <PERSON>\"]}", "parsed": {"examples": ["Dune by <PERSON>", "Neuromancer by <PERSON>", "The Left Hand of Darkness by <PERSON>", "Foundation by <PERSON>", "Snow Crash by <PERSON>"], "genre": "Science Fiction"}, "refusal": null, "role": "assistant"}}], "root_span_id": "1e0cb3e1a06e05a971f7a0aae4543dcdd3099ba35eeafec07b054b3bdf49a6b4", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "a558620ce490a20f768dc6a2caa3c1cda52ac2a294c9d43303d1c0f4a7286bd0", "span_parents": ["1e0cb3e1a06e05a971f7a0aae4543dcdd3099ba35eeafec07b054b3bdf49a6b4"], "tags": null}]