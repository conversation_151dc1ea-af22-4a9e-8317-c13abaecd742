[{"context": {}, "expected": "2", "id": "c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a", "input": "1", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "93078ddcb9c9e326d35696726e12c4423c81d6787a66d04b3fb889615f856385", "input": "1", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "93078ddcb9c9e326d35696726e12c4423c81d6787a66d04b3fb889615f856385", "span_parents": ["c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a"], "tags": null}, {"context": {}, "expected": null, "id": "27203325c2d6c6436d304dcc780b7e0e4bef961560ed4e9a3ff97046488ea37e", "input": {"expected": "2", "input": "1", "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "27203325c2d6c6436d304dcc780b7e0e4bef961560ed4e9a3ff97046488ea37e", "span_parents": ["c0a8fcb2293f033723d5492b2f373a79f7ac73bb76cbeefc9df39f9263538d2a"], "tags": null}]