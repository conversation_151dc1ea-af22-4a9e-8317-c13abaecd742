[{"context": {}, "expected": null, "id": "1a766eb8b5b684c00ebb6a68c81b984844568aa7b8a20cf1b0a27dab79dad875", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "f"}, "span_id": "1a766eb8b5b684c00ebb6a68c81b984844568aa7b8a20cf1b0a27dab79dad875", "span_parents": ["ff0d8e5c97b2ef72c67eb782babefbb4d77bf6a49a8d5969a2e0afc1b00b043c"], "tags": null}, {"context": {}, "expected": null, "id": "1b81f25c9d2258f6a1369466dc1d8ddf5c83e1454171c52d71b28a71d2fb60a9", "input": null, "is_root": false, "metadata": null, "metrics": {"foo": 0.1}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "d"}, "span_id": "1b81f25c9d2258f6a1369466dc1d8ddf5c83e1454171c52d71b28a71d2fb60a9", "span_parents": ["6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd"], "tags": null}, {"context": {}, "expected": null, "id": "2612c5ecb3217d136c26586dc6d4eeb690430ae95cf3360069c0b46782744b5e", "input": null, "is_root": false, "metadata": null, "metrics": {"foo": 0.2}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "c"}, "span_id": "2612c5ecb3217d136c26586dc6d4eeb690430ae95cf3360069c0b46782744b5e", "span_parents": ["6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd"], "tags": null}, {"context": {}, "expected": null, "id": "6b225ca2612836179f229d49d9f747b4b3afdafaed59f083fd814cec60da5ef1", "input": null, "is_root": false, "metadata": null, "metrics": {"foo": 0.1}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "e"}, "span_id": "6b225ca2612836179f229d49d9f747b4b3afdafaed59f083fd814cec60da5ef1", "span_parents": ["1b81f25c9d2258f6a1369466dc1d8ddf5c83e1454171c52d71b28a71d2fb60a9"], "tags": null}, {"context": {}, "expected": null, "id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "input": null, "is_root": true, "metadata": null, "metrics": {"foo": 0.1}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "a", "type": "eval"}, "span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "ff0d8e5c97b2ef72c67eb782babefbb4d77bf6a49a8d5969a2e0afc1b00b043c", "input": null, "is_root": false, "metadata": null, "metrics": {"foo": 0.1}, "origin": null, "output": null, "root_span_id": "6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd", "scores": null, "span_attributes": {"name": "b"}, "span_id": "ff0d8e5c97b2ef72c67eb782babefbb4d77bf6a49a8d5969a2e0afc1b00b043c", "span_parents": ["6eb4c9d553b03089bb15aa285d3dfd78bc4c4f3d8a93c55edab8b9af2c07e1bd"], "tags": null}]