import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { Dataset, Eval, initDataset, initLogger } from "braintrust";

const projectName = "tags_sanity";

const logger = initLogger({ projectName });

async function* yieldDatasetInputs(dataset: Dataset<false>) {
  for await (const item of dataset) {
    yield { input: item.input, expected: item.expected };
  }
}

async function main() {
  const span = logger.startSpan();

  // Only ["bar", "boo"] should show up in the logs
  span.log({ input: "foo", tags: ["bar", "baz"] });
  span.log({ tags: ["bar", "boo"] });

  await logger.flush();

  const child = span.startSpan();

  try {
    child.log({ input: "foo", tags: ["bar", "baz"] });
    throw new Error("child span should not be able to log");
  } catch {}

  // Now it should only have the tag zoo
  logger.logFeedback({
    id: span.id,
    tags: ["zoo"],
  });

  await logger.flush();

  const datasetName = `tags_sanity-${Date.now()}`;

  const dataset = initDataset(projectName, {
    dataset: datasetName,
    useOutput: false,
  });

  dataset.insert({ input: "foo", expected: "bar", tags: ["bar", "baz"] });
  dataset.insert({ input: "foo", expected: "bar", tags: ["bar", "bar"] });
  await dataset.flush();

  await Eval(projectName, {
    data: yieldDatasetInputs(
      initDataset(projectName, {
        dataset: datasetName,
        useOutput: false,
      }),
    ),
    task: (datum) => datum.input,
    scores: [Levenshtein],
  });
}

main();
