[{"context": {}, "expected": "<PERSON>", "id": "8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0", "input": "<PERSON>", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "665b97c7147bbc9d8026a496ebd1d62a93264d9e1fa706b61a321b3a6d3ebf62", "input": "<PERSON>", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "<PERSON>", "root_span_id": "8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "665b97c7147bbc9d8026a496ebd1d62a93264d9e1fa706b61a321b3a6d3ebf62", "span_parents": ["8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0"], "tags": null}, {"context": {}, "expected": null, "id": "4f69cf9ea12b24851c05219dc36a878349e70b62178cdd3499c562f47cc282e8", "input": {"expected": "<PERSON>", "input": "<PERSON>", "metadata": {}, "output": "<PERSON>"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "4f69cf9ea12b24851c05219dc36a878349e70b62178cdd3499c562f47cc282e8", "span_parents": ["8d3a5d3fdc54a32863e210b4942af50c96779b60105812137a913a777f0817e0"], "tags": null}]