import os

import requests
from braintrust import Eval, init_function, init_logger


def main():
    logger = init_logger(project="function-shim")
    API_URL = "http://127.0.0.1:8000"
    project = logger.project

    requests.post(
        f"{API_URL}/v1/prompt",
        headers={"Authorization": f"Bearer {os.environ['BRAINTRUST_API_KEY']}", "Content-Type": "application/json"},
        json={
            "project_id": project.id,
            "prompt_data": {
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "system", "content": 'Return "Hi {{input}}", nothing else.'}],
                },
                "options": {"params": {"model": "gpt-4o"}},
            },
            "name": "hi-prompt",
            "slug": "hi-prompt",
        },
    )
    requests.post(
        f"{API_URL}/v1/function",
        headers={"Authorization": f"Bearer {os.environ['BRAINTRUST_API_KEY']}", "Content-Type": "application/json"},
        json={
            "project_id": project.id,
            "function_type": "scorer",
            "function_data": {
                "type": "code",
                "data": {
                    "type": "inline",
                    "runtime_context": {"runtime": "python", "version": "3.12"},
                    "code": """def handler(output, expected):
    return 1.0 if output == expected else 0.0""",
                },
            },
            "function_schema": {
                "parameters": {
                    "type": "object",
                    "properties": {"output": {"type": "string"}, "expected": {"type": "string"}},
                },
                "returns": {"type": "number"},
            },
            "name": "exact-match-scorer",
            "slug": "exact-match-scorer",
        },
    )

    Eval(
        project.name,
        data=[
            {
                "input": "Joe",
                "expected": "Hi Joe",
            },
            {
                "input": "Jane",
                "expected": "Hello Jane",
            },
        ],
        task=init_function(project_name=project.name, slug="hi-prompt"),
        scores=[
            init_function(project_name=project.name, slug="exact-match-scorer"),
        ],
    )

    requests.post(
        f"{API_URL}/v1/function",
        headers={"Authorization": f"Bearer {os.environ['BRAINTRUST_API_KEY']}", "Content-Type": "application/json"},
        json={
            "project_id": project.id,
            "prompt_data": {
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Always pick choice A"}],
                },
                "options": {
                    "model": "gpt-4o",
                },
                "parser": {
                    "type": "llm_classifier",
                    "use_cot": True,
                    "choice_scores": {
                        "A": 0.1,
                        "B": 1,
                    },
                },
            },
            "name": "llm-scorer",
            "slug": "llm-scorer",
            "function_data": {"type": "prompt"},
            "function_type": "scorer",
        },
    )

    Eval(
        project.name,
        data=[
            {
                "input": "Joe",
                "expected": "Hi Joe",
            },
            {
                "input": "Jane",
                "expected": "Hello Jane",
            },
        ],
        task=init_function(project_name=project.name, slug="hi-prompt"),
        scores=[
            init_function(project_name=project.name, slug="llm-scorer"),
        ],
    )


if __name__ == "__main__":
    main()
