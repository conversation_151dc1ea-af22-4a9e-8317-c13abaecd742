import * as braintrust from "braintrust";

import { v4 as uuidv4 } from "uuid";
import { SpanComponentsV3 } from "braintrust/util";

async function testLoggerExportNonBlocking() {
  braintrust._internalGetGlobalState().httpLogger().syncFlush = true;
  try {
    const l = braintrust.initLogger({
      projectId: "invalid_id",
      asyncFlush: true,
    });
    l.log({ input: "0" });
    const parent0 = await l.export();
    const parent1 = await l.traced(async (span) => {
      span.log({ input: "1" });
      return await span.export();
    });
    await l.traced(
      async (span) => {
        span.log({ input: "2" });
      },
      { parent: parent0 },
    );
    await l.traced(
      async (span) => {
        span.log({ input: "3" });
      },
      { parent: parent1 },
    );

    let e = undefined;
    try {
      await braintrust.flush();
    } catch (err) {
      e = err;
    }

    if (e === undefined) {
      throw new Error("Expected error when flushing");
    }
  } finally {
    braintrust._internalGetGlobalState().httpLogger().syncFlush = false;
  }
}

async function testLoggerExportUseIdAfterFlushing() {
  braintrust._internalGetGlobalState().httpLogger().syncFlush = true;
  try {
    const projectName = uuidv4();
    const l = braintrust.initLogger({ projectName, asyncFlush: true });
    l.log({ input: "0" });
    let exportStr = await l.export();
    let components = SpanComponentsV3.fromStr(exportStr);
    if (components.data.object_id) {
      throw Error("Fail");
    }
    if (
      JSON.stringify(components.data.compute_object_metadata_args) !==
      JSON.stringify({ project_name: projectName, project_id: undefined })
    ) {
      throw new Error(
        `Fail: ${JSON.stringify(components.data.compute_object_metadata_args)}`,
      );
    }
    await braintrust.flush();
    exportStr = await l.export();
    components = SpanComponentsV3.fromStr(exportStr);
    if (!components.data.object_id) {
      throw Error("Fail");
    }
    if (components.data.compute_object_metadata_args) {
      throw new Error("Fail");
    }
    if (
      (await braintrust.spanComponentsToObjectId({ components })) !==
      (await l.id)
    ) {
      throw new Error("Fail");
    }
  } finally {
    braintrust._internalGetGlobalState().httpLogger().syncFlush = false;
  }
}

async function testResolveSpanComponentsToObjectId() {
  braintrust._internalGetGlobalState().httpLogger().syncFlush = true;
  try {
    const projectName = uuidv4();
    const l = braintrust.initLogger({ projectName, asyncFlush: true });
    l.log({ input: "0" });
    // Check resolving before flush.
    let exportStr = await l.export();
    let components = SpanComponentsV3.fromStr(exportStr);
    if (components.data.object_id) {
      throw Error("Fail");
    }
    if (
      (await braintrust.spanComponentsToObjectId({ components })) !==
      (await l.id)
    ) {
      throw new Error("Fail");
    }
    // This should resolve the object ID for the next export.
    await l.flush();
    // Check resolving after flush.
    exportStr = await l.export();
    components = SpanComponentsV3.fromStr(exportStr);
    if (!components.data.object_id) {
      throw Error("Fail");
    }
    if (
      (await braintrust.spanComponentsToObjectId({ components })) !==
      (await l.id)
    ) {
      throw new Error("Fail");
    }
  } finally {
    braintrust._internalGetGlobalState().httpLogger().syncFlush = false;
  }
}

async function main() {
  await testLoggerExportNonBlocking();
  await testLoggerExportUseIdAfterFlushing();
  await testResolveSpanComponentsToObjectId();
}

main();
