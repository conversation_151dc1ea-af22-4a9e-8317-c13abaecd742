// Mirror of tests/test_span_identifier_v1.py.

import { v4 as uuidv4 } from "uuid";

import {
  SpanObjectTypeV1,
  SpanComponentsV1,
  SpanRowIdsV1,
} from "braintrust/util";

function assertThrows(f: () => void) {
  let didThrow = false;
  try {
    f();
  } catch {
    didThrow = true;
  }
  if (!didThrow) {
    throw new Error("Function did not throw");
  }
}

function assertEquals(lhs: SpanComponentsV1, rhs: SpanComponentsV1) {
  const lhsStr = JSON.stringify(lhs.toObject());
  const rhsStr = JSON.stringify(rhs.toObject());
  if (lhsStr !== rhsStr) {
    throw new Error(`lhs != rhs:\nlhs = ${lhsStr}\nrhs = ${rhsStr}`);
  }
}

function testSpanRowIdsV1() {
  new SpanRowIdsV1({ rowId: "x", spanId: "x", rootSpanId: "x" });
  assertThrows(() => {
    new SpanRowIdsV1({ rowId: "", spanId: "x", rootSpanId: "x" });
  });
  assertThrows(() => {
    new SpanRowIdsV1({ rowId: "x", spanId: "", rootSpanId: "x" });
  });
  assertThrows(() => {
    new SpanRowIdsV1({ rowId: "x", spanId: "x", rootSpanId: "" });
  });
}

function testNoRowId() {
  for (const objectType of [
    SpanObjectTypeV1.EXPERIMENT,
    SpanObjectTypeV1.PROJECT_LOGS,
  ]) {
    const objectId = uuidv4();
    const components = new SpanComponentsV1({ objectType, objectId });
    assertEquals(SpanComponentsV1.fromStr(components.toStr()), components);
  }
}

function testWithRowId() {
  const [objectId, rowId, spanId, rootSpanId] = Array.from({ length: 4 }).map(
    () => uuidv4(),
  );
  let components = new SpanComponentsV1({
    objectType: SpanObjectTypeV1.EXPERIMENT,
    objectId,
    rowIds: new SpanRowIdsV1({ rowId, spanId, rootSpanId }),
  });
  assertEquals(SpanComponentsV1.fromStr(components.toStr()), components);

  components = new SpanComponentsV1({
    objectType: SpanObjectTypeV1.EXPERIMENT,
    objectId,
    rowIds: new SpanRowIdsV1({ rowId: "hello world", spanId, rootSpanId }),
  });
  assertEquals(SpanComponentsV1.fromStr(components.toStr()), components);

  function roundtripBadComponent(args?: {
    objectId?: string;
    rowId?: string;
    spanId?: string;
    rootSpanId?: string;
  }) {
    const x = uuidv4();
    const { objectId: _, ...rowIdKwargs } = args ?? {};
    const objectId = args?.objectId ?? x;
    const components = new SpanComponentsV1({
      objectType: SpanObjectTypeV1.EXPERIMENT,
      objectId,
      rowIds: new SpanRowIdsV1({
        ...{ rowId: x, spanId: x, rootSpanId: x },
        ...rowIdKwargs,
      }),
    });
    SpanComponentsV1.fromStr(components.toStr());
  }

  assertThrows(() => roundtripBadComponent({ objectId: "hello" }));
  assertThrows(() => roundtripBadComponent({ spanId: "hello" }));
  assertThrows(() => roundtripBadComponent({ rootSpanId: "hello" }));
}

async function main() {
  testSpanRowIdsV1();
  testNoRowId();
  testWithRowId();
}

main();
