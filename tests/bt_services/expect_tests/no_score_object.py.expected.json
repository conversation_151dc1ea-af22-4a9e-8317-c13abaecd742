[{"context": {}, "expected": "Hello Bar", "id": "38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927", "input": "Bar", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "Hi Bar", "root_span_id": "38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "467bff81f8f75057cc9eb363eed8e93f28cd06e6da4bf7058fb40dd59829f23c", "input": "Bar", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "Hi Bar", "root_span_id": "38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "467bff81f8f75057cc9eb363eed8e93f28cd06e6da4bf7058fb40dd59829f23c", "span_parents": ["38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927"], "tags": null}, {"context": {}, "expected": "Hi <PERSON>oo", "id": "6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2", "input": "Foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "Hi <PERSON>oo", "root_span_id": "6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "641b3fb23df80cd66d88504d3f9968bd287650dc31299c29ba7fadce24fa82ec", "input": "Foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "Hi <PERSON>oo", "root_span_id": "6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "641b3fb23df80cd66d88504d3f9968bd287650dc31299c29ba7fadce24fa82ec", "span_parents": ["6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2"], "tags": null}, {"context": {}, "expected": null, "id": "02679cf16fbfa8442d79f5c2b11a2849026cce2adbe5354fa6a97e76069c57cd", "input": {"expected": "Hello Bar", "input": "Bar", "metadata": {}, "output": "Hi Bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": false}, "root_span_id": "38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927", "scores": {"is_equal": 0}, "span_attributes": {"name": "is_equal", "type": "score"}, "span_id": "02679cf16fbfa8442d79f5c2b11a2849026cce2adbe5354fa6a97e76069c57cd", "span_parents": ["38963d229db6f15bfcf9d2473484c23728a18e77a7f20a2d09b9e3167bcb4927"], "tags": null}, {"context": {}, "expected": null, "id": "b8527dc6e90aa603bc91cf4249b90b395755aca009347f6f5648b9276c7e6df1", "input": {"expected": "Hi <PERSON>oo", "input": "Foo", "metadata": {}, "output": "Hi <PERSON>oo"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": true}, "root_span_id": "6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2", "scores": {"is_equal": 1}, "span_attributes": {"name": "is_equal", "type": "score"}, "span_id": "b8527dc6e90aa603bc91cf4249b90b395755aca009347f6f5648b9276c7e6df1", "span_parents": ["6952d24060d36cb1b93001723cc72175015cd119906ec2766d5c711e154b23c2"], "tags": null}]