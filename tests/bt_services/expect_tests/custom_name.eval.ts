import { <PERSON><PERSON> } from "braintrust";
import { Score, JSONDiff } from "autoevals";

function myScorer({ output, expected }: { output: string; expected: string }) {
  return JSONDiff({ output, expected });
}

function myHandrolledScorer({
  output,
  expected,
}: {
  output: string;
  expected: string;
}): Score {
  return {
    name: "Custom scorer",
    score: 0.5,
  };
}

function makeScorer() {
  return (args: { output: string; expected: string }) => {
    return JSONDiff(args);
  };
}
const myLambda = makeScorer();

Eval("Custom scorer", {
  data: [{ input: "a", expected: "b" }],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  task: (input: any) => input,
  scores: [myScorer, myHandrolledScorer, myLambda],
});
