import asyncio
import os

import braintrust
import openai
from braintrust.oai import wrap_openai

SYSTEM_PROMPT = """
    You are a helpful math tutor. You will be provided with a math problem,
    and your goal is to output a step by step solution, along with a final answer.
"""


def make_completion_kwargs(question):
    return dict(
        model="gpt-4o-mini",
        messages=[
            {
                "role": "system",
                "content": SYSTEM_PROMPT,
            },
            {
                "role": "user",
                "content": question,
            },
        ],
        response_format={
            "type": "json_schema",
            "json_schema": {
                "name": "math_reasoning",
                "schema": {
                    "type": "object",
                    "properties": {
                        "steps": {
                            "type": "array",
                            "items": {"type": "string"},
                        },
                        "final_answer": {"type": "string"},
                    },
                    "required": ["steps", "final_answer"],
                    "additionalProperties": False,
                },
                "strict": True,
            },
        },
    )


def sync_query(experiment):
    client = wrap_openai(openai.OpenAI())

    with experiment.start_span("sync_query") as root_span:
        question = "sin x = 0.5"
        resp = client.beta.chat.completions.parse(**make_completion_kwargs(question))
        root_span.log(input=question, output=resp.choices[0].model_dump())

    client.beta.chat.completions.parse(**make_completion_kwargs("x + 2 = -1"))


async def async_query(experiment):
    client = wrap_openai(openai.AsyncOpenAI())

    with experiment.start_span("async_query") as root_span:
        question = "How much slower does a clock in geosynchronous orbit run, for an observer on Earth's surface?"
        resp = await client.beta.chat.completions.parse(**make_completion_kwargs(question))
        root_span.log(input=question, output=resp.choices[0].model_dump())

    await client.beta.chat.completions.parse(**make_completion_kwargs("e^x = -1"))


if __name__ == "__main__":
    experiment = braintrust.init("openai_wrapper_beta")
    sync_query(experiment)
    asyncio.run(async_query(experiment))
    print(experiment.summarize())
