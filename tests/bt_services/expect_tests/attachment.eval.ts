// A sample program to insert several types of attachment.

import { Attachment, <PERSON><PERSON> } from "braintrust";

Eval("Attachment", {
  data: async () => {
    return [
      {
        input: new Attachment({
          data: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
          filename: "attachment.png",
          contentType: "image/png",
        }),
      },
    ];
  },
  task: () => "foo",
  scores: [],
});
