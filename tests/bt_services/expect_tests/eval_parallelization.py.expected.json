[{"context": {}, "expected": null, "id": "50ccdec5bc394f7ab778e71b18491ab855174c0f99966113dc40b91dcff3f668", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "50ccdec5bc394f7ab778e71b18491ab855174c0f99966113dc40b91dcff3f668", "span_parents": ["c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725"], "tags": null}, {"context": {}, "expected": null, "id": "50ccdec5bc394f7ab778e71b18491ab855174c0f99966113dc40b91dcff3f668", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "50ccdec5bc394f7ab778e71b18491ab855174c0f99966113dc40b91dcff3f668", "span_parents": ["c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725"], "tags": null}, {"context": {}, "expected": null, "id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "input": 0, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b3724271470e2ccbb5847b468a8f21fc4414f09878bbfeaf46c5c30345cd2a39", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b3724271470e2ccbb5847b468a8f21fc4414f09878bbfeaf46c5c30345cd2a39", "span_parents": ["0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28"], "tags": null}, {"context": {}, "expected": null, "id": "b3724271470e2ccbb5847b468a8f21fc4414f09878bbfeaf46c5c30345cd2a39", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b3724271470e2ccbb5847b468a8f21fc4414f09878bbfeaf46c5c30345cd2a39", "span_parents": ["0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28"], "tags": null}, {"context": {}, "expected": null, "id": "3482d936e8d739cea2ec48128399a0ee5f64f99fa8bfc81cdf37ebbd1c7ce5d2", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3482d936e8d739cea2ec48128399a0ee5f64f99fa8bfc81cdf37ebbd1c7ce5d2", "span_parents": ["b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903"], "tags": null}, {"context": {}, "expected": null, "id": "3482d936e8d739cea2ec48128399a0ee5f64f99fa8bfc81cdf37ebbd1c7ce5d2", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3482d936e8d739cea2ec48128399a0ee5f64f99fa8bfc81cdf37ebbd1c7ce5d2", "span_parents": ["b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903"], "tags": null}, {"context": {}, "expected": null, "id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 8, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8b10dfbdba7613a903a1003a2964daa6e47da1e592409196b328a1202fdd5cb9", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "8b10dfbdba7613a903a1003a2964daa6e47da1e592409196b328a1202fdd5cb9", "span_parents": ["1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102"], "tags": null}, {"context": {}, "expected": null, "id": "8b10dfbdba7613a903a1003a2964daa6e47da1e592409196b328a1202fdd5cb9", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 8, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "8b10dfbdba7613a903a1003a2964daa6e47da1e592409196b328a1202fdd5cb9", "span_parents": ["1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102"], "tags": null}, {"context": {}, "expected": null, "id": "74a18311df0f10f5cda3cc355fd0d4ae1cdf07beb34aee58fceda511c9b0abd4", "input": {"expected": null, "input": 0, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "74a18311df0f10f5cda3cc355fd0d4ae1cdf07beb34aee58fceda511c9b0abd4", "span_parents": ["c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725"], "tags": null}, {"context": {}, "expected": null, "id": "74a18311df0f10f5cda3cc355fd0d4ae1cdf07beb34aee58fceda511c9b0abd4", "input": {"expected": null, "input": 0, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "74a18311df0f10f5cda3cc355fd0d4ae1cdf07beb34aee58fceda511c9b0abd4", "span_parents": ["c66ad12cad471b028bd7bb8422e6b8a41f605b380348ca6bff071e2110c35725"], "tags": null}, {"context": {}, "expected": null, "id": "8e22ad6c6cdd47a512c163426a89773e6a56166702f79108763323b955d19761", "input": {"expected": null, "input": 1, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "8e22ad6c6cdd47a512c163426a89773e6a56166702f79108763323b955d19761", "span_parents": ["0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28"], "tags": null}, {"context": {}, "expected": null, "id": "8e22ad6c6cdd47a512c163426a89773e6a56166702f79108763323b955d19761", "input": {"expected": null, "input": 1, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "8e22ad6c6cdd47a512c163426a89773e6a56166702f79108763323b955d19761", "span_parents": ["0a2641dce824fb5a8d64df35aeb2f2340efdd35e773573294b06c6f278f08a28"], "tags": null}, {"context": {}, "expected": null, "id": "a8284865814823f0980082c5922a295f5b2b66a296cadb9250384331728051a3", "input": {"expected": null, "input": 2, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "a8284865814823f0980082c5922a295f5b2b66a296cadb9250384331728051a3", "span_parents": ["b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903"], "tags": null}, {"context": {}, "expected": null, "id": "a8284865814823f0980082c5922a295f5b2b66a296cadb9250384331728051a3", "input": {"expected": null, "input": 2, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "a8284865814823f0980082c5922a295f5b2b66a296cadb9250384331728051a3", "span_parents": ["b7e3ca32e155ac785039a6dc4a7861513198c6a11c4d539c472f6e392fb6c903"], "tags": null}, {"context": {}, "expected": null, "id": "223f5acf1626b746b8895142e508e8b8d021a2ea57407a169412af00e1f49d62", "input": {"expected": null, "input": 3, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "223f5acf1626b746b8895142e508e8b8d021a2ea57407a169412af00e1f49d62", "span_parents": ["1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102"], "tags": null}, {"context": {}, "expected": null, "id": "223f5acf1626b746b8895142e508e8b8d021a2ea57407a169412af00e1f49d62", "input": {"expected": null, "input": 3, "metadata": {}, "output": 8}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102", "scores": {"max_counter_scorer": 1}, "span_attributes": {"name": "max_counter_scorer", "type": "score"}, "span_id": "223f5acf1626b746b8895142e508e8b8d021a2ea57407a169412af00e1f49d62", "span_parents": ["1e8d3f401bf7a4b1cc727d841720f464362e36c59e14b886c2677cd6b201b102"], "tags": null}]