import { initLogger, traced } from "braintrust";

const logger = initLogger({
  projectName: "test project",
  asyncFlush: true,
});

function someLLM(input: string) {
  return traced(
    async (span) => {
      const output = input + " foo!";
      span.log({ input, output });
      return output;
    },
    {
      name: "some LLM",
    },
  );
}

function myRequestHandler(input: string) {
  return traced(
    async (span) => {
      const output = await someLLM(input);
      span.log({ input, output });
      return {
        result: output,
        requestId: await span.export(),
      };
    },
    {
      name: "request handler",
    },
  );
}

function myFeedbackHandler(requestId: string, score: number) {
  return traced(
    async (span) => {
      if (score < 0.5) {
        span.logFeedback({
          scores: { foo: score },
          comment: "This sucks!",
        });
      } else {
        span.log({ scores: { foo: score } });
        span.logFeedback({ comment: "This is great!" });
      }
    },
    {
      name: "feedback",
      parent: requestId,
    },
  );
}

async function main() {
  for (const flushBetween of [false, true]) {
    const response = await myRequestHand<PERSON>("hello");

    if (flushBetween) {
      await logger.flush();
    }

    for (let i = 0; i < 5; i++) {
      await myFeedbackHandler(response.requestId, i / 5);
    }
  }
}

main();
