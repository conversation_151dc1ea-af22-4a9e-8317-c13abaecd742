import { OpenAI } from "openai";
import { type ChatCompletionStreamParams } from "openai/lib/ChatCompletionStream";
import { wrapOpenAI, init, Experiment, Span } from "braintrust";

async function main() {
  function makeQuery(prompt_str: string): ChatCompletionStreamParams {
    return {
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant" },
        { role: "user", content: prompt_str },
      ],
    };
  }

  async function asyncQuery(experiment: Experiment) {
    const client = wrapOpenAI(new OpenAI());

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give me a list of ten children's toys";
        const resp = await client.beta.chat.completions.stream(
          makeQuery(PROMPT),
        );
        let numResponses = 0;
        for await (const _ of resp) {
          ++numResponses;
        }
        rootSpan.log({ input: PROMPT, output: { numResponses } });
      },
      { name: "async_query1" },
    );
  }

  const experiment = init("openai_wrapper_sdktrace");
  await asyncQuery(experiment);
  console.log(await experiment.summarize());
}

main();
