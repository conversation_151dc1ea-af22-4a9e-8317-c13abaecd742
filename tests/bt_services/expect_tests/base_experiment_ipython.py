"""
Test that `Eval()` and `await EvalAsync()` work in IPython, which has an
existing event loop.
"""

import subprocess


def main():
    subprocess.run(
        [
            "ipython",
            "-c",
            "import tests.bt_services.expect_tests.base_experiment as test; await test.main()",
        ],
        check=True,
    )

    subprocess.run(
        [
            "ipython",
            "-c",
            "import tests.bt_services.expect_tests.base_experiment_sync as test; test.main()",
        ],
        check=True,
    )


if __name__ == "__main__":
    main()
