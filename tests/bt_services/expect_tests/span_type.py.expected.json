[{"context": {}, "expected": null, "id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "scores": null, "span_attributes": {"name": "root", "type": "function"}, "span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "35029331cb0bcc8811fc24b282b6fc7cca06cc458cd41b96be1a654a4b410569", "input": {"message": "hello"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "scores": null, "span_attributes": {"name": "foo", "type": "llm"}, "span_id": "35029331cb0bcc8811fc24b282b6fc7cca06cc458cd41b96be1a654a4b410569", "span_parents": ["ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd"], "tags": null}]