[{"context": {}, "error": "<has_error>", "expected": "b", "id": "9e563526c8bce4d3edd63462bff6dd1faf8bebf56ec29fc16faa36419b4e5b20", "input": "a", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "9e563526c8bce4d3edd63462bff6dd1faf8bebf56ec29fc16faa36419b4e5b20", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "9e563526c8bce4d3edd63462bff6dd1faf8bebf56ec29fc16faa36419b4e5b20", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "c40ce34b05f7a24a68e8d0100836aea7f97f8c294c4370cfb73a85ee42329e5d", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "9e563526c8bce4d3edd63462bff6dd1faf8bebf56ec29fc16faa36419b4e5b20", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "c40ce34b05f7a24a68e8d0100836aea7f97f8c294c4370cfb73a85ee42329e5d", "span_parents": ["9e563526c8bce4d3edd63462bff6dd1faf8bebf56ec29fc16faa36419b4e5b20"], "tags": null}]