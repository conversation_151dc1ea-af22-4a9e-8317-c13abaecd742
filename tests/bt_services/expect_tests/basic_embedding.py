import asyncio

import braintrust
import openai

from autoevals import EmbeddingSimilarity, ListContains

client = braintrust.wrap_openai(openai.Client())
embedding = client.embeddings.create(model="text-embedding-3-small", input="apple")

braintrust.Eval(
    "embedding test",
    data=[{"input": "hi", "expected": "bonjour"}],
    task=lambda _input: "bounjorno",
    scores=[EmbeddingSimilarity],
)


async def eval_test():
    await braintrust.EvalAsync(
        "embedding test",
        data=[{"input": "hi", "expected": "bonjour"}],
        task=lambda _input: "bounjorno",
        scores=[EmbeddingSimilarity],
    )


asyncio.run(eval_test())

braintrust.Eval(
    "embedding list test",
    data=[{"input": "hi", "expected": ["bonjour"]}],
    task=lambda _input: ["bounjorno"],
    scores=[ListContains(pairwise_scorer=EmbeddingSimilarity)],
)
