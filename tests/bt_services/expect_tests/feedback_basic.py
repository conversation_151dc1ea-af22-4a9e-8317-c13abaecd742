import braintrust

if __name__ == "__main__":
    with braintrust._internal_with_custom_background_logger() as custom_bg_logger:
        logger = braintrust.init_logger("test project")

        # This should be logged in one audit entry
        # NOTE: Because of how we merge rows, we'll combine the two log calls into one row
        # and it'll appear the span is created by an external user. We could solve this properly
        # by having the SDK handle "merge barriers" and log them separately. However, we expect
        # this to rarely (if ever) occur.
        custom_bg_logger.sync_flush = True
        request_id = logger.log(**{"input": 1, "output": 1})
        logger.log_feedback(id=request_id, scores={"foo": 0.5}, comment="Foo bar bing")
        custom_bg_logger.sync_flush = False
        logger.flush()

        # This should be logged in two audit entries
        request_id = logger.log(**{"input": 1, "output": 2})
        logger.flush()
        logger.log_feedback(id=request_id, scores={"foo": 0.7}, comment="This is great!")
        logger.flush()

        # This should be logged in three audit entries
        request_id = logger.log(**{"input": 1, "output": 3})
        logger.flush()
        logger.log_feedback(id=request_id, scores={"foo": 1})
        logger.flush()
        logger.log_feedback(id=request_id, comment="You rock!")
        logger.flush()

        logger.log_feedback(id=request_id, expected=4)

        experiment = braintrust.init("test experiment")
        request_id = experiment.log(**{"input": 1, "output": 3, "scores": {}})
        experiment.log_feedback(id=request_id, scores={"foo": 0.5}, comment="Foo bar bing")
        print(experiment.summarize())
