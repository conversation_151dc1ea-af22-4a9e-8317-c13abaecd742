import { initDataset } from "braintrust";

console.log("Creating dataset");

async function main() {
  const dataset = initDataset("basic_dataset_ts");

  for (let i = 0; i < 10; i++) {
    dataset.insert({
      input: i,
      output: { result: i + 1, error: null },
      metadata: {
        foo: i % 2,
        bar: "baz",
      },
    });
  }

  await dataset.flush();
  for await (const data of dataset) {
    console.log(data);
  }

  const summary = await dataset.summarize();
  console.log("Summary: ", summary);
}

main();
