import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

async function main() {
  await <PERSON><PERSON>("p_js", {
    data: [{ input: "a", expected: "b" }],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    task: (x: any) => x,
    scores: [<PERSON><PERSON><PERSON><PERSON>],
    experimentName: "update_eval",
  });

  await <PERSON><PERSON>("p_js", {
    data: [{ input: "a", expected: "b" }],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    task: (x: any) => x,
    scores: [Levenshtein],
    experimentName: "update_eval",
    update: true,
  });

  await <PERSON><PERSON>("p_js", {
    data: [{ input: "a", expected: "b" }],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    task: (x: any) => x,
    scores: [<PERSON><PERSON><PERSON><PERSON>],
    experimentName: "update_eval",
  });
}
main();
