import { Eval, initDataset } from "braintrust";
import { NumericDiff } from "autoevals";

async function main() {
  const ds = initDataset("dataset_in_eval", { dataset: "data" });
  ds.insert({ input: { a: 1, b: 2 }, expected: 3 });
  ds.insert({ input: { a: 2, b: 2 }, expected: 4 });
  await ds.flush();

  await <PERSON><PERSON>("dataset_in_eval", {
    data: initDataset("dataset_in_eval", { dataset: "data" }),
    task: (input) => input.a + input.b,
    scores: [NumericDiff],
  });
}

main().catch(console.error);
