import os

import braintrust

os.environ["BRAINTRUST_SYNC_FLUSH"] = "1"

experiment = braintrust.init("p")

for flush_level in range(5):
    with experiment.start_span(name="s1") as span1:
        span1.log(input="one")
        if flush_level > 0:
            experiment.flush()
        with experiment.start_span(parent=span1.export(), name="s2") as span2:
            span2.log(input="two")
            if flush_level > 1:
                experiment.flush()
            with span2.start_span(name="s3") as span3:
                span3.log(input="three")
                if flush_level > 2:
                    experiment.flush()
                with experiment.start_span(parent=span3.export(), name="s4") as span4:
                    span4.log(input="four")
                    if flush_level > 3:
                        experiment.flush()
                with span3.start_span(name="s5") as span5:
                    span5.log(input="five")
                    if flush_level > 4:
                        experiment.flush()
print(experiment.summarize())
