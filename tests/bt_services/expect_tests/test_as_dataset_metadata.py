from braintrust.logger import ExperimentDatasetIterator


def main():
    """Test that as_dataset properly propagates metadata from experiment events."""

    # Mock experiment events with metadata
    mock_events = [
        # Root span with metadata
        {
            "id": "1",
            "_xact_id": "xact1",
            "root_span_id": "span1",
            "span_id": "span1",
            "input": {"question": "What is 2+2?"},
            "output": "4",
            "expected": "4",
            "tags": ["math", "simple"],
            "metadata": {"model": "gpt-4", "temperature": 0.5},
        },
        # Non-root span (should be filtered out)
        {
            "id": "2",
            "_xact_id": "xact2",
            "root_span_id": "span1",
            "span_id": "span2",
            "input": {"context": "Math problem"},
            "output": "Processing...",
        },
        # Root span without metadata
        {
            "id": "3",
            "_xact_id": "xact3",
            "root_span_id": "span3",
            "span_id": "span3",
            "input": {"question": "What is 5+5?"},
            "output": "10",
            "expected": None,
            "tags": ["math"],
        },
        # Root span with metadata but no expected
        {
            "id": "4",
            "_xact_id": "xact4",
            "root_span_id": "span4",
            "span_id": "span4",
            "input": {"question": "What is 3+3?"},
            "output": "6",
            "expected": None,
            "metadata": {"model": "gpt-3.5", "temperature": 0.7},
        },
    ]

    # Create iterator
    iterator = ExperimentDatasetIterator(iter(mock_events))

    # Collect results
    results = list(iterator)

    # Should have 3 results (only root spans)
    assert len(results) == 3, f"Expected 3 items, got {len(results)}"

    # Sort by input to ensure consistent ordering
    results.sort(key=lambda x: x["input"]["question"])

    # First result should have metadata
    assert results[0]["input"] == {"question": "What is 2+2?"}
    assert results[0]["expected"] == "4"
    assert results[0]["tags"] == ["math", "simple"]
    assert results[0].get("metadata") == {
        "model": "gpt-4",
        "temperature": 0.5,
    }, f"Expected metadata for first item, got {results[0].get('metadata')}"

    # Second result should have metadata
    assert results[1]["input"] == {"question": "What is 3+3?"}
    assert results[1]["expected"] == "6"  # Uses output since expected is None
    assert results[1].get("metadata") == {
        "model": "gpt-3.5",
        "temperature": 0.7,
    }, f"Expected metadata for second item, got {results[1].get('metadata')}"

    # Third result should not have metadata (it wasn't in the original event)
    assert results[2]["input"] == {"question": "What is 5+5?"}
    assert results[2]["expected"] == "10"  # Uses output since expected is None
    assert results[2]["tags"] == ["math"]
    assert results[2].get("metadata") is None, f"Expected no metadata for third item, got {results[2].get('metadata')}"

    print("✅ All as_dataset metadata propagation tests passed!")


if __name__ == "__main__":
    main()
