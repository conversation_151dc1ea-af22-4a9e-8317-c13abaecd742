[{"context": {}, "error": "<has_error>", "expected": null, "id": "9dfb044075ee3368412995142809525fac2b8e9474691ec9b8bbd96a61bd1f32", "input": "error_scoring_no_scores", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "dd557efb083c32f279c0f73c6dd5eb2e950d784f6c2fc1fdbc31221918b4598f", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "9dfb044075ee3368412995142809525fac2b8e9474691ec9b8bbd96a61bd1f32", "span_parents": ["dd557efb083c32f279c0f73c6dd5eb2e950d784f6c2fc1fdbc31221918b4598f"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "dd557efb083c32f279c0f73c6dd5eb2e950d784f6c2fc1fdbc31221918b4598f", "input": "error_scoring_no_scores", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "dd557efb083c32f279c0f73c6dd5eb2e950d784f6c2fc1fdbc31221918b4598f", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dd557efb083c32f279c0f73c6dd5eb2e950d784f6c2fc1fdbc31221918b4598f", "span_parents": null, "tags": null}]