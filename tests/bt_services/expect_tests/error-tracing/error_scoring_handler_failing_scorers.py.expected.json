[{"context": {}, "expected": null, "id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "input": "error_scoring_failing_scorers", "is_root": true, "metadata": {"scorer_errors": {"will_error_scorer": ""}}, "metrics": {}, "origin": null, "output": "error_scoring_failing_scorers", "root_span_id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "scores": {"will_error_scorer": 0}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "771a4479241e7fce17c51b72dd1f0f3e646550ce79b9e665ec7271ec5202a018", "input": "error_scoring_failing_scorers", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "error_scoring_failing_scorers", "root_span_id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "771a4479241e7fce17c51b72dd1f0f3e646550ce79b9e665ec7271ec5202a018", "span_parents": ["45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "1d8275fb5a3c8b2e535c63fb988810555adb56dcd5ad253bbe2b716a9ee72e6e", "input": {"expected": null, "input": "error_scoring_failing_scorers", "metadata": {}, "output": "error_scoring_failing_scorers"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "scores": null, "span_attributes": {"name": "will_error_scorer", "type": "score"}, "span_id": "1d8275fb5a3c8b2e535c63fb988810555adb56dcd5ad253bbe2b716a9ee72e6e", "span_parents": ["45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a"], "tags": null}, {"context": {}, "expected": null, "id": "20afd550e8c351333ee0bddcdeb8fe66a63cdc838c46b9926a5cd421d6e5d199", "input": {"expected": null, "input": "error_scoring_failing_scorers", "metadata": {}, "output": "error_scoring_failing_scorers"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a", "scores": {"will_not_error_scorer": 1}, "span_attributes": {"name": "will_not_error_scorer", "type": "score"}, "span_id": "20afd550e8c351333ee0bddcdeb8fe66a63cdc838c46b9926a5cd421d6e5d199", "span_parents": ["45609937a103f8642fc2f18e6fad65ed5fbc90929d38e761e0560d5fa46c9c3a"], "tags": null}]