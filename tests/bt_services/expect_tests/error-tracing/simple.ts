import { init, initLogger, wrapTraced } from "braintrust";

const logger = initLogger({ projectName: "errors" });

const willFail = wrapTraced(async function willFail() {
  throw new Error("I will fail");
});

const main = wrapTraced(async function main() {
  await willFail();
});

async function run() {
  try {
    await main();
  } catch (e) {
    console.error("ERROR", e);
    // pass
  }

  try {
    await logger.traced(async (span) => {
      await willFail();
    });
  } catch (e) {
    console.error("ERROR", e);
    // pass
  }

  const experiment = init("errors");
  try {
    await experiment.traced(async () => {
      await willFail();
    });
  } catch (e) {
    console.error("ERROR", e);
    // pass
  }
}

run();
