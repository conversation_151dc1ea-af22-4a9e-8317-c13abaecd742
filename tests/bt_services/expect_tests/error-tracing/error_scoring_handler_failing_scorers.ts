import { Eval, defaultErrorScoreHand<PERSON> } from "braintrust";

function willNotErrorScorer() {
  return {
    name: "will_not_error_scorer",
    score: 1,
  };
}

function willErrorScorer() {
  throw new Error("test error");
  return 1;
}

Eval("basic", {
  data: [{ input: "error_scoring_failing_scorers" }],
  task: async () => {
    throw new Error("test error");
  },
  scores: [willNotErrorScorer, willErrorScorer],
  errorScoreHandler: defaultErrorScoreHandler,
});
