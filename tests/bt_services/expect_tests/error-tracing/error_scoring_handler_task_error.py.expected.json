[{"context": {}, "error": "<has_error>", "expected": null, "id": "86d26bb404e84fe856481358d35fe30585ea2b8614ce37b214c988dd64a03b93", "input": "error_scoring_task_error", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "86d26bb404e84fe856481358d35fe30585ea2b8614ce37b214c988dd64a03b93", "scores": {"logged_1_scorer": 0, "logged_2_scorer": 0}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "86d26bb404e84fe856481358d35fe30585ea2b8614ce37b214c988dd64a03b93", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "86d26bb404e84fe856481358d35fe30585ea2b8614ce37b214c988dd64a03b93", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["86d26bb404e84fe856481358d35fe30585ea2b8614ce37b214c988dd64a03b93"], "tags": null}]