[{"context": {}, "error": "<has_error>", "expected": null, "id": "49b5e43c7e8db6bea7b7e9bc4169b2c8578e7b72e5fdf77f20292bbc87f61c8c", "input": "error_scoring_noop", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "49b5e43c7e8db6bea7b7e9bc4169b2c8578e7b72e5fdf77f20292bbc87f61c8c", "span_parents": ["723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "input": "error_scoring_noop", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "span_parents": null, "tags": null}]