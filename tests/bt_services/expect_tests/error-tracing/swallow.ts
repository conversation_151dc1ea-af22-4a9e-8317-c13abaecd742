import { initLogger, wrapTraced } from "braintrust";

initLogger({ projectName: "errors" });

const willFail = wrapTraced(async function willFail() {
  throw new Error("I will fail (swallow)");
});

const callWillFail = wrapTraced(async function callWillFail() {
  await willFail();
});

const main = wrapTraced(async function main() {
  try {
    await callWillFail();
  } catch {
    // pass
  }
});

main();
