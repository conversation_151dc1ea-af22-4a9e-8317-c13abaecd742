[{"context": {}, "error": "<has_error>", "expected": null, "id": "630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "scores": null, "span_attributes": {"name": "callWillFail", "type": "function"}, "span_id": "630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f", "span_parents": ["d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "scores": null, "span_attributes": {"name": "will<PERSON>ail", "type": "function"}, "span_id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "span_parents": ["630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "input": [], "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "span_parents": null, "tags": null}]