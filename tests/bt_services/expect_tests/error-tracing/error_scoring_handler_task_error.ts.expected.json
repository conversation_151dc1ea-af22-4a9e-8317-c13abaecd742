[{"context": {}, "error": "<has_error>", "expected": null, "id": "0095d6391a6b1112e3511651b1711041da7cbd38eb3b0e728d91d654d12668f1", "input": "error_scoring_task_error", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "05fbcc1e6c0de04ef3a79a65a2bea3135dc88cdb51776b70de85b672ea5234f7", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "0095d6391a6b1112e3511651b1711041da7cbd38eb3b0e728d91d654d12668f1", "span_parents": ["05fbcc1e6c0de04ef3a79a65a2bea3135dc88cdb51776b70de85b672ea5234f7"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "05fbcc1e6c0de04ef3a79a65a2bea3135dc88cdb51776b70de85b672ea5234f7", "input": "error_scoring_task_error", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "05fbcc1e6c0de04ef3a79a65a2bea3135dc88cdb51776b70de85b672ea5234f7", "scores": {"myScorer1": 0, "myScorer2": 0}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "05fbcc1e6c0de04ef3a79a65a2bea3135dc88cdb51776b70de85b672ea5234f7", "span_parents": null, "tags": null}]