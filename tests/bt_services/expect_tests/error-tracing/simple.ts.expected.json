[{"context": {}, "error": "<has_error>", "expected": null, "id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "scores": null, "span_attributes": {"name": "will<PERSON>ail", "type": "function"}, "span_id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "span_parents": ["4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ba8ae937254d8a609d48b1d411f081d34ffedb6eef58af8bfa8a78f22b74cf85", "scores": null, "span_attributes": {"name": "will<PERSON>ail", "type": "function"}, "span_id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "span_parents": ["ba8ae937254d8a609d48b1d411f081d34ffedb6eef58af8bfa8a78f22b74cf85"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "scores": null, "span_attributes": {"name": "will<PERSON>ail", "type": "function"}, "span_id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "span_parents": ["d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "input": [], "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "d3265111473d47318ebdd06cc19d62fc4473d1eee8b7fb416689d7129069f10f", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "ba8ae937254d8a609d48b1d411f081d34ffedb6eef58af8bfa8a78f22b74cf85", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ba8ae937254d8a609d48b1d411f081d34ffedb6eef58af8bfa8a78f22b74cf85", "scores": null, "span_attributes": {"name": "root", "type": "eval"}, "span_id": "ba8ae937254d8a609d48b1d411f081d34ffedb6eef58af8bfa8a78f22b74cf85", "span_parents": null, "tags": null}]