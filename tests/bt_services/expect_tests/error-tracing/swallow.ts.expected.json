[{"context": {}, "error": "<has_error>", "expected": null, "id": "630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5", "scores": null, "span_attributes": {"name": "callWillFail", "type": "function"}, "span_id": "630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f", "span_parents": ["7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "input": [], "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5", "scores": null, "span_attributes": {"name": "will<PERSON>ail", "type": "function"}, "span_id": "77baa2fa7373467aced08a07689ea745965d9a0e01d68dfbf2050cc6d8fba344", "span_parents": ["630c9c54f5d318f2bcea68180d38c14faa298827af696458c3c7bd3f3e3e4c4f"], "tags": null}, {"context": {}, "expected": null, "id": "7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5", "input": [], "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "7e18acf376f3f790681bd868db26ced317824df83ed06e58251e62825b1ed8a5", "span_parents": null, "tags": null}]