from braintrust import <PERSON><PERSON>


def error_task(input):
    raise Exception("test error")


def arbitrary_score_handler(root_span, data, unhandled_scores):
    root_span.log(scores={"error_score": 1})
    return None


def not_logged_scorer():
    return 1


if __name__ == "__main__":
    Eval(
        name="error_scoring_arbitrary_score",
        data=[{"input": "error_scoring_arbitrary_score"}],
        task=error_task,
        error_score_handler=arbitrary_score_handler,
        scores=[not_logged_scorer],
    )
