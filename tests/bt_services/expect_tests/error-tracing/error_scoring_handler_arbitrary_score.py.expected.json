[{"context": {}, "error": "<has_error>", "expected": null, "id": "318e59b13e4f15a596259542eed0f7f4035129d2664dc991a6df2c020fbf7753", "input": "error_scoring_arbitrary_score", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "318e59b13e4f15a596259542eed0f7f4035129d2664dc991a6df2c020fbf7753", "scores": {"error_score": 1}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "318e59b13e4f15a596259542eed0f7f4035129d2664dc991a6df2c020fbf7753", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "318e59b13e4f15a596259542eed0f7f4035129d2664dc991a6df2c020fbf7753", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["318e59b13e4f15a596259542eed0f7f4035129d2664dc991a6df2c020fbf7753"], "tags": null}]