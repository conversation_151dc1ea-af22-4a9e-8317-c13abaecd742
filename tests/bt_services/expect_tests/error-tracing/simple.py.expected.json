[{"context": {}, "error": "<has_error>", "expected": null, "id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "scores": null, "span_attributes": {"name": "will_fail", "type": "function"}, "span_id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "span_parents": ["df86d0614f5f4eebe26ff27606578bbcffdabd5d88c82a62e9c245e2d6f03412"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "df86d0614f5f4eebe26ff27606578bbcffdabd5d88c82a62e9c245e2d6f03412", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "df86d0614f5f4eebe26ff27606578bbcffdabd5d88c82a62e9c245e2d6f03412", "span_parents": ["4dcfc16de2131294aa8eecbb663b3b77299b4455086a0ad6ee573b5206f6899e"], "tags": null}]