[{"context": {}, "error": "<has_error>", "expected": null, "id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "input": "error_scoring_noop", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a373001acd437863dd170a09d1a2684a114d001e2412c91565d40e759986f783", "span_parents": ["723f2e16eb647426353acfc48d2c30dfe089445a99f18d7c9200a8eeab3e5d80"], "tags": null}]