from braintrust import Eva<PERSON>, framework


def error_task(input):
    raise Exception("test error")


def will_not_error_scorer():
    return 1


def will_error_scorer():
    raise Exception("test error")


if __name__ == "__main__":
    Eval(
        name="error_scoring_failing_scorers",
        data=[{"input": "error_scoring_failing_scorers"}],
        task=lambda input: input,
        error_score_handler=framework.default_error_score_handler,
        scores=[will_not_error_scorer, will_error_scorer],
    )
