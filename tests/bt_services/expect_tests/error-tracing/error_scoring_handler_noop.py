from braintrust import <PERSON><PERSON>


def error_task(input):
    raise Exception("test error")


def not_logged_scorer():
    return 1


if __name__ == "__main__":
    Eval(
        name="error_scoring_noop",
        data=[{"input": "error_scoring_noop"}],
        task=error_task,
        error_score_handler=lambda root_span, data, unhandled_scores: None,
        scores=[not_logged_scorer],
    )
