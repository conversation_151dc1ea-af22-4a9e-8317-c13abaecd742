import { initLogger, wrapTraced } from "braintrust";

initLogger({ projectName: "errors" });

const willFail = wrapTraced(async function willFail() {
  throw new Error("I will fail");
});

const callWillFail = wrapTraced(async function callWillFail() {
  await willFail();
});

const main = wrapTraced(async function main() {
  await callWillFail();
});

async function run() {
  try {
    await main();
  } catch {
    // pass
  }
}

run();
