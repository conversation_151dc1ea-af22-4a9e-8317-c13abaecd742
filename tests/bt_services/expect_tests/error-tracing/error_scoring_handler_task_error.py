from braintrust import Eval, framework


def error_task(input):
    raise Exception("test error")


def logged_1_scorer():
    return 1


def logged_2_scorer():
    return 1


if __name__ == "__main__":
    Eval(
        name="error_scoring_task_error",
        data=[{"input": "error_scoring_task_error"}],
        task=error_task,
        error_score_handler=framework.default_error_score_handler,
        scores=[logged_1_scorer, logged_2_scorer],
    )
