[{"context": {}, "error": "<has_error>", "expected": null, "id": "040f135a72bd327989ad438d0dc5597c0c7b8608ba498b0ba5ccb104f9fbff76", "input": "error_scoring_arbitrary_score", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "909cef670b227ccc8829854dd1e4ddecc5be9e9c5edb438fab94cb6588faba68", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "040f135a72bd327989ad438d0dc5597c0c7b8608ba498b0ba5ccb104f9fbff76", "span_parents": ["909cef670b227ccc8829854dd1e4ddecc5be9e9c5edb438fab94cb6588faba68"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "909cef670b227ccc8829854dd1e4ddecc5be9e9c5edb438fab94cb6588faba68", "input": "error_scoring_arbitrary_score", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "909cef670b227ccc8829854dd1e4ddecc5be9e9c5edb438fab94cb6588faba68", "scores": {"custom_score": 1}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "909cef670b227ccc8829854dd1e4ddecc5be9e9c5edb438fab94cb6588faba68", "span_parents": null, "tags": null}]