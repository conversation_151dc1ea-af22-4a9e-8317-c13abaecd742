import { Eval, defaultError<PERSON>coreHand<PERSON> } from "braintrust";

function myScorer1() {
  return {
    name: "not_logged_scorer1",
    score: 1,
  };
}

function myScorer2() {
  return {
    name: "not_logged_scorer2",
    score: 1,
  };
}

Eval("basic", {
  data: [{ input: "error_scoring_task_error" }],
  task: async () => {
    throw new Error("test error");
  },
  scores: [myScorer1, myScorer2],
  errorScoreHandler: defaultErrorScoreHandler,
});
