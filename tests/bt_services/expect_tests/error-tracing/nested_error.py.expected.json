[{"context": {}, "error": "<has_error>", "expected": null, "id": "3d3bc798ef55ca3b95810ec44814f78c4040e206c23ebca2861799742d8401e5", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755", "scores": null, "span_attributes": {"name": "call_will_fail", "type": "function"}, "span_id": "3d3bc798ef55ca3b95810ec44814f78c4040e206c23ebca2861799742d8401e5", "span_parents": ["a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755", "input": {}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755", "span_parents": null, "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "a5fb86dc934fd684d11f0ee8b2bf92a88881203f10a20f6acc73f4e3c5858755", "scores": null, "span_attributes": {"name": "will_fail", "type": "function"}, "span_id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "span_parents": ["3d3bc798ef55ca3b95810ec44814f78c4040e206c23ebca2861799742d8401e5"], "tags": null}]