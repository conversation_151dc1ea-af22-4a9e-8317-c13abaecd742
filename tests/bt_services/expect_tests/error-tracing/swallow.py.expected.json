[{"context": {}, "error": "<has_error>", "expected": null, "id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3", "scores": null, "span_attributes": {"name": "will_fail", "type": "function"}, "span_id": "bf9005633599f41f3b828c72abfd937603eb6fb03d07ffa77fef9dcc5f41a5a9", "span_parents": ["92662cc2632ec78f3036ef292a387c4dae0f43b9a011789de5e43b947bbfc110"], "tags": null}, {"context": {}, "expected": null, "id": "92662cc2632ec78f3036ef292a387c4dae0f43b9a011789de5e43b947bbfc110", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 5, "root_span_id": "bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3", "scores": null, "span_attributes": {"name": "call_will_fail", "type": "function"}, "span_id": "92662cc2632ec78f3036ef292a387c4dae0f43b9a011789de5e43b947bbfc110", "span_parents": ["bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3"], "tags": null}, {"context": {}, "expected": null, "id": "bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3", "input": {}, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3", "scores": null, "span_attributes": {"name": "main", "type": "function"}, "span_id": "bff38889556fa19fe101f3e0e4c754152a030e79b252339ffd0e94c9fcff83a3", "span_parents": null, "tags": null}]