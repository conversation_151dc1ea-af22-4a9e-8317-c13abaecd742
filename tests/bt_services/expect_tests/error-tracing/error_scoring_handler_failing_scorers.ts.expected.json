[{"context": {}, "error": "<has_error>", "expected": null, "id": "164c5c254da0212d4126ed7b37069ddf0d5ca27f20cc222b148ad4bd26fc1b9a", "input": "error_scoring_failing_scorers", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "885f6438eed56bbe64bf39faceaa53da8e5585fae6d85d738741605b1dfac8cf", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "164c5c254da0212d4126ed7b37069ddf0d5ca27f20cc222b148ad4bd26fc1b9a", "span_parents": ["885f6438eed56bbe64bf39faceaa53da8e5585fae6d85d738741605b1dfac8cf"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "885f6438eed56bbe64bf39faceaa53da8e5585fae6d85d738741605b1dfac8cf", "input": "error_scoring_failing_scorers", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "885f6438eed56bbe64bf39faceaa53da8e5585fae6d85d738741605b1dfac8cf", "scores": {"willErrorScorer": 0, "willNotErrorScorer": 0}, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "885f6438eed56bbe64bf39faceaa53da8e5585fae6d85d738741605b1dfac8cf", "span_parents": null, "tags": null}]