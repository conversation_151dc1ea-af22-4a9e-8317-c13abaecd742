[{"context": {}, "expected": "b", "id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "45bc81f9b762543a427cef21b2a8f8ab257ae62b17045966f9aa5df5c443cc1a", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": null}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"null scorer": null}, "span_attributes": {"name": "scorer", "type": "score"}, "span_id": "45bc81f9b762543a427cef21b2a8f8ab257ae62b17045966f9aa5df5c443cc1a", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}]