import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { Eval, wrapTraced } from "braintrust";
import { OpenAI } from "openai";

const client = new OpenAI();

const fooT = wrapTraced(async function foo(input) {
  const resp = await client.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: input }],
  });
  return resp.choices[0].message.content ?? "unknown";
});

Eval("trace wrapper", {
  data: [{ input: "1+1", expected: "2" }],
  task: async (input) => fooT(input),
  scores: [Lev<PERSON>htein],
  experimentName: "lambda",
});

Eval("trace wrapper", {
  data: [{ input: "1+1", expected: "2" }],
  task: fooT,
  scores: [Lev<PERSON>htein],
  experimentName: "trace",
});

const explicitInput = wrapTraced(
  async function foo(input) {
    return input;
  },
  {
    event: {
      input: "my input",
      output: "my output",
    },
  },
);

// This should log "my input" and "my output"
Eval("trace wrapper", {
  data: [{ input: "1+1", expected: "2" }],
  task: explicitInput,
  scores: [<PERSON><PERSON><PERSON><PERSON>],
  experimentName: "explicit input",
});
