from braintrust import Eval
from pydantic import BaseModel

from autoevals import Summary


class Input(BaseModel):
    text: str


Eval(
    "autoevals-object-inputs",
    data=[
        {
            "input": Input(text="What's the capital of France?"),
        },
        {
            "input": Input(text="Who wrote <PERSON> and Juliet?"),
        },
    ],
    task=lambda x: x,
    scores=[Summary],
)
