import * as braintrust from "braintrust";

process.env.BRAINTRUST_APP_URL = undefined;

function foo(a: number) {
  return braintrust.traced(
    () => {
      const output = a + 1;
      braintrust.currentSpan().log({
        input: { a },
        output,
      });
      return output;
    },
    { asyncFlush: true },
  );
}

const logger = braintrust.initLogger({ asyncFlush: true });

async function main() {
  const startTime = Date.now();
  for (let i = 0; i < 10; i++) {
    console.log(foo(i));
  }
  console.log(`Took ${Date.now() - startTime} milliseconds to run foo`);
  await logger.flush();
}

main();
