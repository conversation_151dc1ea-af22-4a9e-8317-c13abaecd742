import asyncio

from braintrust import BaseExperiment, EvalAsync

PROJECT_NAME = "Base experiment test"


async def main():
    base = await EvalAsync(
        PROJECT_NAME,
        data=[{"input": "a"}],
        task=lambda input: f"foo = {input}",
        scores=[],
    )
    base_experiment_name, base_experiment_id = (
        base.summary.experiment_name,
        base.summary.experiment_id,
    )

    using_base_name = await EvalAsync(
        PROJECT_NAME,
        data=[{"input": "a"}],
        task=lambda input: f"foo = {input}",
        scores=[],
        base_experiment_name=base_experiment_name,
    )
    assert using_base_name.summary.comparison_experiment_name == base_experiment_name

    using_base_id = await EvalAsync(
        PROJECT_NAME,
        data=[{"input": "a"}],
        task=lambda input: f"foo = {input}",
        scores=[],
        base_experiment_name=using_base_name.summary.experiment_name,
        base_experiment_id=base_experiment_id,
    )
    assert using_base_id.summary.comparison_experiment_name == base_experiment_name

    using_base_name_with_data = await <PERSON>lAsync(
        PROJECT_NAME,
        task=lambda input: f"foo = {input}",
        scores=[],
        data=BaseExperiment(name=using_base_id.summary.experiment_name),
        base_experiment_name=base_experiment_name,
    )
    assert using_base_name_with_data.summary.comparison_experiment_name == base_experiment_name

    using_base_id_with_data = await EvalAsync(
        PROJECT_NAME,
        data=BaseExperiment(name=using_base_id.summary.experiment_name),
        task=lambda input: f"foo = {input}",
        scores=[],
        base_experiment_id=base_experiment_id,
    )
    assert using_base_id_with_data.summary.comparison_experiment_name == base_experiment_name


if __name__ == "__main__":
    asyncio.run(main())
