import { initLogger } from "braintrust";

const backgroundPromises: Promise<void>[] = [];
// @ts-ignore
globalThis[Symbol.for("@vercel/request-context")] = {
  get: () => ({
    waitUntil: (promise: Promise<void>) => {
      backgroundPromises.push(promise);
    },
  }),
};

async function main() {
  // waitUntil runs whether or not asyncFlush is true
  const loggerNoAsyncFlush = initLogger({
    projectName: "no-async-flush",
    asyncFlush: false,
  });

  await loggerNoAsyncFlush.log({
    input: "1",
  });

  // Typescript tries its best to predict the length of backgroundPromises
  const len = () => backgroundPromises.length;

  if (len() !== 1) {
    throw new Error(`backgroundPromises should be 1 (${len()})`);
  }

  await loggerNoAsyncFlush.traced(async (span) => {
    span.log({ input: "2" });
  });

  if (len() !== 2) {
    throw new Error(`backgroundPromises should be 2 (${len()})`);
  }

  // Now create an async flush logger
  const loggerAsyncFlush = initLogger({
    projectName: "async-flush",
    asyncFlush: true,
  });

  loggerAsyncFlush.log({ input: "3" });

  // For some reason, Typescript believes this will never be set
  if (len() < 3) {
    throw new Error(`backgroundPromises should be >= 3 (${len()})`);
  }

  loggerAsyncFlush.traced(async (span) => {
    span.log({ input: "4" });
  });

  // For some reason, Typescript believes this will never be set
  if (len() < 3) {
    throw new Error(`backgroundPromises should be >= 3 (${len()})`);
  }

  await loggerNoAsyncFlush.flush();

  loggerAsyncFlush.traced(async (span) => {
    span.log({ input: "4" });
  });

  if (len() < 4) {
    throw new Error(`backgroundPromises should be >= 4 (${len()})`);
  }

  await Promise.all(backgroundPromises);
}

main();
