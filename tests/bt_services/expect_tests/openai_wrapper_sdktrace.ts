import { OpenAI } from "openai";
import { wrapOpenAI, init, Experiment, Span } from "braintrust";

type OptSpanInfo = {
  span_info?: unknown;
};

async function main() {
  function makeQuery(
    prompt_str: string,
  ): OpenAI.Chat.ChatCompletionCreateParamsNonStreaming & OptSpanInfo {
    return {
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are a helpful assistant" },
        { role: "user", content: prompt_str },
      ],
      span_info: {
        name: "chat completion query",
        span_attributes: { extra: "hello" },
      },
    };
  }

  async function asyncQuery(experiment: Experiment) {
    const client = wrapOpenAI(new OpenAI());

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give me a list of ten children's toys";
        const resp = await client.chat.completions.create(makeQuery(PROMPT));
        rootSpan.log({ input: PROMPT, output: resp.choices[0] });
      },
      { name: "async_query0" },
    );

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give a random number between 1 and 2000";
        const resp = await client.chat.completions.create({
          ...makeQuery(PROMPT),
          stream: true,
        });
        let numResponses = 0;
        for await (const _ of resp) {
          ++numResponses;
        }
        rootSpan.log({ input: PROMPT, output: { numResponses } });
      },
      { name: "async_query1" },
    );

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give me a list of ten children's toys";
        const resp = await client.chat.completions.create({
          ...makeQuery(PROMPT),
          tools: [
            {
              type: "function",
              function: {
                description: "Returns the sum of two numbers.",
                name: "add",
                parameters: {
                  type: "object",
                  properties: {
                    a: { type: "number", description: "The first number" },
                    b: { type: "number", description: "The second number" },
                  },
                  required: ["a", "b"],
                },
              },
            },
          ],
        });
        rootSpan.log({ input: PROMPT, output: resp.choices[0] });
      },
      { name: "function_call_nonstreaming" },
    );

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give a random number between 1 and 2000";
        const resp = await client.chat.completions.create({
          ...makeQuery(PROMPT),
          stream: true,
          tools: [
            {
              type: "function",
              function: {
                description: "Returns the sum of two numbers.",
                name: "add",
                parameters: {
                  type: "object",
                  properties: {
                    a: { type: "number", description: "The first number" },
                    b: { type: "number", description: "The second number" },
                  },
                  required: ["a", "b"],
                },
              },
            },
          ],
        });
        let numResponses = 0;
        for await (const _ of resp) {
          ++numResponses;
        }
        rootSpan.log({ input: PROMPT, output: { numResponses } });
      },
      { name: "function_call_streaming" },
    );

    await client.chat.completions.create({
      ...makeQuery("Give me all your money"),
    });
  }

  const experiment = init("openai_wrapper_nonproxy");
  await asyncQuery(experiment);
  console.log(await experiment.summarize());
}

main();
