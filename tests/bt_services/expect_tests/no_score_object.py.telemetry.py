from tests.helpers.datetime import iso8601
from tests.helpers.number import number
from tests.helpers.telemetry import ctx, idempotency


def task():
    return {
        "individual": (
            6
            * [
                {
                    "event_name": "LogInsertedEvent",
                    "external_customer_id": ctx("org_id"),
                    "idempotency_key": idempotency(),
                    "properties": {
                        "log_bytes": number(),
                        "org_id": ctx("org_id"),
                        "scores_count": number(),
                        "metrics_count": number(),
                        "type": "individual",
                    },
                    "timestamp": iso8601(),
                },
            ]
        ),
        "aggregated": [
            {
                "event_name": "LogInsertedEvent",
                "idempotency_key": idempotency(),
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "log_bytes": number(gte=1),
                    "metrics_count": number(eq=0),
                    "scores_count": number(gte=2),
                    "type": "aggregated",
                    "count": number(gte=1),
                },
                "timestamp": iso8601(),
            },
        ],
    }
