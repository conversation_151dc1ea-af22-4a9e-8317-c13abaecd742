[{"context": {}, "expected": null, "id": "7a6f1cf6b31bdc02ddba99e30affa79868c0bc6f071c9a8c8073cc9e8dc7ddac", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"num_responses": 21}, "root_span_id": "7a6f1cf6b31bdc02ddba99e30affa79868c0bc6f071c9a8c8073cc9e8dc7ddac", "scores": null, "span_attributes": {"name": "sync_query1", "type": "eval"}, "span_id": "7a6f1cf6b31bdc02ddba99e30affa79868c0bc6f071c9a8c8073cc9e8dc7ddac", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "9687c7c20955c619839396e6acba3f2f0d710da8625f06b5d199a82f26be8af9", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"num_responses": 21}, "root_span_id": "9687c7c20955c619839396e6acba3f2f0d710da8625f06b5d199a82f26be8af9", "scores": null, "span_attributes": {"name": "async_query1", "type": "eval"}, "span_id": "9687c7c20955c619839396e6acba3f2f0d710da8625f06b5d199a82f26be8af9", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "bf2dff408124ffe4b5d8fe62171c904711419e953fd1b9d01076e12ca40f9c25", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Sure! Here is a list of twelve baby names:\n\n1. <PERSON>\n2. <PERSON>\n3. <PERSON>\n4. <PERSON>\n5. <PERSON>\n6. <PERSON>\n7. <PERSON>\n8. <PERSON>\n9. <PERSON>\n10. <PERSON>\n11. <PERSON>\n12. <PERSON>\n\nI hope you find these names helpful! Let me know if you need more suggestions.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}, "root_span_id": "bf2dff408124ffe4b5d8fe62171c904711419e953fd1b9d01076e12ca40f9c25", "scores": null, "span_attributes": {"name": "function_call_nonstreaming", "type": "eval"}, "span_id": "bf2dff408124ffe4b5d8fe62171c904711419e953fd1b9d01076e12ca40f9c25", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "f2db831129b8fb378e5fb2d9fc71947157b9b4af48ecffbea1b9864a170c9206", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"num_responses": 12}, "root_span_id": "f2db831129b8fb378e5fb2d9fc71947157b9b4af48ecffbea1b9864a170c9206", "scores": null, "span_attributes": {"name": "function_call_streaming", "type": "eval"}, "span_id": "f2db831129b8fb378e5fb2d9fc71947157b9b4af48ecffbea1b9864a170c9206", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "8a0d4f54f7002c8c898a1cfb11b4c3b5036c81b54bff0d8e7773ac51634db2d6", "input": "Give me a list of ten children's toys", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "1. LEGO building sets\n2. Barbie dolls\n3. Hot Wheels cars\n4. Play-Doh\n5. Crayola crayons\n6. Furby interactive toy\n7. Nerf blasters\n8. Baby Alive doll\n9. Paw Patrol figurines\n10. Playmobil playsets", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}, "root_span_id": "8a0d4f54f7002c8c898a1cfb11b4c3b5036c81b54bff0d8e7773ac51634db2d6", "scores": null, "span_attributes": {"name": "sync_query0", "type": "eval"}, "span_id": "8a0d4f54f7002c8c898a1cfb11b4c3b5036c81b54bff0d8e7773ac51634db2d6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b82f5d16fe09fcb5d969cde1000bcb197c5f12d40c837170bd3b810e229b48c6", "input": "Give me a list of twelve baby names", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Sure! Here is a list of twelve baby names:\n\n1. <PERSON>\n2. <PERSON>\n3. <PERSON>\n4. <PERSON>\n5. <PERSON>\n6. <PERSON>\n7. <PERSON>\n8. <PERSON>\n9. <PERSON>\n10. <PERSON>\n11. <PERSON>\n12. <PERSON>\n\nI hope you find these names helpful! Let me know if you need more suggestions.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}, "root_span_id": "b82f5d16fe09fcb5d969cde1000bcb197c5f12d40c837170bd3b810e229b48c6", "scores": null, "span_attributes": {"name": "async_query0", "type": "eval"}, "span_id": "b82f5d16fe09fcb5d969cde1000bcb197c5f12d40c837170bd3b810e229b48c6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "628fd69b82a10e9ea84f8a6531767fa7b98a6d3035f8149ad2acae08e0ee3678", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "stream": true}, "metrics": {}, "origin": null, "output": [{"finish_reason": null, "index": 0, "logprobs": null, "message": {"content": "Sure! Here is a random number between 1 and 2000: 1378.", "role": "assistant", "tool_calls": null}}], "root_span_id": "7a6f1cf6b31bdc02ddba99e30affa79868c0bc6f071c9a8c8073cc9e8dc7ddac", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "628fd69b82a10e9ea84f8a6531767fa7b98a6d3035f8149ad2acae08e0ee3678", "span_parents": ["7a6f1cf6b31bdc02ddba99e30affa79868c0bc6f071c9a8c8073cc9e8dc7ddac"], "tags": null}, {"context": {}, "expected": null, "id": "628fd69b82a10e9ea84f8a6531767fa7b98a6d3035f8149ad2acae08e0ee3678", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "stream": true}, "metrics": {}, "origin": null, "output": [{"finish_reason": null, "index": 0, "logprobs": null, "message": {"content": "Sure! Here is a random number between 1 and 2000: 1378.", "role": "assistant", "tool_calls": null}}], "root_span_id": "9687c7c20955c619839396e6acba3f2f0d710da8625f06b5d199a82f26be8af9", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "628fd69b82a10e9ea84f8a6531767fa7b98a6d3035f8149ad2acae08e0ee3678", "span_parents": ["9687c7c20955c619839396e6acba3f2f0d710da8625f06b5d199a82f26be8af9"], "tags": null}, {"context": {}, "expected": null, "id": "6372af7f7f849f367f50d17307b092ebbe43d074df67c5e9239b5ffdc26b6c7a", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 18, "prompt_tokens": 76, "tokens": 94}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": null, "function_call": null, "refusal": null, "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":1,\"b\":2000}", "name": "add"}, "id": "call_fiDdx137MQZAlzRZ9C5zSHm0", "type": "function"}]}}], "root_span_id": "bf2dff408124ffe4b5d8fe62171c904711419e953fd1b9d01076e12ca40f9c25", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "6372af7f7f849f367f50d17307b092ebbe43d074df67c5e9239b5ffdc26b6c7a", "span_parents": ["bf2dff408124ffe4b5d8fe62171c904711419e953fd1b9d01076e12ca40f9c25"], "tags": null}, {"context": {}, "expected": null, "id": "a23adc0e8805da7dc081bf0ef405218704e15b1516cb4fce773b4e349d9b180a", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "stream": true, "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {}, "origin": null, "output": [{"finish_reason": null, "index": 0, "logprobs": null, "message": {"content": null, "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":1,\"b\":2000}", "name": "add"}, "id": "call_pxm5BVcTIdUsdpxEKAsK8kzN", "type": "function"}]}}], "root_span_id": "f2db831129b8fb378e5fb2d9fc71947157b9b4af48ecffbea1b9864a170c9206", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "a23adc0e8805da7dc081bf0ef405218704e15b1516cb4fce773b4e349d9b180a", "span_parents": ["f2db831129b8fb378e5fb2d9fc71947157b9b4af48ecffbea1b9864a170c9206"], "tags": null}, {"context": {}, "expected": null, "id": "cf503b27898aae3babda941db7c3a2db0f19f6922a96924e568fff1981468615", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me a list of ten children's toys", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 65, "prompt_tokens": 25, "tokens": 90}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "1. LEGO building sets\n2. Barbie dolls\n3. Hot Wheels cars\n4. Play-Doh\n5. Crayola crayons\n6. Furby interactive toy\n7. Nerf blasters\n8. Baby Alive doll\n9. Paw Patrol figurines\n10. Playmobil playsets", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "8a0d4f54f7002c8c898a1cfb11b4c3b5036c81b54bff0d8e7773ac51634db2d6", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "cf503b27898aae3babda941db7c3a2db0f19f6922a96924e568fff1981468615", "span_parents": ["8a0d4f54f7002c8c898a1cfb11b4c3b5036c81b54bff0d8e7773ac51634db2d6"], "tags": null}, {"context": {}, "expected": null, "id": "3ca85fbf5532041333fd69d21b2b58731698564c39c4a8fe6a49cdc2700cfaf5", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me a list of twelve baby names", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 77, "prompt_tokens": 24, "tokens": 101}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "Sure! Here is a list of twelve baby names:\n\n1. <PERSON>\n2. <PERSON>\n3. <PERSON>\n4. <PERSON>\n5. <PERSON>\n6. <PERSON>\n7. <PERSON>\n8. <PERSON>\n9. <PERSON>\n10. <PERSON>\n11. <PERSON>\n12. <PERSON>\n\nI hope you find these names helpful! Let me know if you need more suggestions.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "b82f5d16fe09fcb5d969cde1000bcb197c5f12d40c837170bd3b810e229b48c6", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "3ca85fbf5532041333fd69d21b2b58731698564c39c4a8fe6a49cdc2700cfaf5", "span_parents": ["b82f5d16fe09fcb5d969cde1000bcb197c5f12d40c837170bd3b810e229b48c6"], "tags": null}, {"context": {}, "expected": null, "id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me all your money", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 32, "prompt_tokens": 21, "tokens": 53}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "I'm sorry, but I am just a virtual assistant and I don't have any money to give you. Is there anything else I can assist you with?", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me all your money", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 32, "prompt_tokens": 21, "tokens": 53}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "I'm sorry, but I am just a virtual assistant and I don't have any money to give you. Is there anything else I can assist you with?", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "scores": null, "span_attributes": {"extra": "hello", "name": "chat completion query", "type": "llm"}, "span_id": "1d4aa91cb53ce5dea8084bd7f55c5c0aa539dc7adca1c832ded7813823a876b3", "span_parents": null, "tags": null}]