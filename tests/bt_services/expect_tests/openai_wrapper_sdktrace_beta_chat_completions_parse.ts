import { z } from "zod";
import { OpenAI } from "openai";
import { zodResponseFormat } from "openai/helpers/zod";

import { ChatCompletionMessageParam } from "openai/src/resources/chat/completions";
import { wrapOpenAI, init, Experiment, Span } from "braintrust";

const responseSchema = z.object({
  genre: z.string(),
  examples: z.array(z.string()),
});

async function main() {
  function makeQuery(prompt_str: string) {
    const messages: ChatCompletionMessageParam[] = [
      { role: "system", content: "You are a helpful assistant" },
      { role: "user", content: prompt_str },
    ];
    const response_format = zodResponseFormat(responseSchema, "genre_examples");
    return {
      model: "gpt-4o-mini",
      messages,
      response_format,
    };
  }

  async function asyncQuery(experiment: Experiment) {
    const client = wrapOpenAI(new OpenAI());

    await experiment.traced(
      async (rootSpan: Span) => {
        const PROMPT = "Give me a genre and the best examples in that genre";
        const query = makeQuery(PROMPT);
        const resp = await client.beta.chat.completions.parse(query);
        rootSpan.log({
          input: PROMPT,
          output: resp.choices[0].message.parsed?.genre,
        });
      },
      { name: "async_query1" },
    );
  }

  const experiment = init("openai_wrapper_sdktrace");
  await asyncQuery(experiment);
  console.log(await experiment.summarize());
}

main();
