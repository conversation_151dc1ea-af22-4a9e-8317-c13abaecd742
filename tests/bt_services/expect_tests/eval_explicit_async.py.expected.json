[{"context": {}, "expected": "b", "id": "04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foobar", "root_span_id": "04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "66fcd3f9529f13e5060366b412b98457576253e7aed78873ebb6b9d18cbf66f7", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foobar", "root_span_id": "04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "66fcd3f9529f13e5060366b412b98457576253e7aed78873ebb6b9d18cbf66f7", "span_parents": ["04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a"], "tags": null}, {"context": {}, "expected": null, "id": "9592af8e1e616556fd50e9cc1b1bffcd24cd160d0bf70486033f2ba3ed1329cd", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "foobar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.166667}, "root_span_id": "04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a", "scores": {"Levenshtein": 0.166667}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "9592af8e1e616556fd50e9cc1b1bffcd24cd160d0bf70486033f2ba3ed1329cd", "span_parents": ["04f48a99b5236dde85a9d9ea4bebbf5cd8a932da82678fd56671c415c037ca1a"], "tags": null}]