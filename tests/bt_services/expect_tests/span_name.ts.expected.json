[{"context": {}, "expected": null, "id": "c12adbc4e223c01990323bd2a6f6524b50827fd9609570ad5fd08e22566d87be", "input": "row1", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "c12adbc4e223c01990323bd2a6f6524b50827fd9609570ad5fd08e22566d87be", "scores": null, "span_attributes": {"name": "name2", "type": "task"}, "span_id": "c12adbc4e223c01990323bd2a6f6524b50827fd9609570ad5fd08e22566d87be", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "d2aef210700f3be85e1555a8ba89ef91f29f7caea9266c5a56b1022ff4e42271", "input": "row2", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "d2aef210700f3be85e1555a8ba89ef91f29f7caea9266c5a56b1022ff4e42271", "scores": null, "span_attributes": {"name": "name3", "type": "task"}, "span_id": "d2aef210700f3be85e1555a8ba89ef91f29f7caea9266c5a56b1022ff4e42271", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "59eff51e0672c2b7ef1721a4ea361742f7f7751a5f53946cbfb3e8a8743d5e01", "input": "row3", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "59eff51e0672c2b7ef1721a4ea361742f7f7751a5f53946cbfb3e8a8743d5e01", "scores": null, "span_attributes": {"name": "name4", "type": "task"}, "span_id": "59eff51e0672c2b7ef1721a4ea361742f7f7751a5f53946cbfb3e8a8743d5e01", "span_parents": null, "tags": null}]