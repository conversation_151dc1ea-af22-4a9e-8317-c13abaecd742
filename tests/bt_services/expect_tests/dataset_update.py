import braintrust

if __name__ == "__main__":
    print("Creating dataset")

    dataset = braintrust.init_dataset("dataset_update_py")
    n = 2
    for i in range(n):
        dataset.insert(
            input=i,
            output={"result": i + 1, "error": None},
            metadata={
                "foo": i % 2,
                "bar": "baz",
            },
            tags=["foo", "bar"],
            id=str(i),
        )
    dataset.flush()
    # Fetch and print dataset records
    for row in dataset.fetch():
        print(row)

    # Update existing records
    for i in range(n):
        dataset.update(
            input=i,
            metadata={
                "baz": "UPDATE",
            },
            id=str(i),
        )
        dataset.flush()

    # Fetch and print dataset records
    for row in dataset.fetch():
        print(row)

    # Summarize the dataset
    print("Summary:", dataset.summarize())
