import os

from braintrust import <PERSON><PERSON>, <PERSON>, current_span, traced

from autoevals import <PERSON>enshteinScorer


@traced("foo")
def basic_foo():
    current_span().log(metadata=dict(message="In foo"))
    return "bar"


def basic_task(input, hooks):
    hooks.span.log(metadata=dict(message="In task"))
    return basic_foo()


Eval(
    "basic",
    data=lambda: [dict(input="a", expected="b")],
    task=basic_task,
    scores=[LevenshteinScorer],
    experiment_name="My basic eval",
    metadata={"foo": "bar"},
)

Eval(
    "basic",
    data=lambda: [dict(input="c", expected="d")],
    task=basic_task,
    scores=[LevenshteinScorer],
    experiment_name="My other basic eval",
    metadata={"foo": "baz"},
)


def env_var_task(input):
    if os.environ.get("MY_ENV_VAR"):
        return "f"
    else:
        return None


Eval(
    "env_var",
    data=lambda: [dict(input="e", expected="f")],
    task=env_var_task,
    scores=[LevenshteinScorer],
)


def verify_scores_set(evaluator, result):
    print("RESULTS", result)
    if "Levenshtein" not in result.results[0].scores or result.results[0].scores["Levenshtein"] is None:
        raise ValueError("Levenshtein score not set")
    return True


Reporter(
    "verify scores set",
    report_eval=verify_scores_set,
    report_run=lambda eval_reports: True,
)
