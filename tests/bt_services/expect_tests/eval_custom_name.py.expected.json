[{"context": {}, "expected": "b", "id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "4a6d1146a55acfaff77ecf9d3c22810ebda1e60f382f241838842049aa3ca75c", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"Custom scorer": 0.5}, "span_attributes": {"name": "my_handrolled_scorer", "type": "score"}, "span_id": "4a6d1146a55acfaff77ecf9d3c22810ebda1e60f382f241838842049aa3ca75c", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "ad3f34f3a5a39dd075b4b4dbae34f4dd4d00392c22d1fd8aae47b33406f93396", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"my_generated_scorer": 0.5}, "span_attributes": {"name": "my_generated_scorer", "type": "score"}, "span_id": "ad3f34f3a5a39dd075b4b4dbae34f4dd4d00392c22d1fd8aae47b33406f93396", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "c39ebb01ebe529510e9997b127ec459a0a0f525ed64cb6b45b73ab3e9266bfb1", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"JSONDiff": 0}, "span_attributes": {"name": "my_scorer", "type": "score"}, "span_id": "c39ebb01ebe529510e9997b127ec459a0a0f525ed64cb6b45b73ab3e9266bfb1", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}]