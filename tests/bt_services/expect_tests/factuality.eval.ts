import { currentSpan, Eva<PERSON> } from "braintrust";
import { Factuality } from "autoevals";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
async function callModel(input: any) {
  return currentSpan().traced(
    async (span) => {
      const messages = { messages: [{ role: "system", text: input }] };
      span.log({ input: messages });

      // Replace this with a model call
      const result = {
        content: "China",
        latency: 1,
        prompt_tokens: 10,
        completion_tokens: 2,
      };

      span.log({
        output: result.content,
        metrics: {
          latency: result.latency,
          prompt_tokens: result.prompt_tokens,
          completion_tokens: result.completion_tokens,
        },
      });
      return result.content;
    },
    { name: "My AI Model" },
  );
}

Eval("My Evaluation", {
  data: () => [
    { input: "Which country has the highest population?", expected: "China" },
  ],
  task: async (input, { span }) => {
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000));
    return await callModel(input);
  },
  scores: [async (args) => Factuality({ ...args })],
});
