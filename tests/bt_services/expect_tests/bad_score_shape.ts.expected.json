[{"context": {}, "expected": null, "id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "020f894ad66d5d487d29eb660834f4ffd96a1f1ce66104bc15f342f6f643b91a", "span_parents": null, "tags": null}]