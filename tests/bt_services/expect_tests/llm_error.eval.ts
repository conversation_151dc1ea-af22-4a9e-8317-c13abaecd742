import { <PERSON><PERSON>, <PERSON> } from "braintrust";
import { <PERSON>actuality, <PERSON><PERSON>, Possible } from "autoevals";

Eval("Error eval", {
  data: () => [{ input: "What is my name?", expected: "<PERSON>" }],
  task: async (_input, {}) => {
    return "Rob";
  },
  scores: [
    async (args) => Factuality({ ...args, openAiApiKey: "bogus" }),
    Hu<PERSON>,
    async (args) => Possible({ ...args, openAiApiKey: "bogus" }),
  ],
});

Reporter("allow anything", {
  reportEval() {
    console.log("I don't care");
  },
  reportRun() {
    return true;
  },
});
