import * as braintrust from "braintrust";

const RANDOM_NUMS = Array.from({ length: 20 }).map(
  (_, idx) => ((idx + 5) * 99) / 33,
);

async function computeOutput(rowInput: number) {
  return braintrust.traced(
    async () => {
      const randomNum =
        RANDOM_NUMS[(rowInput * 4238923) % RANDOM_NUMS.length] /
        Math.max(...RANDOM_NUMS);
      const output = (randomNum / 10 + 1.9) * rowInput;
      braintrust.currentSpan().log({
        input: rowInput,
        output: output,
        metadata: {
          compute_output: {
            random_num: randomNum,
          },
        },
      });
      return output;
    },
    { name: "computeOutputXYZ" },
  );
}

async function computeAbsoluteOrRelative(expected: number) {
  return braintrust.traced(
    async () => {
      const absOrRel = expected === 0 ? "absolute" : "relative";
      braintrust.currentSpan().log({
        input: expected,
        output: absOrRel,
        scores: {
          is_absolute: absOrRel === "absolute" ? 0.9 : 0.1,
        },
      });
      return absOrRel;
    },
    { name: "computeAbsoluteOrRelativeABC" },
  );
}

async function computeCloseness(output: number, expected: number) {
  return braintrust.traced(
    async () => {
      const absOrRel = await computeAbsoluteOrRelative(expected);
      const closeness = absOrRel === "absolute" ? 1 : output / expected;
      braintrust.currentSpan().log({
        input: {
          output: output,
          expected: expected,
        },
        output: closeness,
        scores: {
          closeness: closeness,
        },
      });
    },
    { name: "computeCloseness" },
  );
}

(async () => {
  const rows = Array.from({ length: 10 }).map((_, idx) => ({
    input: idx,
    output: idx * 2,
  }));
  const experiment = braintrust.init("experiment_span");
  await Promise.all(
    rows.map(async (row, idx) =>
      braintrust.traced(
        async () => {
          const output = await computeOutput(row.input);
          braintrust
            .currentSpan()
            .log({ output: output, metadata: { row_num: idx } });
          await computeCloseness(output, row.output);
        },
        {
          event: { input: row.input, expected: row.output },
        },
      ),
    ),
  );
  console.log(`Finished experiment with id ${await experiment.id}`);
  console.log(await experiment.summarize());
})();
