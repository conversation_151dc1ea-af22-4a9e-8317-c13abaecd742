[{"context": {}, "expected": null, "id": "418b72f9facc87bcf5127a2712052b8e40a6ec370e75a2185be9e59705439f58", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"numResponses": 21}, "root_span_id": "418b72f9facc87bcf5127a2712052b8e40a6ec370e75a2185be9e59705439f58", "scores": null, "span_attributes": {"name": "async_query1", "type": "eval"}, "span_id": "418b72f9facc87bcf5127a2712052b8e40a6ec370e75a2185be9e59705439f58", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "aa8c5828dacac8f8e893135e78fb4eae1c2aacb0703fd98f0f1c9a5eeb9c35fd", "input": "Give a random number between 1 and 2000", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"numResponses": 12}, "root_span_id": "aa8c5828dacac8f8e893135e78fb4eae1c2aacb0703fd98f0f1c9a5eeb9c35fd", "scores": null, "span_attributes": {"name": "function_call_streaming", "type": "eval"}, "span_id": "aa8c5828dacac8f8e893135e78fb4eae1c2aacb0703fd98f0f1c9a5eeb9c35fd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "6cb7eb005f8d18cf2fac17fbba5071121e8f8c3e082a122beea6cf418fa014ca", "input": "Give me a list of ten children's toys", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "1. <PERSON><PERSON> set\n2. Barbie doll\n3. Hot Wheels cars\n4. <PERSON>-<PERSON><PERSON> set\n5. Teddy bear\n6. <PERSON><PERSON><PERSON> blaster\n7. Board game (e.g., <PERSON><PERSON><PERSON>, Candy Land)\n8. Baby doll\n9. Remote control car\n10. Building blocks", "role": "assistant"}}, "root_span_id": "6cb7eb005f8d18cf2fac17fbba5071121e8f8c3e082a122beea6cf418fa014ca", "scores": null, "span_attributes": {"name": "async_query0", "type": "eval"}, "span_id": "6cb7eb005f8d18cf2fac17fbba5071121e8f8c3e082a122beea6cf418fa014ca", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a2ccbc414d7fb7feded7bceb7226c0d78aa134d19f1e1315175d4ee0cdb1c257", "input": "Give me a list of ten children's toys", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": {"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"content": null, "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\": 5, \"b\": 5}", "name": "add"}, "id": "call_DjzPSVONnnVnlXIqVOY81ZUa", "type": "function"}, {"function": {"arguments": "{\"a\": 10, \"b\": 5}", "name": "add"}, "id": "call_Pt23GHcDg578S7mJGOsAEhhs", "type": "function"}]}}, "root_span_id": "a2ccbc414d7fb7feded7bceb7226c0d78aa134d19f1e1315175d4ee0cdb1c257", "scores": null, "span_attributes": {"name": "function_call_nonstreaming", "type": "eval"}, "span_id": "a2ccbc414d7fb7feded7bceb7226c0d78aa134d19f1e1315175d4ee0cdb1c257", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "6e2d540dae0351cc5985ea64a312d9ff7cb858eaccab5ac4dc8303f7e963230d", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "stream": true}, "metrics": {}, "origin": null, "output": [{"index": 0, "logprobs": null, "message": {"content": "Sure! Here is a random number between 1 and 2000: 1369.", "role": "assistant"}}], "root_span_id": "418b72f9facc87bcf5127a2712052b8e40a6ec370e75a2185be9e59705439f58", "scores": null, "span_attributes": {"name": "chat completion query", "type": "llm"}, "span_id": "6e2d540dae0351cc5985ea64a312d9ff7cb858eaccab5ac4dc8303f7e963230d", "span_parents": ["418b72f9facc87bcf5127a2712052b8e40a6ec370e75a2185be9e59705439f58"], "tags": null}, {"context": {}, "expected": null, "id": "b4c73ebc6c5354ae031060aa298fa0b4fdb923135894b69db76f891ac77b7972", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give a random number between 1 and 2000", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "stream": true, "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {}, "origin": null, "output": [{"index": 0, "logprobs": null, "message": {"role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":1,\"b\":2000}", "name": "add"}, "id": "call_aELjJIpHnUrfdmnGpELsX9pL", "type": "function"}]}}], "root_span_id": "aa8c5828dacac8f8e893135e78fb4eae1c2aacb0703fd98f0f1c9a5eeb9c35fd", "scores": null, "span_attributes": {"name": "chat completion query", "type": "llm"}, "span_id": "b4c73ebc6c5354ae031060aa298fa0b4fdb923135894b69db76f891ac77b7972", "span_parents": ["aa8c5828dacac8f8e893135e78fb4eae1c2aacb0703fd98f0f1c9a5eeb9c35fd"], "tags": null}, {"context": {}, "expected": null, "id": "8c70d6ac8012c8ec2f72e34b5582e76751c93c35cefa3b757fae3177bb2c0c8e", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me a list of ten children's toys", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai", "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 49, "prompt_tokens": 74, "tokens": 123}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"content": null, "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\": 5, \"b\": 5}", "name": "add"}, "id": "call_DjzPSVONnnVnlXIqVOY81ZUa", "type": "function"}, {"function": {"arguments": "{\"a\": 10, \"b\": 5}", "name": "add"}, "id": "call_Pt23GHcDg578S7mJGOsAEhhs", "type": "function"}]}}], "root_span_id": "a2ccbc414d7fb7feded7bceb7226c0d78aa134d19f1e1315175d4ee0cdb1c257", "scores": null, "span_attributes": {"name": "chat completion query", "type": "llm"}, "span_id": "8c70d6ac8012c8ec2f72e34b5582e76751c93c35cefa3b757fae3177bb2c0c8e", "span_parents": ["a2ccbc414d7fb7feded7bceb7226c0d78aa134d19f1e1315175d4ee0cdb1c257"], "tags": null}, {"context": {}, "expected": null, "id": "b47cb34170f29ee52b1a340b2b334d0e4dabc1d594867ff6fe3e1299d0323644", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me a list of ten children's toys", "role": "user"}], "is_root": false, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 64, "prompt_tokens": 25, "tokens": 89}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "1. <PERSON><PERSON> set\n2. Barbie doll\n3. Hot Wheels cars\n4. <PERSON>-<PERSON><PERSON> set\n5. Teddy bear\n6. <PERSON><PERSON><PERSON> blaster\n7. Board game (e.g., <PERSON><PERSON><PERSON>, Candy Land)\n8. Baby doll\n9. Remote control car\n10. Building blocks", "role": "assistant"}}], "root_span_id": "6cb7eb005f8d18cf2fac17fbba5071121e8f8c3e082a122beea6cf418fa014ca", "scores": null, "span_attributes": {"name": "chat completion query", "type": "llm"}, "span_id": "b47cb34170f29ee52b1a340b2b334d0e4dabc1d594867ff6fe3e1299d0323644", "span_parents": ["6cb7eb005f8d18cf2fac17fbba5071121e8f8c3e082a122beea6cf418fa014ca"], "tags": null}, {"context": {}, "expected": null, "id": "80af19028370da682245b9864f2e4bd9403cf6a7ec6cca19cd164e06d7786d7a", "input": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "Give me all your money", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 27, "prompt_tokens": 21, "tokens": 48}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "I'm afraid I can't do that. How about I help you with something else instead? What do you need assistance with today?", "role": "assistant"}}], "root_span_id": "80af19028370da682245b9864f2e4bd9403cf6a7ec6cca19cd164e06d7786d7a", "scores": null, "span_attributes": {"name": "chat completion query", "type": "llm"}, "span_id": "80af19028370da682245b9864f2e4bd9403cf6a7ec6cca19cd164e06d7786d7a", "span_parents": null, "tags": null}]