[{"context": {}, "expected": "bar", "id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "span_parents": null, "tags": null}, {"context": {}, "expected": "bar", "id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "input": "foo", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "218b93fc1e32758257c37f01dc592be3555a974acd834638bfdc704e3cdbedc9", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "218b93fc1e32758257c37f01dc592be3555a974acd834638bfdc704e3cdbedc9", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "218b93fc1e32758257c37f01dc592be3555a974acd834638bfdc704e3cdbedc9", "span_parents": null, "tags": ["zoo"]}, {"context": {}, "expected": null, "id": "9816b9a189736c5734fd64fc642017553452c3ce71a2acf84c2452628187b704", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "9816b9a189736c5734fd64fc642017553452c3ce71a2acf84c2452628187b704", "span_parents": ["bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55"], "tags": null}, {"context": {}, "expected": null, "id": "9816b9a189736c5734fd64fc642017553452c3ce71a2acf84c2452628187b704", "input": "foo", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "9816b9a189736c5734fd64fc642017553452c3ce71a2acf84c2452628187b704", "span_parents": ["bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55"], "tags": null}, {"expected": "bar", "id": "91bc2a3a9e2e5f2de6d7f46d206d67aeceb180093039166ab4986b95a8f6be2c", "input": "foo", "is_root": true, "metadata": null, "origin": null, "root_span_id": "91bc2a3a9e2e5f2de6d7f46d206d67aeceb180093039166ab4986b95a8f6be2c", "span_id": "91bc2a3a9e2e5f2de6d7f46d206d67aeceb180093039166ab4986b95a8f6be2c", "tags": ["bar", "baz"]}, {"expected": "bar", "id": "f04f5861b9bee1f42324bee02afb752ae885b989eb6ff6c5e29653fd584285ae", "input": "foo", "is_root": true, "metadata": null, "origin": null, "root_span_id": "f04f5861b9bee1f42324bee02afb752ae885b989eb6ff6c5e29653fd584285ae", "span_id": "f04f5861b9bee1f42324bee02afb752ae885b989eb6ff6c5e29653fd584285ae", "tags": ["bar", "bar"]}, {"context": {}, "expected": null, "id": "3f996ad422302b19cd4b929884f41df9d7b83b285cc36f415315a5bc067c6092", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "218b93fc1e32758257c37f01dc592be3555a974acd834638bfdc704e3cdbedc9", "scores": null, "span_attributes": {"name": "main:tags_sanity.ts:23"}, "span_id": "3f996ad422302b19cd4b929884f41df9d7b83b285cc36f415315a5bc067c6092", "span_parents": ["218b93fc1e32758257c37f01dc592be3555a974acd834638bfdc704e3cdbedc9"], "tags": null}, {"context": {}, "expected": null, "id": "5b3014fa94b1c3528de3dc602b486c20e2206375d96f6cd0e751b49087492914", "input": {"expected": "bar", "input": "foo", "metadata": {}}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "5b3014fa94b1c3528de3dc602b486c20e2206375d96f6cd0e751b49087492914", "span_parents": ["bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55"], "tags": null}, {"context": {}, "expected": null, "id": "5b3014fa94b1c3528de3dc602b486c20e2206375d96f6cd0e751b49087492914", "input": {"expected": "bar", "input": "foo", "metadata": {}}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "5b3014fa94b1c3528de3dc602b486c20e2206375d96f6cd0e751b49087492914", "span_parents": ["bb0849a9f1b5bda50ab48036b49b88a79571eb746b5fe6ad468077b1d38ffa55"], "tags": null}]