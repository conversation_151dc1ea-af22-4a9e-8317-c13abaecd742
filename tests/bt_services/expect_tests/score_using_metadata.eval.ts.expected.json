[{"context": {}, "expected": "0", "id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": "1", "id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": "2", "id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": "3", "id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": "4", "id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}, {"context": {}, "expected": null, "id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "695ce842af2632b791d643e6a0b1ff1efe92d0e2a16469eb5f3550d0abfa2ef9", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "695ce842af2632b791d643e6a0b1ff1efe92d0e2a16469eb5f3550d0abfa2ef9", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": null, "id": "a1cdc52cd2d37c1d93a0ea5f2ea8e1305e82a31eeb7db0d2f77003c7c65bb1b8", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a1cdc52cd2d37c1d93a0ea5f2ea8e1305e82a31eeb7db0d2f77003c7c65bb1b8", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": null, "id": "6c2bde3e35ff54925a7671f33195a60fc23b2c32b1296b884f146cdc4ebff0e0", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "6c2bde3e35ff54925a7671f33195a60fc23b2c32b1296b884f146cdc4ebff0e0", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "de4b1623d8a2cd779850ff6bb627c5249acad4dd48ae22383efb7eb23d37a3e3", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "de4b1623d8a2cd779850ff6bb627c5249acad4dd48ae22383efb7eb23d37a3e3", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "952579bbcaf8f6de4481b5089e2db224ae7f0ec56e3228902089cb6a90d4d863", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "952579bbcaf8f6de4481b5089e2db224ae7f0ec56e3228902089cb6a90d4d863", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "c97bb7435d3e8653af2313cccdfa452e2e9a0e135aa7942cafba81e083972ec3", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "c97bb7435d3e8653af2313cccdfa452e2e9a0e135aa7942cafba81e083972ec3", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "761be48ab5c8ec764a1c89d4e6341aa87734df5129a25c0db4ccddbe85006f49", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "761be48ab5c8ec764a1c89d4e6341aa87734df5129a25c0db4ccddbe85006f49", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "ced06e566b62975f99139863ae801d0427c6f3ee54f4bdb7c03284bf3e137519", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ced06e566b62975f99139863ae801d0427c6f3ee54f4bdb7c03284bf3e137519", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "7ad55682b30051277cce53d3acec04689232b92c6085c9b54bb0c479e10ffbcc", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "7ad55682b30051277cce53d3acec04689232b92c6085c9b54bb0c479e10ffbcc", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": null, "id": "ee067dcbd25621c264d64021fbb31f72ad21d850da6396308a19a6f58844ce67", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "ee067dcbd25621c264d64021fbb31f72ad21d850da6396308a19a6f58844ce67", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": null, "id": "843a84633f34601265a5d3df511b91bae2f1916841333f2a90fca632442fab66", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "843a84633f34601265a5d3df511b91bae2f1916841333f2a90fca632442fab66", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "8b1c71719d34fa9055df80a1dd56721c89b767c8ed9a2084a72a47364bc697f2", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "8b1c71719d34fa9055df80a1dd56721c89b767c8ed9a2084a72a47364bc697f2", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "a582f4f97efafc6af1175430fd370d8a6d139555ca1ed6e0714bcb2f57d7c2bd", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a582f4f97efafc6af1175430fd370d8a6d139555ca1ed6e0714bcb2f57d7c2bd", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "a6f1252d054f9385c74453595587f44b021fcc91af5882bfd94f0e2b6db38704", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "a6f1252d054f9385c74453595587f44b021fcc91af5882bfd94f0e2b6db38704", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "019347a2bd6da5ed7014f154c9784098146e317864f7a1df28ae283ab5da235c", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "019347a2bd6da5ed7014f154c9784098146e317864f7a1df28ae283ab5da235c", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "eeaea69698e38d6cc2e8a839902242a7bc1a4802f732d64c52ea7e4866bdb945", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "eeaea69698e38d6cc2e8a839902242a7bc1a4802f732d64c52ea7e4866bdb945", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "ccd8f9619c01a2713b3e105d0e4075cd677ccad2a6ee58fce008eec67a16cfaf", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ccd8f9619c01a2713b3e105d0e4075cd677ccad2a6ee58fce008eec67a16cfaf", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "f5f8b3d36468dc1efc051e1eb0be095b3256299cce869760cfa171dafa174c4e", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "f5f8b3d36468dc1efc051e1eb0be095b3256299cce869760cfa171dafa174c4e", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "c610a84bf9d8a425d2926173ec2b95682f74c6c13928e604d60619d4e3bbaaf9", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "c610a84bf9d8a425d2926173ec2b95682f74c6c13928e604d60619d4e3bbaaf9", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": null, "id": "f9361a712362082f7687d1297a5325968f8084ac67eaa601542963a967cdcfb6", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "f9361a712362082f7687d1297a5325968f8084ac67eaa601542963a967cdcfb6", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": null, "id": "972eb8702c9e58b46dda8f585b1613ee44755a646fe5faf7fcc244cefa8908f6", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "972eb8702c9e58b46dda8f585b1613ee44755a646fe5faf7fcc244cefa8908f6", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "a1aae90750971aabb7792a7ac38b46b0ae02dd8cecbaed6b0e735c83d5e942b1", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "a1aae90750971aabb7792a7ac38b46b0ae02dd8cecbaed6b0e735c83d5e942b1", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "78e672a568b3cd8607e909b12619a1084de00f747c51df13b9aee9cd0abd1b63", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "78e672a568b3cd8607e909b12619a1084de00f747c51df13b9aee9cd0abd1b63", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "d05f4cf7c3d4edb6d3a0c71dbf574580cd1ddd53d51f1d999df1bbd0517f8fbc", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "d05f4cf7c3d4edb6d3a0c71dbf574580cd1ddd53d51f1d999df1bbd0517f8fbc", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "8700bb68f74514e014b9341868659ede0921e2c0ee7899bbdc725b4e2a8b8f1e", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "8700bb68f74514e014b9341868659ede0921e2c0ee7899bbdc725b4e2a8b8f1e", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "8e58ab05f2112802c934bbbacde834426db6d3a3b3bc7eaddb83150ec862f4c2", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "8e58ab05f2112802c934bbbacde834426db6d3a3b3bc7eaddb83150ec862f4c2", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "2e880f79dcca18a1b237d9460d1ae54f61d564c46847db400515f07145d9c8da", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2e880f79dcca18a1b237d9460d1ae54f61d564c46847db400515f07145d9c8da", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": null, "id": "4e285cea67be7c87243e1d843da28272313401fa9037ac5675d933541d88c007", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "4e285cea67be7c87243e1d843da28272313401fa9037ac5675d933541d88c007", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": null, "id": "3fc9fcc186dfb015f2b9f7693b698432e7c444cc95992e32fa38f96592ee9e64", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "3fc9fcc186dfb015f2b9f7693b698432e7c444cc95992e32fa38f96592ee9e64", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "a2cb0defe6f428289d8755273f6c6a41d0c1726292eb0ab9fa0bc163991a82ad", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a2cb0defe6f428289d8755273f6c6a41d0c1726292eb0ab9fa0bc163991a82ad", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "c30bea311f27d2036b3c38f793c64688954dc53de39975ee34cbb107b412e646", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "c30bea311f27d2036b3c38f793c64688954dc53de39975ee34cbb107b412e646", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "f2d26287adf6131f163f8920e36ca542adc65c6de9eea90a8d72c6aa9ff121a6", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "f2d26287adf6131f163f8920e36ca542adc65c6de9eea90a8d72c6aa9ff121a6", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "65d2d7dbe7fdd95c259926ba8b3696e43fd6246478e1e9c77b7b721ba0734e7a", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "65d2d7dbe7fdd95c259926ba8b3696e43fd6246478e1e9c77b7b721ba0734e7a", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "b0ec603c08443e57d2c45546a390bce8c87e6885fb03f8487ccab9fa13e49577", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "b0ec603c08443e57d2c45546a390bce8c87e6885fb03f8487ccab9fa13e49577", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "7e0f7063da30447dda2acb2b7f9aa76ab4bad5c9e674ee55341cc76443b3c09b", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "7e0f7063da30447dda2acb2b7f9aa76ab4bad5c9e674ee55341cc76443b3c09b", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "ddc3f5125917f42d7e8958f6650e9ca4ca7ff960c4a0f60a5d286204dc686f0e", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ddc3f5125917f42d7e8958f6650e9ca4ca7ff960c4a0f60a5d286204dc686f0e", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "1f8f8504c88684d66e290833822ac7b6f60aacc537598aafb356276ba36c6d79", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "1f8f8504c88684d66e290833822ac7b6f60aacc537598aafb356276ba36c6d79", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "eba1891e5141aad7886eaf6894164bd0e58b20ab3fef04566b43095c41161084", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "eba1891e5141aad7886eaf6894164bd0e58b20ab3fef04566b43095c41161084", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "86ba4d93e77513b56614386c43c8330f58c6e96ce1b1f089934d50f58c52bca3", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "emptyOutputs", "type": "score"}, "span_id": "86ba4d93e77513b56614386c43c8330f58c6e96ce1b1f089934d50f58c52bca3", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}, {"context": {}, "expected": null, "id": "ef673b79b43b44a7d419fade3a2c649f4a43f4549164b9e372b81012780e1411", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ef673b79b43b44a7d419fade3a2c649f4a43f4549164b9e372b81012780e1411", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}]