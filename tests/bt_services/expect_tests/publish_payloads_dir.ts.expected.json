[{"context": {}, "expected": null, "id": "cc1a8a3e22b927dee3fab702f9290f1fb44c01b75c8045a6391205d1a8e1cdad", "input": "foobar", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "foobar", "root_span_id": "cc1a8a3e22b927dee3fab702f9290f1fb44c01b75c8045a6391205d1a8e1cdad", "scores": {}, "span_attributes": {"name": "root", "type": "eval"}, "span_id": "cc1a8a3e22b927dee3fab702f9290f1fb44c01b75c8045a6391205d1a8e1cdad", "span_parents": null, "tags": null}]