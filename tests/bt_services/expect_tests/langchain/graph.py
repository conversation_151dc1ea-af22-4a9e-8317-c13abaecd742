import asyncio
import os
import threading
import uuid
from typing import TypedDict

from braintrust import traced
from braintrust.logger import init_logger
from braintrust_langchain import BraintrustCallbackHandler
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph

braintrust_logger = init_logger(
    project="langchain-py-graph", api_key=os.getenv("BRAINTRUST_API_KEY"), set_current=True
)


run_id = uuid.uuid4()
guid = str(uuid.uuid4())
user_id = str(uuid.uuid4())

config: RunnableConfig = {
    "run_id": run_id,
    "run_name": f"braintrust-example",
    "tags": ["langgraph", "multi-agent", "local"],
    "metadata": {
        "session_id": guid,
        "app_name": "braintrust-example",
        "user_id": user_id,
        "user_email": "<EMAIL>",
        "organization_guid": user_id,
        "architecture": "multi-agent",
    },
    "configurable": {
        "thread_id": guid,
    },
    "callbacks": [BraintrustCallbackHandler(logger=braintrust_logger)],
}


@traced
def other(*args, **kwargs):
    return 1


# 1. Define the State
class AgentState(TypedDict):
    """
    Represents the state of our graph.

    Attributes:
        messages: A list of messages in the conversation.
        user_input: The user's initial input.
    """

    messages: list
    user_input: str


# 2. Define the Nodes
def greet_node(state: AgentState):
    """A node that generates a greeting message."""
    other()

    return {"messages": [f"Hello, {state['user_input']}! How can I help you today?"]}


def respond_node(state: AgentState):
    """A node that simulates a response based on the input."""
    # In a real application, this would involve an LLM call or tool usage
    return {"messages": [f"You asked: '{state['messages'][-1]}'. I'm processing that now."]}


# 3. Build the Graph
workflow = StateGraph(AgentState)

# Add nodes to the graph
workflow.add_node("greet", greet_node)
workflow.add_node("respond", respond_node)

# Define edges
workflow.set_entry_point("greet")  # Start at the 'greet' node
workflow.add_edge("greet", "respond")  # After 'greet', go to 'respond'
workflow.add_edge("respond", END)  # After 'respond', end the graph

# Compile the graph
app = workflow.compile()

# 4. Run the Graph
initial_state = {"messages": [], "user_input": "Alice"}
result = app.invoke(initial_state, config)

print("\nFinal State:")
print(result)
