[{"context": {}, "expected": null, "id": "3b70e5d3b40cac96b86ba3917e87c4b74f61b37b041ebe2a26159668f81965ad", "input": {"messages": ["Hello, <PERSON>! How can I help you today?"], "user_input": "<PERSON>"}, "is_root": false, "metadata": {"app_name": "braintrust-example", "architecture": "multi-agent", "langgraph_node": "respond", "langgraph_path": ["__pregel_pull", "respond"], "langgraph_step": 2, "langgraph_triggers": ["branch:to:respond"], "tags": ["graph:step:2", "langgraph", "multi-agent", "local"], "user_email": "<EMAIL>"}, "metrics": {}, "origin": null, "output": {"messages": ["You asked: 'Hello, <PERSON>! How can I help you today?'. I'm processing that now."]}, "root_span_id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "scores": null, "span_attributes": {"name": "respond"}, "span_id": "3b70e5d3b40cac96b86ba3917e87c4b74f61b37b041ebe2a26159668f81965ad", "span_parents": ["655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149"], "tags": null}, {"context": {}, "expected": null, "id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "input": {"messages": [], "user_input": "<PERSON>"}, "is_root": true, "metadata": {"app_name": "braintrust-example", "architecture": "multi-agent", "tags": ["langgraph", "multi-agent", "local"], "user_email": "<EMAIL>"}, "metrics": {}, "origin": null, "output": {"messages": ["You asked: 'Hello, <PERSON>! How can I help you today?'. I'm processing that now."], "user_input": "<PERSON>"}, "root_span_id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "scores": null, "span_attributes": {"name": "braintrust-example", "type": "task"}, "span_id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "d29fdc37c3ba2733f5d776fd61710510e6a7f408b96ba757d01f68c904efbf1c", "input": {"messages": [], "user_input": "<PERSON>"}, "is_root": false, "metadata": {"app_name": "braintrust-example", "architecture": "multi-agent", "langgraph_node": "greet", "langgraph_path": ["__pregel_pull", "greet"], "langgraph_step": 1, "langgraph_triggers": ["branch:to:greet"], "tags": ["graph:step:1", "langgraph", "multi-agent", "local"], "user_email": "<EMAIL>"}, "metrics": {}, "origin": null, "output": {"messages": ["Hello, <PERSON>! How can I help you today?"]}, "root_span_id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "scores": null, "span_attributes": {"name": "greet"}, "span_id": "d29fdc37c3ba2733f5d776fd61710510e6a7f408b96ba757d01f68c904efbf1c", "span_parents": ["655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149"], "tags": null}, {"context": {}, "expected": null, "id": "fe635c905554012398885d21af8f025b463eb4816ab32d9ccd88d2b207643b06", "input": {}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 1, "root_span_id": "655f33ec8bbedcd320cadb3452ee5c19783f91c079a24b9468877a522bd29149", "scores": null, "span_attributes": {"name": "other", "type": "function"}, "span_id": "fe635c905554012398885d21af8f025b463eb4816ab32d9ccd88d2b207643b06", "span_parents": ["d29fdc37c3ba2733f5d776fd61710510e6a7f408b96ba757d01f68c904efbf1c"], "tags": null}]