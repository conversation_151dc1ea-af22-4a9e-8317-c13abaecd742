[{"context": {}, "expected": "foo = a", "id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "span_parents": null, "tags": null}, {"context": {}, "expected": "foo = a", "id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "span_parents": ["166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49"], "tags": null}, {"context": {}, "expected": null, "id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "span_parents": ["166fca15e5164688016245e666d6ccec838ba81e901cd06800720c9d56ec2e49"], "tags": null}, {"context": {}, "expected": null, "id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "span_parents": ["b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad"], "tags": null}, {"context": {}, "expected": null, "id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "span_parents": ["b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad"], "tags": null}, {"context": {}, "expected": null, "id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "85ed0f6a7576ee5ad8a8719d604dc046555258c06ddb277fc8b2df0ebe1baa24", "span_parents": ["b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad"], "tags": null}, {"context": {}, "expected": null, "id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "foo = a", "root_span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b391ad663c4027302e92b0aa1149df3c7c8f2d1523a11da373d035d0114c25ad", "span_parents": null, "tags": null}]