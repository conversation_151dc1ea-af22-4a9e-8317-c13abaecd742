import braintrust, { initLogger, wrap<PERSON>penAI } from "braintrust";
import OpenAI from "openai";

const project = braintrust.projects.create({
  name: "test",
});

const prompt = project.prompts.create({
  name: "test",
  messages: [
    {
      role: "user",
      content: "what is {{formula}}?",
    },
  ],
  model: "gpt-4o",
});

initLogger({ projectName: "test" });
const client = wrapOpenAI(new OpenAI());

async function main() {
  const result = await client.chat.completions.create(
    prompt.build({
      formula: "1+1",
    }),
  );
  console.log(result);
}

main();
