[{"context": {}, "expected": "2", "id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "975e8902dd474122e3d885bc976a66aef4625053d09e758d63cfda0075eef3fe", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["975e8902dd474122e3d885bc976a66aef4625053d09e758d63cfda0075eef3fe"], "tags": null}, {"context": {}, "expected": null, "id": "975e8902dd474122e3d885bc976a66aef4625053d09e758d63cfda0075eef3fe", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "975e8902dd474122e3d885bc976a66aef4625053d09e758d63cfda0075eef3fe", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "975e8902dd474122e3d885bc976a66aef4625053d09e758d63cfda0075eef3fe", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "db420725ba408d14d9b697cd59060ec5283287cc1ef33036fcce6bd256558592", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["db420725ba408d14d9b697cd59060ec5283287cc1ef33036fcce6bd256558592"], "tags": null}, {"context": {}, "expected": null, "id": "db420725ba408d14d9b697cd59060ec5283287cc1ef33036fcce6bd256558592", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "db420725ba408d14d9b697cd59060ec5283287cc1ef33036fcce6bd256558592", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "db420725ba408d14d9b697cd59060ec5283287cc1ef33036fcce6bd256558592", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "026797d133da5d261a0a294b0f6d04976a0dcb52c8fd4a357de2dd7d94f534dc", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "026797d133da5d261a0a294b0f6d04976a0dcb52c8fd4a357de2dd7d94f534dc", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "026797d133da5d261a0a294b0f6d04976a0dcb52c8fd4a357de2dd7d94f534dc", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "026797d133da5d261a0a294b0f6d04976a0dcb52c8fd4a357de2dd7d94f534dc", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["026797d133da5d261a0a294b0f6d04976a0dcb52c8fd4a357de2dd7d94f534dc"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "a6026a700de5e6138c2a56452c6ce44743803fc4b3f1ba5eaaf85e5bdd17d21e", "input": 5, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "6", "root_span_id": "f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a6026a700de5e6138c2a56452c6ce44743803fc4b3f1ba5eaaf85e5bdd17d21e", "span_parents": ["f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b"], "tags": null}, {"context": {}, "expected": null, "id": "f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b", "input": 5, "is_root": true, "metadata": {"scorer_errors": {"Levenshtein": ""}}, "metrics": {}, "origin": null, "output": "6", "root_span_id": "f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "13a21193e3b546faf8fb729d4fef026a4011664ec589ef9619058ff17ca17f23", "input": 6, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "7", "root_span_id": "1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "13a21193e3b546faf8fb729d4fef026a4011664ec589ef9619058ff17ca17f23", "span_parents": ["1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73"], "tags": null}, {"context": {}, "expected": null, "id": "1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73", "input": 6, "is_root": true, "metadata": {"scorer_errors": {"Levenshtein": ""}}, "metrics": {}, "origin": null, "output": "7", "root_span_id": "1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2cbfa4df8152516e6e0f4bf97b80c7b67a3c19a01add8f13689cbf06d412fd68", "input": 7, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "8", "root_span_id": "f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2cbfa4df8152516e6e0f4bf97b80c7b67a3c19a01add8f13689cbf06d412fd68", "span_parents": ["f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da"], "tags": null}, {"context": {}, "expected": null, "id": "f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da", "input": 7, "is_root": true, "metadata": {"scorer_errors": {"Levenshtein": ""}}, "metrics": {}, "origin": null, "output": "8", "root_span_id": "f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "input": {"expected": "2", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "input": {"expected": "2", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "input": {"expected": "2", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "input": {"expected": "2", "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "da7922e6cf4aee7ce25f3190fd621d030e3f6d8577af47c67600f917926444c2", "span_parents": ["7252a9e0cba72420be596fd2f6aab019545f5e86192ca4ccbd36e201444b6f7d"], "tags": null}, {"context": {}, "expected": null, "id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "input": {"expected": "3", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "input": {"expected": "3", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "input": {"expected": "3", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "input": {"expected": "3", "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "269cb0be036d3ec970163aae6bc9d968072436a47dcb0c21969e23d745428549", "span_parents": ["6127d97f770fdcf759a35cb9ef7f4f7f9da492c78e5f7528eb7a32916fc8fb3a"], "tags": null}, {"context": {}, "expected": null, "id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "input": {"expected": "4", "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "input": {"expected": "4", "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "input": {"expected": "4", "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "expected": null, "id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "input": {"expected": "4", "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1a8e2ef378ecad911de340057c2bb15518d4e99f5128b569913a0e8f356cd9f4", "span_parents": ["5dd6c5f789a74c54e38e55b69dfc9a04cd55ca44d1754bc89a38ffe8d8daaae6"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "3705c063581ff0784d68fa1987c2002b9d9a2c33667cb8887ffdb6fd6b86456f", "input": {"input": 5, "metadata": {}, "output": "6"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b", "scores": null, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "3705c063581ff0784d68fa1987c2002b9d9a2c33667cb8887ffdb6fd6b86456f", "span_parents": ["f2e0535a1b937c19723e775ffa56e74cd92760a20eeb50ad66c15ed4e0e45f8b"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "a34a5fbb357d99d40e28a87f3e389480741d417ca16233efa48610a71f7d25e9", "input": {"input": 6, "metadata": {}, "output": "7"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73", "scores": null, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "a34a5fbb357d99d40e28a87f3e389480741d417ca16233efa48610a71f7d25e9", "span_parents": ["1c36f3c680d22cb61c4c62516e2411ef8d658595ca71b3301c118091b1d9bc73"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "9220c7b14d18351e9857eafcd5418026327c1a4286cf54bc5394816f2d9f8091", "input": {"input": 7, "metadata": {}, "output": "8"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da", "scores": null, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "9220c7b14d18351e9857eafcd5418026327c1a4286cf54bc5394816f2d9f8091", "span_parents": ["f85809432f1ed8ea17179feae8ed97903c7ba3cc159d549444e6f22a3bda74da"], "tags": null}]