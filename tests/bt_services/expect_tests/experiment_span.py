import braintrust

RANDOM_NUMS = [(idx + 5) * 99 / 33 for idx in range(20)]


@braintrust.traced(name="compute_output_xyz")
def compute_output(row_input):
    random_num = RANDOM_NUMS[(row_input * 4238923) % len(RANDOM_NUMS)] / max(RANDOM_NUMS)
    braintrust.current_span().log(metadata=dict(compute_output=dict(random_num=random_num)))
    return ((random_num / 10) + 1.9) * row_input


@braintrust.traced("compute_absolute_or_relative_abc")
def compute_absolute_or_relative(expected):
    abs_or_rel = "absolute" if expected == 0 else "relative"
    braintrust.current_span().log(scores=dict(is_absolute=0.9 if (abs_or_rel == "absolute") else 0.1))
    return abs_or_rel


@braintrust.traced("compute_closeness")
def compute_closeness(output, expected):
    abs_or_rel = compute_absolute_or_relative(expected)
    closeness = 1 if abs_or_rel == "absolute" else output / expected
    braintrust.current_span().log(scores=dict(closeness=closeness))
    return closeness


if __name__ == "__main__":
    rows = [dict(input=i, output=i * 2) for i in range(10)]

    experiment = braintrust.init("experiment_span")
    for i, row in enumerate(rows):
        with braintrust.start_span(input=row["input"], expected=row["output"]) as root_span:
            output = compute_output(row["input"])
            root_span.log(output=output, metadata=dict(row_num=i))
            compute_closeness(output, row["output"])
    print(experiment.summarize())
