import { init, initLogger, updateSpan } from "braintrust";

async function main() {
  const experiment = init("test update span");

  // Log initial span
  const spanId = experiment.log({
    input: "initial input",
    output: "initial output",
    scores: { accuracy: 0.8 },
  });
  await experiment.flush();

  // Update the span
  experiment.updateSpan({
    id: spanId,
    output: "updated output",
    scores: { accuracy: 0.9 },
  });
  await experiment.flush();

  // Verify the update
  const summary = await experiment.summarize();
  console.log(summary);

  // Log a span with a logger
  const logger = initLogger({
    projectName: "test update span logger",
    asyncFlush: true,
  });
  const loggerSpanId = logger.log({
    input: "logger input",
    output: "logger output",
    scores: { precision: 0.7 },
  });
  await logger.flush();

  // Update the logger span
  logger.updateSpan({
    id: loggerSpanId,
    output: "updated logger output",
    scores: { precision: 0.8 },
  });
  await logger.flush();

  // Use global update_span
  let exportedSpan: string | undefined;
  await logger.traced(
    async (span) => {
      span.log({
        input: "global input",
        output: "global output",
      });
      exportedSpan = await span.export();
    },
    { event: { id: "global_update_span" } },
  );
  await logger.flush();

  if (!exportedSpan) {
    throw new Error("exportedSpan is undefined");
  }
  updateSpan({
    exported: exportedSpan,
    output: "globally updated output",
  });
  await experiment.flush();

  console.log("Test completed. Check the Braintrust UI for results.");
}

main();
