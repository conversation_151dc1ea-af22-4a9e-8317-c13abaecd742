[{"context": {}, "expected": null, "id": "fbe6111825636ee4fdccaadbe078b98d38d0c06c4f6882ad7092c06163663c8d", "input": "foo", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "fbe6111825636ee4fdccaadbe078b98d38d0c06c4f6882ad7092c06163663c8d", "scores": {}, "span_attributes": {"name": "root", "type": "eval"}, "span_id": "fbe6111825636ee4fdccaadbe078b98d38d0c06c4f6882ad7092c06163663c8d", "span_parents": null, "tags": null}]