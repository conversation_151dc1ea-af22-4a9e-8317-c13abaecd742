import * as braintrust from "braintrust";
import { Factuality } from "autoevals";

async function runEvaluation() {
  const experiment = braintrust.init("<PERSON> Hi Bot"); // Replace with your project name
  const dataset = [{ input: "<PERSON>", expected: "<PERSON> <PERSON>" }]; // Replace with your eval dataset

  const promises = [];
  for (const { input, expected } of dataset) {
    // You can await here instead to run these sequentially
    promises.push(
      experiment.traced(async (span) => {
        const output = "Hi David"; // Replace with your LLM call

        const { name, score } = await Factuality({ input, output, expected });

        span.log({
          input,
          output,
          expected,
          scores: {
            [name]: score,
          },
          metadata: { type: "Test" },
        });
      }),
    );
  }
  await Promise.all(promises);

  const summary = await experiment.summarize();
  if (!summary.experimentId) {
    throw new Error("experimentId is undefined in summarize() result");
  }
  console.log(summary);
  return summary;
}

runEvaluation();
