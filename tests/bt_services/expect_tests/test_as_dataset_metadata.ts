/* eslint-disable @typescript-eslint/no-explicit-any */
// This test file uses mock data to verify metadata propagation in asDataset() method

// Mock implementation matching the real asDataset behavior
async function* mockAsDataset(events: any[]): AsyncGenerator<any> {
  for (const record of events) {
    if (record.root_span_id !== record.span_id) {
      continue;
    }

    const { output, expected: expectedRecord, metadata } = record;
    const expected = expectedRecord ?? output;

    const baseCase: any = {
      input: record.input,
      tags: record.tags,
    };

    if (metadata !== undefined && metadata !== null) {
      baseCase.metadata = metadata;
    }

    if (!isEmpty(expected)) {
      baseCase.expected = expected;
    }

    yield baseCase;
  }
}

function isEmpty(value: any): boolean {
  return (
    value === null ||
    value === undefined ||
    (typeof value === "object" && Object.keys(value).length === 0) ||
    (Array.isArray(value) && value.length === 0)
  );
}

async function main() {
  const mockEvents = [
    // Root span with metadata
    {
      id: "1",
      _xact_id: "xact1",
      root_span_id: "span1",
      span_id: "span1",
      input: { question: "What is 2+2?" },
      output: "4",
      expected: "4",
      tags: ["math", "simple"],
      metadata: { model: "gpt-4", temperature: 0.5 },
    },
    // Non-root span (should be filtered out)
    {
      id: "2",
      _xact_id: "xact2",
      root_span_id: "span1",
      span_id: "span2",
      input: { context: "Math problem" },
      output: "Processing...",
    },
    // Root span without metadata
    {
      id: "3",
      _xact_id: "xact3",
      root_span_id: "span3",
      span_id: "span3",
      input: { question: "What is 5+5?" },
      output: "10",
      expected: undefined,
      tags: ["math"],
    },
    // Root span with metadata but no expected
    {
      id: "4",
      _xact_id: "xact4",
      root_span_id: "span4",
      span_id: "span4",
      input: { question: "What is 3+3?" },
      output: "6",
      expected: undefined,
      metadata: { model: "gpt-3.5", temperature: 0.7 },
    },
  ];

  const results = [];
  for await (const item of mockAsDataset(mockEvents)) {
    results.push(item);
  }

  // Should have 3 results (only root spans)
  if (results.length !== 3) {
    throw new Error(`Expected 3 items, got ${results.length}`);
  }

  // Sort by input to ensure consistent ordering
  results.sort((a, b) => {
    const aQuestion = a.input.question ?? "";
    const bQuestion = b.input.question ?? "";
    return aQuestion.localeCompare(bQuestion);
  });

  // First result should have metadata
  if (results[0].input.question !== "What is 2+2?") {
    throw new Error(
      `Expected first input to be "What is 2+2?", got "${results[0].input.question}"`,
    );
  }
  if (results[0].expected !== "4") {
    throw new Error(
      `Expected first expected to be "4", got "${results[0].expected}"`,
    );
  }
  if (
    JSON.stringify(results[0].metadata) !==
    JSON.stringify({ model: "gpt-4", temperature: 0.5 })
  ) {
    throw new Error(
      `Expected metadata for first item, got ${JSON.stringify(results[0].metadata)}`,
    );
  }

  // Second result should have metadata
  if (results[1].input.question !== "What is 3+3?") {
    throw new Error(
      `Expected second input to be "What is 3+3?", got "${results[1].input.question}"`,
    );
  }
  if (results[1].expected !== "6") {
    throw new Error(
      `Expected second expected to be "6", got "${results[1].expected}"`,
    );
  }
  if (
    JSON.stringify(results[1].metadata) !==
    JSON.stringify({ model: "gpt-3.5", temperature: 0.7 })
  ) {
    throw new Error(
      `Expected metadata for second item, got ${JSON.stringify(results[1].metadata)}`,
    );
  }

  // Third result should not have metadata
  if (results[2].input.question !== "What is 5+5?") {
    throw new Error(
      `Expected third input to be "What is 5+5?", got "${results[2].input.question}"`,
    );
  }
  if (results[2].expected !== "10") {
    throw new Error(
      `Expected third expected to be "10", got "${results[2].expected}"`,
    );
  }
  if (results[2].metadata !== undefined) {
    throw new Error(
      `Expected no metadata for third item, got ${JSON.stringify(results[2].metadata)}`,
    );
  }

  console.log("✅ All asDataset metadata propagation tests passed!");
}

main().catch((error) => {
  console.error("❌ Test failed:", error);
  process.exit(1);
});
