[{"context": {}, "expected": null, "id": "b1afa283eb514841e1f1b35a64ea737b5afd8fa48de89f1d56e822a701facaf4", "input": "global input", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "globally updated output", "root_span_id": "b1afa283eb514841e1f1b35a64ea737b5afd8fa48de89f1d56e822a701facaf4", "scores": null, "span_attributes": {"name": "root", "type": "task"}, "span_id": "b1afa283eb514841e1f1b35a64ea737b5afd8fa48de89f1d56e822a701facaf4", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "e59e442a36739a1d0e9055b36b7896cf6f4d1f3e78bbe2b4d66428219610fb83", "input": "initial input", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "updated output", "root_span_id": "e59e442a36739a1d0e9055b36b7896cf6f4d1f3e78bbe2b4d66428219610fb83", "scores": {"accuracy": 0.9}, "span_attributes": {"name": "root", "type": "eval"}, "span_id": "e59e442a36739a1d0e9055b36b7896cf6f4d1f3e78bbe2b4d66428219610fb83", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "32b4fd86b3792801af5cf030ce62a84fd5b0efeb0e74a1a346299a87fd07c97b", "input": "logger input", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": "updated logger output", "root_span_id": "32b4fd86b3792801af5cf030ce62a84fd5b0efeb0e74a1a346299a87fd07c97b", "scores": {"precision": 0.8}, "span_attributes": {"name": "root", "type": "task"}, "span_id": "32b4fd86b3792801af5cf030ce62a84fd5b0efeb0e74a1a346299a87fd07c97b", "span_parents": null, "tags": null}]