[{"context": {}, "expected": "b", "id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0", "input": "a", "is_root": false, "metadata": {"message": "In task"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": "d", "id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "input": "c", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c", "input": "c", "is_root": false, "metadata": {"message": "In task"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c", "span_parents": ["28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4"], "tags": null}, {"context": {}, "expected": "f", "id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "input": "e", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "f", "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "80f3bb11cb4154e630bdffba199f645474cee3f54d11be1605da5826041c0fa7", "input": "e", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "f", "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "80f3bb11cb4154e630bdffba199f645474cee3f54d11be1605da5826041c0fa7", "span_parents": ["6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e"], "tags": null}, {"context": {}, "expected": null, "id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.333333}, "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": {"Levenshtein": 0.333333}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "23ff7c384f7f1cc77e1ea6a4bef036b2b00f1a9bcaa1383d9df66d6173abc7cb", "span_parents": ["26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6"], "tags": null}, {"context": {}, "expected": null, "id": "4bcf955faac5e773ce2180c020be720644e521f774de540db26cda8435350b2e", "input": {"expected": "d", "input": "c", "metadata": {}, "output": "bar"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "4bcf955faac5e773ce2180c020be720644e521f774de540db26cda8435350b2e", "span_parents": ["28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4"], "tags": null}, {"context": {}, "expected": null, "id": "7388c31b954513fb55247ef5a9754fb6b17f390ff2651e502ffd794591d9e7f1", "input": {"expected": "f", "input": "e", "metadata": {}, "output": "f"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "7388c31b954513fb55247ef5a9754fb6b17f390ff2651e502ffd794591d9e7f1", "span_parents": ["6542c20602e2811513084036fc8db6678615735478178d5b875510292f9de19e"], "tags": null}, {"context": {}, "expected": null, "id": "eb33b66e4618f7c428b075aab3a6906b9a4600e707067292725f6a16a12b14e8", "input": {}, "is_root": false, "metadata": {"message": "In foo"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "26b8829e249156761d7058a7cc52aa620fe11d8580d0980790dd5929619273d6", "scores": null, "span_attributes": {"name": "foo", "type": "function"}, "span_id": "eb33b66e4618f7c428b075aab3a6906b9a4600e707067292725f6a16a12b14e8", "span_parents": ["95f5559a19b4d9ee1eb5fa25d9b502f80fec423ef5591aec63fc5feeaa3223f0"], "tags": null}, {"context": {}, "expected": null, "id": "eb33b66e4618f7c428b075aab3a6906b9a4600e707067292725f6a16a12b14e8", "input": {}, "is_root": false, "metadata": {"message": "In foo"}, "metrics": {}, "origin": null, "output": "bar", "root_span_id": "28f32203c4d1934f4a47dc76d6a5299c3d8f7999143e992c29bfe69dcd54fbb4", "scores": null, "span_attributes": {"name": "foo", "type": "function"}, "span_id": "eb33b66e4618f7c428b075aab3a6906b9a4600e707067292725f6a16a12b14e8", "span_parents": ["0b24aa440deac8248bd0787b5c55d12375ce96d566e57e190ba9fb12847a2b1c"], "tags": null}]