import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";
import * as lancedb from "@lancedb/lancedb";
import path from "path";
import fs from "fs";
import os from "os";

Eval("Say Hi Bot", {
  data: async () => {
    const tmpDir = fs.mkdtempSync(path.join(os.tmpdir(), "lancedb-test-"));
    const db = await lancedb.connect(tmpDir);
    const _table = await db.createTable(
      "vectors",
      [
        { id: 1, vector: [0.1, 0.2], item: "foo", price: 10 },
        { id: 2, vector: [1.1, 1.2], item: "bar", price: 50 },
      ],
      { mode: "overwrite" },
    );

    return [
      {
        input: "<PERSON>",
        expected: "Hi David",
      },
    ];
  },
  task: async (input) => {
    return "Hi " + input;
  },
  scores: [Levenshtein],
});
