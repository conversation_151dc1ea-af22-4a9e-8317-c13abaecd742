import { Eval } from "braintrust";
import { <PERSON><PERSON>hteinScorer } from "autoevals";

const TRIALS = 4;

const INPUTS = [...Array(5).keys()];
const OUTPUTS = INPUTS.map(() => [...Array(TRIALS).keys()]);
const EXPECTED = [...Array(INPUTS.length).keys()];

function getOutput(input: number) {
  return `${OUTPUTS[input].pop()}`;
}

function emptyOutputs({
  input,
  output,
  expected,
  metadata,
}: {
  input: number;
  output: string;
  expected: string;
  metadata: { score: number };
}) {
  for (const outputs of OUTPUTS) {
    if (outputs.length > 0) {
      throw new Error("Outputs were not empty");
    }
  }

  return { name: "empty_outputs", score: metadata.score };
}

Eval("basic_trial", {
  data: () =>
    INPUTS.map((input, i) => ({
      input,
      expected: `${EXPECTED[i]}`,
      metadata: { score: 0.5 },
    })),
  task: getOutput,
  scores: [LevenshteinScorer, emptyOutputs],
  trialCount: TRIALS,
});
