[{"context": {}, "expected": "0", "id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "span_parents": null, "tags": null}, {"context": {}, "expected": "0", "id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "input": 0, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "21962ea397c01264735af6575c702b8f25c0c56384971eae1586abeba0409860", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "5a1e08113f049d284b6bc2af2ae8f27cf5f7c4c3380cd3b4e098264354273791", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f15035e8bbdebad7243f3a3c99912209cd4bf42e2f2e62068bb747e06be12d0f", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "input": 0, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ff1ea3ac5a10d8b781b7c67be309b075d10ffe7221e39579fba4d84b40833226", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": "1", "id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "span_parents": null, "tags": null}, {"context": {}, "expected": "1", "id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "input": 1, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "980d2e6af8c2ab5bdc8e6d799145d4c96acc678b26752ea382e1e3f3c93838f6", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a21db3561ccf0953c658318b3135733a9bf3cc8ee7fa6bbf7fc8ace5e131abb6", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "b282b3d7e856a8b5b54c54d11f9e36145ab7de0c39369461cbb7073e3f9a9ede", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": "2", "id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "span_parents": null, "tags": null}, {"context": {}, "expected": "2", "id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "input": 2, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "1b74a8906118d7c915f3156cb8ae6b3f3e05be0c14051fe9d779bc329c8c320d", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "457d2321170664e363982c6921584b53d421ba3d036d6886ae7ac213f21a5985", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ef53ade65ae10a66bc560f4e6245734337bac9b79fb0e7baf891ef4628649830", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": "3", "id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "span_parents": null, "tags": null}, {"context": {}, "expected": "3", "id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "input": 3, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3ff69966c594df1dde709ecbe6243f87758f4fc5b1469fa307009ef321782640", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "d5de2598c21bf02f50bdc16bd4985f3f08d83a49c793d6f6e4681ca3d4e70c63", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "dcc10745db439101abbbbec90e463a97da374c015171be2c30cacdc841a74bd4", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "fbe6fcb2b355fb379950a391d3e37a58703db6d698eb46c28c3aecbf9150116d", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": "4", "id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "1", "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "0", "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "span_parents": null, "tags": null}, {"context": {}, "expected": "4", "id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "input": 4, "is_root": true, "metadata": {"score": 0.5}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "1", "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2c260441a2249be33cd47f1c462a0405ef3c5e99b070ad9c703739a423476838", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "78175d1a0514155c780b7ca9ce5783ee696ee68155620fb572a089c3c63e1231", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "e1286a5d0af9e89aedc047e11f5cfb104e2453e95bae526f4641b59e6705ec08", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}, {"context": {}, "expected": null, "id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "input": 4, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "0", "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ed7d3b757bfe087dd037636378b659293f31f5a48ab93cab0ab7a0b02f201413", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "d952e71a2fb28fcdc04ba4bddd8691f3156f2e6a427f119b726b44dd40ae87ee", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "d952e71a2fb28fcdc04ba4bddd8691f3156f2e6a427f119b726b44dd40ae87ee", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": null, "id": "f86544751201933f94c936f79cf9fb28d3d98022fd0eed95cf96c69908219de2", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "f86544751201933f94c936f79cf9fb28d3d98022fd0eed95cf96c69908219de2", "span_parents": ["7f6bb2fc67e570c27a968794d8ad8c45fd28034bf84a368f655da474a1fa9c1a"], "tags": null}, {"context": {}, "expected": null, "id": "35215b23b78b2c756f8bfcf3e9cd3f1e9221ae72251e3d31a72e7768c420b9ec", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "35215b23b78b2c756f8bfcf3e9cd3f1e9221ae72251e3d31a72e7768c420b9ec", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "7d7367e821b5547145b79241b62d6b7c6dd7decc10b63cea2ded31c3ca6d93e1", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "7d7367e821b5547145b79241b62d6b7c6dd7decc10b63cea2ded31c3ca6d93e1", "span_parents": ["43e39238b06894b3185ebb012ecb2a004bf6110125ba7ea5d7cbe44294d14f3d"], "tags": null}, {"context": {}, "expected": null, "id": "5560d4bfd7fef957586a3d3ba30f4c93f7cbb30c754d299cb3fba16c68754b72", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "5560d4bfd7fef957586a3d3ba30f4c93f7cbb30c754d299cb3fba16c68754b72", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "9765f45f87bce34aa7149fb75279359d55263d3a6bf09cdadc882fd138b9b3e4", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "9765f45f87bce34aa7149fb75279359d55263d3a6bf09cdadc882fd138b9b3e4", "span_parents": ["76efb8837513a1e5bc67f08b1076f36f09f77990e7cb0bead8f78dd1a1dad330"], "tags": null}, {"context": {}, "expected": null, "id": "00c568b42f3d90bbf7a7bf373351ce99d5efacb96bee4bde5934c32b5ac02322", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "00c568b42f3d90bbf7a7bf373351ce99d5efacb96bee4bde5934c32b5ac02322", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "ddc40139cdc714b4c178d576c23b649bcb79797a0084e67c40ebfa966dadf5b0", "input": {"expected": "0", "input": 0, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "ddc40139cdc714b4c178d576c23b649bcb79797a0084e67c40ebfa966dadf5b0", "span_parents": ["fa60296fd03048a738f3a2a900747d7d2d45e067c68281d4fb007ae31b947adb"], "tags": null}, {"context": {}, "expected": null, "id": "99c0a9f912e08c90b72f15ee861cca24d69a51615e0d737ae06761f63ada9954", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "99c0a9f912e08c90b72f15ee861cca24d69a51615e0d737ae06761f63ada9954", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": null, "id": "ee0582b8bb3179ef3d6bcd11be41d6210de9c956a50c5fee6b82b3f16afa897d", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "ee0582b8bb3179ef3d6bcd11be41d6210de9c956a50c5fee6b82b3f16afa897d", "span_parents": ["9cd863338b842cdf8080f94e060c04a405cd1df3e9d0f6a4d854ed1ecd55addf"], "tags": null}, {"context": {}, "expected": null, "id": "b42f1cef6cdf78dd42f022289c19ec7bfcacfb816dad8c48eca9001152cd60da", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "b42f1cef6cdf78dd42f022289c19ec7bfcacfb816dad8c48eca9001152cd60da", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "d9230796e72db1a1794ea18855deedbe9af174d2bdf89afb0a8e5911885ffe6f", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "d9230796e72db1a1794ea18855deedbe9af174d2bdf89afb0a8e5911885ffe6f", "span_parents": ["c05c5c72c911c9e18fe657e19511138b4508b08f0e94b86230704d22df90d5cd"], "tags": null}, {"context": {}, "expected": null, "id": "4b3a421cab11990e62e6b83497c63f5d269161317f308015c41ed0ce9ae5bde3", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "4b3a421cab11990e62e6b83497c63f5d269161317f308015c41ed0ce9ae5bde3", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "ce28c6caa5bb15cb3e7db767c260a0987a5c20db95e33dcefea19f2914eb3166", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "ce28c6caa5bb15cb3e7db767c260a0987a5c20db95e33dcefea19f2914eb3166", "span_parents": ["a32ab12486ba22895b9b92fb8bb86282ca28d6a93109a422d2d80127c6bba855"], "tags": null}, {"context": {}, "expected": null, "id": "7b9db5183b3478957541c4274bc29cf09409126c40d0238991d0684a8a0abbe6", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "7b9db5183b3478957541c4274bc29cf09409126c40d0238991d0684a8a0abbe6", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "f186c73603ca826f8e837f7d497870ad2b3ef7024e4e5f9da487bd30efac4ace", "input": {"expected": "1", "input": 1, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "f186c73603ca826f8e837f7d497870ad2b3ef7024e4e5f9da487bd30efac4ace", "span_parents": ["141ad52dea0f06799657159f84fb2c3c29d422f0b100c5bc9293281ef422ee53"], "tags": null}, {"context": {}, "expected": null, "id": "1ea43c86a42690b4eb624eb50307c6a8798ee1aa781c13fee00eb8bbae122690", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1ea43c86a42690b4eb624eb50307c6a8798ee1aa781c13fee00eb8bbae122690", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "726c7f3a983b7b814e30217594613f32a71f2738be46316c2da6d6105183df8e", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "726c7f3a983b7b814e30217594613f32a71f2738be46316c2da6d6105183df8e", "span_parents": ["32f4e521eb2f8fdd05ec18b19356a6150a7c3b2ed34f38505370b261744ba818"], "tags": null}, {"context": {}, "expected": null, "id": "4c9074cb27b12236938f44dae67a456714b41c5a2feb9b8831277025cf8a9ffc", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "4c9074cb27b12236938f44dae67a456714b41c5a2feb9b8831277025cf8a9ffc", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": null, "id": "f390627f67836a965a2a073a784de1b07598fd7764f4755419549d3713fd375d", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "f390627f67836a965a2a073a784de1b07598fd7764f4755419549d3713fd375d", "span_parents": ["f6efecb89a8c4ec7fbb81c1c24eeb5d77e30273273a64b7ec4ec24ea03ceb9f1"], "tags": null}, {"context": {}, "expected": null, "id": "77e6e958d3767ad5e2a88c750ae73a62ee267433f0d8297eeb7faf2e1d17b2bb", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "77e6e958d3767ad5e2a88c750ae73a62ee267433f0d8297eeb7faf2e1d17b2bb", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "a3dc98825855cd88ce5e68f259a03ca3042e904a1538a2ae880f54c29520f08c", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "a3dc98825855cd88ce5e68f259a03ca3042e904a1538a2ae880f54c29520f08c", "span_parents": ["386c083084d1c0b08292302d0ae79b6a4cdfdfcc344c5f0d27f2cfaf22385acb"], "tags": null}, {"context": {}, "expected": null, "id": "eb4257cd28e7d749ddf9a11366b9ea0a7d477d7b9b582e7c0a2866a579b63ec2", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "eb4257cd28e7d749ddf9a11366b9ea0a7d477d7b9b582e7c0a2866a579b63ec2", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "f9b047cc11aa1f07e7450cb802f547322e716751bb18ced626f083b9eecfe8e6", "input": {"expected": "2", "input": 2, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "f9b047cc11aa1f07e7450cb802f547322e716751bb18ced626f083b9eecfe8e6", "span_parents": ["8ce6986f06f2d0b6b1e212e9328f174a875e13f4e132ee96effcb662e3f57225"], "tags": null}, {"context": {}, "expected": null, "id": "7607b8f7feb3043661085adb32e22d48f96ad929dce3677ecd2f367fe0e635ea", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "7607b8f7feb3043661085adb32e22d48f96ad929dce3677ecd2f367fe0e635ea", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "e700b5f72bc7b1048f6e37b938b5f8ec3508d963d13ca3fc505922fd5a61ae4d", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "e700b5f72bc7b1048f6e37b938b5f8ec3508d963d13ca3fc505922fd5a61ae4d", "span_parents": ["46030880649e1a4f44a1886095657977ebbeac2b155d41ebb323dce8008445c7"], "tags": null}, {"context": {}, "expected": null, "id": "993acf5c32a175018cd6c6dbb2d62019d67136ee9869a16bf6598b928f3c481f", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "993acf5c32a175018cd6c6dbb2d62019d67136ee9869a16bf6598b928f3c481f", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": null, "id": "c87f9b0fd128610fd4341b78538f704b2e320e0e415608475ae44a781570f8fb", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "c87f9b0fd128610fd4341b78538f704b2e320e0e415608475ae44a781570f8fb", "span_parents": ["d4112ff4f14cb4d650aa22e1ca57d738339b09fc63e83d32c45a85d29b73b98e"], "tags": null}, {"context": {}, "expected": null, "id": "2a23e3eed4d5ff12cc067d926451a7acbd162c133492ed55b221a156f44c7e91", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a23e3eed4d5ff12cc067d926451a7acbd162c133492ed55b221a156f44c7e91", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "66066e88b8af3b8dd5e15c9e27dd744ba363885e8311a729339b2907401e3535", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "66066e88b8af3b8dd5e15c9e27dd744ba363885e8311a729339b2907401e3535", "span_parents": ["0a5bf30e88559a6a18261ddc0667512e11d12ea67c8f195f2a21b4779bf05585"], "tags": null}, {"context": {}, "expected": null, "id": "22eb63ab638ff4fa6da5f8da00bf8743dbde78d53b3c11c12dfb258e1a7021c5", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "22eb63ab638ff4fa6da5f8da00bf8743dbde78d53b3c11c12dfb258e1a7021c5", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "d8df175dfb0299730664a09ed9292722333b7b315b413d379aa1c2d254a4a6bc", "input": {"expected": "3", "input": 3, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "d8df175dfb0299730664a09ed9292722333b7b315b413d379aa1c2d254a4a6bc", "span_parents": ["7edfb128bfbd177eaa0633eab80e19ae28adf9646d5d0d1b5c4b12f2941699f1"], "tags": null}, {"context": {}, "expected": null, "id": "405df7d821645e4e71f2be7818ec7c3ede7a3a53335eea793bf4422616eed993", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "405df7d821645e4e71f2be7818ec7c3ede7a3a53335eea793bf4422616eed993", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "d87721895025601b586630475adfc2ca11eae743e3f41124e318e3b470c140d1", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "0"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "d87721895025601b586630475adfc2ca11eae743e3f41124e318e3b470c140d1", "span_parents": ["ec1e5990a6bd7235cd5abf6e5b6822cd8771f52a756090660dda0cb96db0d60a"], "tags": null}, {"context": {}, "expected": null, "id": "0b199f394b240abddd133becc80942e63cf81da49c48cc26eaf7228ffd245e70", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "0b199f394b240abddd133becc80942e63cf81da49c48cc26eaf7228ffd245e70", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "8bce304c722eff7adf45ddb220b5f554fd8c45a4a6a20455f0eb61c9a96ea7df", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "1"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "8bce304c722eff7adf45ddb220b5f554fd8c45a4a6a20455f0eb61c9a96ea7df", "span_parents": ["b4bda0efe0aa7be5a82d4303eed86623f56c126dffe4cd4a87dc99a08999ec6d"], "tags": null}, {"context": {}, "expected": null, "id": "18bae1240f84160bd6bf87c6bd25386c29eeeb8c42d0da66859e8ea72a8d08a5", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "18bae1240f84160bd6bf87c6bd25386c29eeeb8c42d0da66859e8ea72a8d08a5", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "de3139d91984a84c602f09298891f868d0ed1f3be7ad6e4f6b34ce969a3b4973", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "de3139d91984a84c602f09298891f868d0ed1f3be7ad6e4f6b34ce969a3b4973", "span_parents": ["604e14bc84ff06b3c2a095258448b1c2376d4d35c06690190c30ece5ccc8b719"], "tags": null}, {"context": {}, "expected": null, "id": "2ebc964776f6bd9da224e5bfa5080987813fc7210ddeb679c7e5be073ec4955e", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": {"Levenshtein": 0}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2ebc964776f6bd9da224e5bfa5080987813fc7210ddeb679c7e5be073ec4955e", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}, {"context": {}, "expected": null, "id": "c3312d554ed482c7d207f5e93faee0852b028b3616049f2c98bd868631652379", "input": {"expected": "4", "input": 4, "metadata": {"score": 0.5}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0", "scores": {"empty_outputs": 0.5}, "span_attributes": {"name": "empty_outputs", "type": "score"}, "span_id": "c3312d554ed482c7d207f5e93faee0852b028b3616049f2c98bd868631652379", "span_parents": ["f4ad1f4984e66809fa70cba90e47b48d5ae02ad25220bd9a60069072476ac1d0"], "tags": null}]