[{"context": {}, "expected": null, "id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "prompt": {"variables": {"formula": "1+1"}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 12, "prompt_tokens": 18, "tokens": 30}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "The sum of 1 + 1 is 2.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "prompt": {"variables": {"formula": "1+1"}}, "provider": "openai"}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 12, "prompt_tokens": 18, "tokens": 30}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "The sum of 1 + 1 is 2.", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "427e50276d86cb79311bef6bef8156fa597b291a0d05fde20c43be46fce6ed38", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 1, "prompt_tokens": 18, "tokens": 19}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "2", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "provider": "openai"}, "metrics": {"completion_tokens": 1, "prompt_tokens": 18, "tokens": 19}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "2", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "4466b59d72dbe96a730eb39f1bc34b29764dc4a855e2cd0313b17cfcad78bb25", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "79655a819059c7e7b06656b6bc9e5eba11534491f74859fda2a160c19b67062c", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "prompt": {"variables": {"input": "1+1"}}, "provider": "openai", "stream": true, "stream_options": {"include_usage": true}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 7, "prompt_tokens": 18, "tokens": 25}, "origin": null, "output": [{"finish_reason": null, "index": 0, "logprobs": null, "message": {"content": "1 + 1 = 2", "role": "assistant", "tool_calls": null}}], "root_span_id": "79655a819059c7e7b06656b6bc9e5eba11534491f74859fda2a160c19b67062c", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "79655a819059c7e7b06656b6bc9e5eba11534491f74859fda2a160c19b67062c", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "7dcfe86833fdff88dc562e38302f7c4a01511d601f8bec18ff67ed1452d109f2", "input": [{"content": "You are a calculator", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "prompt": {"variables": {"formula": "1+1"}}, "provider": "openai", "tools": [{"function": {"description": "Returns the sum of two numbers.", "name": "add", "parameters": {"properties": {"a": {"description": "The first number", "type": "number"}, "b": {"description": "The second number", "type": "number"}}, "required": ["a", "b"], "type": "object"}}, "type": "function"}]}, "metrics": {"completion_tokens": 17, "prompt_tokens": 67, "tokens": 84}, "origin": null, "output": [{"finish_reason": "tool_calls", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": null, "function_call": null, "refusal": null, "role": "assistant", "tool_calls": [{"function": {"arguments": "{\"a\":1,\"b\":1}", "name": "add"}, "id": "call_ttyvahaxIfOn4pS8fd5NtD7J", "type": "function"}]}}], "root_span_id": "7dcfe86833fdff88dc562e38302f7c4a01511d601f8bec18ff67ed1452d109f2", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "7dcfe86833fdff88dc562e38302f7c4a01511d601f8bec18ff67ed1452d109f2", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "b4f7c35fc805cfcd1e763b5ffcd5c8731cd722a9d0a7390f4559508b51b4a0cc", "input": [{"content": "You are a calculator. Return results in JSON", "role": "system"}, {"content": "1+1", "role": "user"}], "is_root": true, "metadata": {"model": "gpt-3.5-turbo", "prompt": {"variables": {"formula": "1+1"}}, "provider": "openai", "response_format": {"type": "json_object"}}, "metrics": {"completion_reasoning_tokens": 0, "completion_tokens": 9, "prompt_tokens": 23, "tokens": 32}, "origin": null, "output": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"annotations": null, "audio": null, "content": "{\n    \"result\": 2\n}", "function_call": null, "refusal": null, "role": "assistant", "tool_calls": null}}], "root_span_id": "b4f7c35fc805cfcd1e763b5ffcd5c8731cd722a9d0a7390f4559508b51b4a0cc", "scores": null, "span_attributes": {"name": "Chat Completion", "type": "llm"}, "span_id": "b4f7c35fc805cfcd1e763b5ffcd5c8731cd722a9d0a7390f4559508b51b4a0cc", "span_parents": null, "tags": null}]