import os

import braintrust
import requests

if __name__ == "__main__":
    logger = braintrust.init_logger(project="use-function-py")
    prompt_data = requests.post(
        f"{logger._get_state().api_url}/v1/prompt",
        json=dict(
            project_id=logger.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [
                        {"role": "system", "content": "You are a calculator. Return just the answer, nothing else."},
                        {"role": "user", "content": "{{formula}}"},
                    ],
                },
                "options": {"model": "gpt-4o"},
            },
            name="calculator",
            slug="calculator",
        ),
        headers=dict(Authorization=f"Bearer {os.environ['BRAINTRUST_API_KEY']}"),
    )
    braintrust.util.response_raise_for_status(prompt_data)

    result = braintrust.invoke(project_name=logger.project.name, slug="calculator", input=dict(formula="1 + 1"))
    assert result == "2"

    braintrust.invoke(
        project_name=logger.project.name,
        slug="calculator",
        input=dict(formula="2 + 1"),
        parent=logger,
    )

    with logger.start_span("parent span") as span:
        braintrust.invoke(
            project_name=logger.project.name,
            slug="calculator",
            input=dict(formula="3 + 1"),
        )

    with logger.start_span("extra message span") as span:
        braintrust.invoke(
            project_name=logger.project.name,
            slug="calculator",
            input=dict(formula="4 + 1"),
            messages=[
                {"role": "assistant", "content": "8"},
                {"role": "user", "content": "Are you sure?"},
            ],
        )

    with logger.start_span("stream span") as span:
        stream_result = braintrust.invoke(
            project_name=logger.project.name,
            slug="calculator",
            input=dict(formula="4 + 1"),
            stream=True,
        )
        span.log(input="A span!", output=stream_result)
        final_value = stream_result.final_value()
        assert final_value == "5", final_value

        final_value = stream_result.final_value()
        assert final_value == "5", final_value

    with logger.start_span("stream span") as span:
        stream_result = braintrust.invoke(
            project_name=logger.project.name,
            slug="calculator",
            input=dict(formula="4 + 1"),
            stream=True,
        )

        final_value = ""
        for chunk in stream_result:
            final_value += chunk.data

        span.log(input="A span!", output=stream_result)
        assert final_value == "5", final_value
