import { <PERSON><PERSON>htein } from "autoevals";
import { _internalGetGlobalState, Eval, login, setFetch } from "braintrust";

const BOGUS_APP_URL = "http://bogus-app";

let apiUrlHits = 0;
const myFetch: typeof globalThis.fetch = async (url, options) => {
  let effectiveUrl = url;
  if (typeof url === "string") {
    const urlObj = new URL(url);
    const apiUrl = _internalGetGlobalState().apiUrl;
    if (urlObj.host === new URL(BOGUS_APP_URL).host) {
      urlObj.host = new URL(process.env.BRAINTRUST_APP_URL!).host;
      effectiveUrl = urlObj.toString();
    } else if (apiUrl && urlObj.host === new URL(apiUrl).host) {
      apiUrlHits++;
    }
  }
  return await fetch(effectiveUrl, options);
};

async function main() {
  // First login with broken URLs
  try {
    await login({
      appUrl: BOGUS_APP_URL,
    });
    throw new Error("Expected login to fail");
  } catch {
    console.log("Login failed as expected");
  }

  setFetch(myFetch);

  const summary = await Eval("weird fetch", {
    data: [{ input: "1", expected: "2" }],
    task: () => "2",
    scores: [Levenshtein],
  });
  console.log(summary);

  if (apiUrlHits === 0) {
    throw new Error("Expected API URL to be hit");
  }
}

main();
