from braintrust import <PERSON><PERSON>

from autoevals import Levenshtein

Eval(
    "eval_update",
    data=[{"input": "1", "expected": 1}],
    task=lambda input: input,
    scores=[<PERSON><PERSON><PERSON><PERSON>],
    experiment_name="foobar",
    update=True,
)

Eval(
    "eval_update",
    data=[{"input": "1", "expected": 1}],
    task=lambda input: input,
    scores=[<PERSON><PERSON><PERSON>ein],
    experiment_name="foobar",
    update=True,
)
