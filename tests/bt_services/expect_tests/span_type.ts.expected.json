[{"context": {}, "expected": null, "id": "d560343fbe5409036898bc338ae25de28f5d210439065c0df7172b347da31e9d", "input": null, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "scores": null, "span_attributes": {"name": "foo:span_type.ts:4", "type": "llm"}, "span_id": "d560343fbe5409036898bc338ae25de28f5d210439065c0df7172b347da31e9d", "span_parents": ["ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd"], "tags": null}, {"context": {}, "expected": null, "id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "input": null, "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "scores": null, "span_attributes": {"name": "root", "type": "function"}, "span_id": "ebd782f5cf835a20ebf3115321c6e0e4638aebac6a6ede55770244068a7ab0bd", "span_parents": null, "tags": null}]