from typing import Mapping

from braintrust import Eval, init_dataset

from autoevals import NumericDiff


def add(input: Mapping[str, int]) -> int:
    return input["a"] + input["b"]


if __name__ == "__main__":
    ds = init_dataset("dataset_in_eval", "data")
    ds.insert(input={"a": 1, "b": 2}, expected=3)
    ds.insert(input={"a": 2, "b": 2}, expected=4)
    ds.flush()

    eval = Eval(
        name="dataset_in_eval",
        data=init_dataset("dataset_in_eval", "data"),
        task=add,
        scores=[NumericDiff],
    )
