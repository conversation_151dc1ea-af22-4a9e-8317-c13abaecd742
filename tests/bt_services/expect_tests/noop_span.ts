// Mirror of tests/bt_services/test_noop_span.py.
import * as braintrust from "braintrust";
import { ExperimentLogPartialArgs } from "braintrust";

function foo(x: number) {
  return braintrust.traced(
    () => {
      if (braintrust.currentSpan() !== braintrust.NOOP_SPAN) {
        throw new Error();
      }
      braintrust.currentSpan().log({ input: "hello" });
      braintrust.currentSpan().traced(
        (subspan) => {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- We need this to pass typechecks even though inputs is a legacy field
          subspan.log({ inputs: "goodbye" } as ExperimentLogPartialArgs);
        },
        { name: "subspan" },
      );
      braintrust.currentSpan().log({ output: "yes" });
      return x + 1;
    },
    { asyncFlush: true },
  );
}

function testBasic() {
  if (foo(9) !== 10) {
    throw new Error();
  }
}

async function testExportNoopSpan() {
  const noopExported = await braintrust.traced((noopSpan) => {
    if (noopSpan !== braintrust.NOOP_SPAN) {
      throw new Error();
    }
    return noopSpan.export();
  });

  braintrust.traced(
    (anotherNoopSpan) => {
      if (anotherNoopSpan !== braintrust.NOOP_SPAN) {
        throw new Error();
      }
    },
    { parent: noopExported, asyncFlush: true },
  );

  const experiment = braintrust.init("p");
  experiment.traced(
    (rootSpan) => {
      rootSpan.log({ input: "part of experiment" });
    },
    { parent: noopExported },
  );
  await experiment.flush();
}

async function main() {
  testBasic();
  await testExportNoopSpan();
}

main();
