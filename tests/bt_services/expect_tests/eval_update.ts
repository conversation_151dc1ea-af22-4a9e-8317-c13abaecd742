import { <PERSON><PERSON><PERSON><PERSON> } from "autoevals";
import { <PERSON><PERSON> } from "braintrust";

Eval("eval_update", {
  data: [{ input: "1", expected: "1" }],
  task: (input) => input,
  scores: [<PERSON><PERSON><PERSON><PERSON>],
  experimentName: "foobar",
  update: true,
});

Eval("eval_update", {
  data: [{ input: "1", expected: "1" }],
  task: (input) => input,
  scores: [<PERSON><PERSON><PERSON><PERSON>],
  experimentName: "foobar",
  update: true,
});
