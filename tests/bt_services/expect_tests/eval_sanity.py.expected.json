[{"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 2, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "631b3b12503c1eff71e32545cbc3c0c02dcc8b9e9071449ee347a95d2ffcf6e5", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "ad7ada087f870330cdb3e79198116dbd85048872bbc523cd82a54ec694dbb460", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 6, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ad7ada087f870330cdb3e79198116dbd85048872bbc523cd82a54ec694dbb460", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "ad7ada087f870330cdb3e79198116dbd85048872bbc523cd82a54ec694dbb460", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": 6, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "ad7ada087f870330cdb3e79198116dbd85048872bbc523cd82a54ec694dbb460", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "input": 3, "is_root": true, "metadata": {"scorer_errors": {"NumericDiff": ""}}, "metrics": {}, "origin": null, "output": 6, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "input": 3, "is_root": true, "metadata": {"scorer_errors": {"NumericDiff": ""}}, "metrics": {}, "origin": null, "output": 6, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"NumericDiff": 1}, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "01e48ccee55a0998cd8517e8ef5fd09edb1bd613d054b33c35d3c0d0374a5a95", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "7652921cb108db045e3966ae9c53cd1b85f4820f4dcd98d173f231a5b1f24934", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": true}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"sync_score": 1}, "span_attributes": {"name": "sync_score", "type": "score"}, "span_id": "7652921cb108db045e3966ae9c53cd1b85f4820f4dcd98d173f231a5b1f24934", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "7652921cb108db045e3966ae9c53cd1b85f4820f4dcd98d173f231a5b1f24934", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": true}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"sync_score": 1}, "span_attributes": {"name": "sync_score", "type": "score"}, "span_id": "7652921cb108db045e3966ae9c53cd1b85f4820f4dcd98d173f231a5b1f24934", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "b40dd5a1588f8694c603ba999645afe901dd070c39627688d252cc765a1704e8", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": true}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"anon_score": 1}, "span_attributes": {"name": "anon_score", "type": "score"}, "span_id": "b40dd5a1588f8694c603ba999645afe901dd070c39627688d252cc765a1704e8", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "expected": null, "id": "b40dd5a1588f8694c603ba999645afe901dd070c39627688d252cc765a1704e8", "input": {"expected": 2, "input": 1, "metadata": {}, "output": 2}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": true}, "root_span_id": "fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595", "scores": {"anon_score": 1}, "span_attributes": {"name": "anon_score", "type": "score"}, "span_id": "b40dd5a1588f8694c603ba999645afe901dd070c39627688d252cc765a1704e8", "span_parents": ["fd3a258fc91c7fa3ce8fb25f23d9da5bea4d86ba10cb064f0bce44e25c426595"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "2913d3603928e2365f8bc91e4b4823022b7fafac88e921527997cde854be952f", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "2913d3603928e2365f8bc91e4b4823022b7fafac88e921527997cde854be952f", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "error": "<has_error>", "expected": null, "id": "2913d3603928e2365f8bc91e4b4823022b7fafac88e921527997cde854be952f", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": null, "span_attributes": {"name": "NumericDiff", "type": "score"}, "span_id": "2913d3603928e2365f8bc91e4b4823022b7fafac88e921527997cde854be952f", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "d9d166f9310e66fae500902e7b4e5a98aecbc8838e95674f2cbe8aa02a31576d", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": false}, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": {"sync_score": 0}, "span_attributes": {"name": "sync_score", "type": "score"}, "span_id": "d9d166f9310e66fae500902e7b4e5a98aecbc8838e95674f2cbe8aa02a31576d", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "d9d166f9310e66fae500902e7b4e5a98aecbc8838e95674f2cbe8aa02a31576d", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": false}, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": {"sync_score": 0}, "span_attributes": {"name": "sync_score", "type": "score"}, "span_id": "d9d166f9310e66fae500902e7b4e5a98aecbc8838e95674f2cbe8aa02a31576d", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "eb920bf4fee1ca16da90797cb6303d1d3b2a7b40677d2349bc571b2c62b3f226", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": false}, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": {"anon_score": 0}, "span_attributes": {"name": "anon_score", "type": "score"}, "span_id": "eb920bf4fee1ca16da90797cb6303d1d3b2a7b40677d2349bc571b2c62b3f226", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}, {"context": {}, "expected": null, "id": "eb920bf4fee1ca16da90797cb6303d1d3b2a7b40677d2349bc571b2c62b3f226", "input": {"expected": null, "input": 3, "metadata": {}, "output": 6}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": false}, "root_span_id": "df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37", "scores": {"anon_score": 0}, "span_attributes": {"name": "anon_score", "type": "score"}, "span_id": "eb920bf4fee1ca16da90797cb6303d1d3b2a7b40677d2349bc571b2c62b3f226", "span_parents": ["df0a36403ba1588051433c5c83f62c1e881144df82a0476bb1875931f6ccfc37"], "tags": null}]