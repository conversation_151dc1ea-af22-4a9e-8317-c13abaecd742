[{"context": {}, "expected": null, "id": "aded3322494cd0548ed6038ebaa5acb033314145d1903fe5d393967e9240b07e", "input": "part of experiment", "is_root": true, "metadata": null, "metrics": {}, "origin": null, "output": null, "root_span_id": "aded3322494cd0548ed6038ebaa5acb033314145d1903fe5d393967e9240b07e", "scores": null, "span_attributes": {"name": "root", "type": "eval"}, "span_id": "aded3322494cd0548ed6038ebaa5acb033314145d1903fe5d393967e9240b07e", "span_parents": null, "tags": null}]