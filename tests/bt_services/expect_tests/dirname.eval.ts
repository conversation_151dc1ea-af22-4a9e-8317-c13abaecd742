// Check that __dirname and __filename work correctly in Eval functions.
import { Eval } from "braintrust";
import * as path from "node:path";

function checkDirname() {
  const basename = path.basename(__dirname);
  if (basename !== "expect_tests") {
    throw new Error(
      `Expected dirname ends with 'expect_tests'. Got ${__dirname}`,
    );
  }
}

function checkFilename() {
  const basename = path.basename(__filename);
  if (basename !== "dirname.eval.ts") {
    throw new Error(
      `Expected dirname ends with 'expect_tests'. Got ${__filename}`,
    );
  }
}

function task(_input: string) {
  checkDirname();
  checkFilename();
  return "a";
}

function scorer(_args: { input: string; output: string; expected?: string }) {
  checkDirname();
  checkFilename();
  return {
    name: "null scorer",
    score: null,
  };
}

Eval("basic-null", {
  data: () => [{ input: "a", expected: "b" }],
  task: task,
  scores: [scorer],
});
