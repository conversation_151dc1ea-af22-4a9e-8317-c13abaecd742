import { initLogger, wrap<PERSON>pen<PERSON><PERSON> } from "braintrust";
import OpenA<PERSON> from "openai";
import "@braintrust/proxy/types";

const logger = initLogger({ projectName: "Gemini multi-turn" });
const client = wrapOpenAI(new OpenAI());

// You'll need VERTEX_AI_API_KEY set with a temporary access token which you can get with `gcloud auth print-access-token | pbcopy`
// You'll also need GCP_PROJECT_ID set with the project ID
async function main() {
  await logger.traced(async (span) => {
    const ret = await client.chat.completions.create({
      model: "publishers/google/models/gemini-2.5-flash-preview-04-17", // regular gemini doesn't have reasoning details
      messages: [
        {
          role: "user",
          content: "What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun",
        },
        {
          role: "assistant",
          content:
            'The Greek name for the Sun is (B) Helio<PERSON>.\n\nIn Greek mythology, <PERSON><PERSON><PERSON> was the god who personified the Sun. He was depicted as driving a chariot across the sky from east to west each day. "Sol" is actually the Roman/Latin name for the Sun, while "Sun" is simply the English word.',
          reasoning: [
            {
              id: "ErUBCkYIAxgCIkCRelMix/UJucD2JrOwSoG6y6wDM+hESq637ICPrhqir5ffzA5LFxflKWnRIx6JxQTyRiC8EDr2+F7yFbOKo3t0Egzyn2xVZRLP2CNGHv8aDASopIiCUuC7Dlb7ziIwRdVJzSPedBMfT/fMGyO5jWueu00SJjk1JYZBYXPm8m7PNmMK0W8JfSbPc6HK3W5TKh30Dovx24zaOQegm7wWfAEG0coiPdPXJbgOy0uGhRgC",
              content:
                "I need to identify the Greek name for the Sun from the given options.\n\n(A) Sol - This is the Latin name for the Sun, used by the Romans.\n(B) Helios - This is the Greek name for the Sun. In Greek mythology, Helios was the god of the Sun who drove a chariot across the sky each day.\n(C) Sun - This is the English word for the Sun, not the Greek name.\n\nSo the correct answer is (B) Helios.",
            },
          ],
        },
        {
          role: "user",
          content: "What's the Latin name of the sun?",
        },
      ],
      reasoning_effort: "medium",
      stream: false,
    });

    console.log(ret);
    console.log(ret.choices[0].reasoning);

    return ret.choices[0].message.content;
  });

  await logger.traced(async (span) => {
    const ret = await client.chat.completions.create({
      model: "publishers/google/models/gemini-2.5-flash-preview-04-17",
      messages: [
        {
          role: "user",
          content: "What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun",
        },
        {
          role: "assistant",
          content:
            'The Greek name for the Sun is (B) Helios.\n\nIn Greek mythology, Helios was the god who personified the Sun. He was depicted as driving a chariot across the sky from east to west each day. "Sol" is actually the Roman/Latin name for the Sun, while "Sun" is simply the English word.',
          reasoning: [
            {
              content:
                "I need to identify the Greek name for the Sun from the given options.\n\n(A) Sol - This is the Latin name for the Sun, used by the Romans.\n(B) Helios - This is the Greek name for the Sun. In Greek mythology, Helios was the god of the Sun who drove a chariot across the sky each day.\n(C) Sun - This is the English word for the Sun, not the Greek name.\n\nSo the correct answer is (B) Helios.",
              id: "ErUBCkYIAxgCIkCRelMix/UJucD2JrOwSoG6y6wDM+hESq637ICPrhqir5ffzA5LFxflKWnRIx6JxQTyRiC8EDr2+F7yFbOKo3t0Egzyn2xVZRLP2CNGHv8aDASopIiCUuC7Dlb7ziIwRdVJzSPedBMfT/fMGyO5jWueu00SJjk1JYZBYXPm8m7PNmMK0W8JfSbPc6HK3W5TKh30Dovx24zaOQegm7wWfAEG0coiPdPXJbgOy0uGhRgC",
            },
          ],
        },
        {
          role: "user",
          content: "What's the Latin name of the sun?",
        },
      ],
      reasoning_effort: "medium",
      stream: true,
    });

    for await (const event of ret) {
      console.log(event);
    }
  });
}

main().catch(console.error);
