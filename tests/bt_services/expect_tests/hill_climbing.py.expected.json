[{"context": {}, "expected": 2, "id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "span_parents": null, "tags": null}, {"context": {}, "expected": 2, "id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "input": 1, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "input": 1, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "2", "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "76cf442f2ee4af2817a16ebffe5870e1da0078759c0e3c8b69e347561bef4eb6", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": 3, "id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "span_parents": null, "tags": null}, {"context": {}, "expected": 3, "id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "span_parents": null, "tags": null}, {"context": {}, "expected": 3, "id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "span_parents": null, "tags": null}, {"context": {}, "expected": 3, "id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "span_parents": null, "tags": null}, {"context": {}, "expected": 3, "id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "input": 2, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "input": 2, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "3", "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "3c8c950334107cf63cb648d280f9b6c0a8d0d2a06d8eb8a327ea119e7cf88c3f", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": 4, "id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "span_parents": null, "tags": null}, {"context": {}, "expected": 4, "id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "span_parents": null, "tags": null}, {"context": {}, "expected": 4, "id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "span_parents": null, "tags": null}, {"context": {}, "expected": 4, "id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "span_parents": null, "tags": null}, {"context": {}, "expected": 4, "id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "input": 3, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "input": 3, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "4", "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "f0882ec43830af46104da69920d296bbe7a5f83b5c6a79cb2aa20315eda90d41", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": 6, "id": "a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0", "input": 5, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "6", "root_span_id": "a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "a6026a700de5e6138c2a56452c6ce44743803fc4b3f1ba5eaaf85e5bdd17d21e", "input": 5, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "6", "root_span_id": "a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "a6026a700de5e6138c2a56452c6ce44743803fc4b3f1ba5eaaf85e5bdd17d21e", "span_parents": ["a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0"], "tags": null}, {"context": {}, "expected": 7, "id": "575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4", "input": 6, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "7", "root_span_id": "575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "13a21193e3b546faf8fb729d4fef026a4011664ec589ef9619058ff17ca17f23", "input": 6, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "7", "root_span_id": "575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "13a21193e3b546faf8fb729d4fef026a4011664ec589ef9619058ff17ca17f23", "span_parents": ["575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4"], "tags": null}, {"context": {}, "expected": 8, "id": "fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb", "input": 7, "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "8", "root_span_id": "fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "2cbfa4df8152516e6e0f4bf97b80c7b67a3c19a01add8f13689cbf06d412fd68", "input": 7, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "8", "root_span_id": "fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "2cbfa4df8152516e6e0f4bf97b80c7b67a3c19a01add8f13689cbf06d412fd68", "span_parents": ["fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb"], "tags": null}, {"context": {}, "expected": null, "id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "input": {"expected": 2, "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "input": {"expected": 2, "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "input": {"expected": 2, "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "input": {"expected": 2, "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "input": {"expected": 2, "input": 1, "metadata": {}, "output": "2"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "e2d37b0fbec8773df8a24bc8419e591445ac8c266e54727170ab04cc89623008", "span_parents": ["dead1f2c7e29aa27b2805660b2f051d1b5080407796d66d77e4bcc9045ada2c5"], "tags": null}, {"context": {}, "expected": null, "id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "input": {"expected": 3, "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "input": {"expected": 3, "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "input": {"expected": 3, "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "input": {"expected": 3, "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "input": {"expected": 3, "input": 2, "metadata": {}, "output": "3"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2a24f7970a21ed7a79c8cea650adba82035c621211f309135f3cb5cf9f6e3e17", "span_parents": ["cf4c0ff27e3d5e5c079e2ee6a1117dfb55fb0e39b3a28c9fb53682fe912c7290"], "tags": null}, {"context": {}, "expected": null, "id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "input": {"expected": 4, "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "input": {"expected": 4, "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "input": {"expected": 4, "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "input": {"expected": 4, "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "input": {"expected": 4, "input": 3, "metadata": {}, "output": "4"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "2c291aee736dec2248d0fdc382c2e4819b7fbb6a43d20481881114163f68e034", "span_parents": ["bd04ac31b0cf577e00871a7ea2086706b542d0d268aa1f74d191680c42e927b3"], "tags": null}, {"context": {}, "expected": null, "id": "fbac325a099609b7b823256bb4582e62faf69ce0526a2aaa8bb9895219adc61a", "input": {"expected": 6, "input": 5, "metadata": {}, "output": "6"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "fbac325a099609b7b823256bb4582e62faf69ce0526a2aaa8bb9895219adc61a", "span_parents": ["a042567b4a0681374cd6169e204a7a77685e5c94e013772a12193aa31f74bab0"], "tags": null}, {"context": {}, "expected": null, "id": "d080e863d46b8ce1f67c14cf172959cd189faf173cea0d51ee2835449c7baae5", "input": {"expected": 7, "input": 6, "metadata": {}, "output": "7"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "d080e863d46b8ce1f67c14cf172959cd189faf173cea0d51ee2835449c7baae5", "span_parents": ["575e7d27e90d86b9d1504918661046736073a53a90598b39a08a02b05c1f66c4"], "tags": null}, {"context": {}, "expected": null, "id": "1d62ecda44a1fc0db453944faea02fcf8b16812eabf82447c5743562659c3c8d", "input": {"expected": 8, "input": 7, "metadata": {}, "output": "8"}, "is_root": false, "metadata": {}, "metrics": {}, "origin": null, "output": {"score": 1}, "root_span_id": "fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb", "scores": {"Levenshtein": 1}, "span_attributes": {"name": "Levenshtein", "type": "score"}, "span_id": "1d62ecda44a1fc0db453944faea02fcf8b16812eabf82447c5743562659c3c8d", "span_parents": ["fa3e1035d7e695d0dde1bd958c69140714e253a5cfb07d38569bb4299e600cbb"], "tags": null}]