"""This test exercises all paths that we could log an attachment."""

import os

import braintrust
from braintrust import Attach<PERSON>, ReadonlyAttachment, init_dataset, init_logger

PROJECT_NAME = "attachment expect tests"
SCRIPT_DIR = os.path.dirname(__file__)
DATAFILES_DIR = os.path.join(SCRIPT_DIR, "../expect_test_datafiles")


def main():
    logger = init_logger(PROJECT_NAME, async_flush=True)

    image = Attachment(
        data=os.path.join(DATAFILES_DIR, "chaos.jpg"),
        filename="attachment.jpg",
        content_type="image/jpeg",
    )

    with open(os.path.join(DATAFILES_DIR, "landscape.jpg"), "rb") as f:
        image2 = Attachment(
            data=f.read(),
            filename="user_input.jpg",
            content_type="image/jpeg",
        )

    with open(os.path.join(DATAFILES_DIR, "strawberry.wav"), "rb") as f:
        audio = Attachment(
            data=bytearray(f.read()),
            filename="model response.wav",
            content_type="audio/wav",
        )

    text = Attachment(
        data=bytes("hello world", "utf-8"),
        filename="greeting.txt",
        content_type="text/plain",
    )
    assert text.data == b"hello world"

    # Basic log statement.
    id = logger.log(
        input={
            "question": "What is this?",
            "context": image,
        },
        output={
            "text": "text response",
            "audio": audio,
            "manyAttachments": [
                image,
                image2,
                audio,
                text,
                None,
                None,
                "string",
                42,
            ],
        },
    )

    braintrust.flush()

    # Test feedback.
    logger.log_feedback(
        id=id,
        scores={
            "metric1": 0.5,
            "metric2": 1,
        },
        expected=audio,
    )

    # Test span.
    span = logger.start_span()
    span.log(
        input={
            "text": "hello world",
            "image": image,
        }
    )
    span.log(input={"image2": image2})
    # Log nested attachment.
    span.log(input={"attachment": [{"file": text}]})
    braintrust.flush()

    span.log(output=text)
    span.log_feedback(expected=text)
    span.close()

    # Test dataset.
    dataset = init_dataset(PROJECT_NAME, name="dataset")

    dataset_id = dataset.insert(input=image, expected=image2)
    dataset.update(id=dataset_id, input=image, expected=image)

    braintrust.flush()

    dataset.update(id=dataset_id, input=image2, expected=image, metadata=dict(attachment=text))
    braintrust.flush()

    # Test reading dataset.
    dataset_for_reading = init_dataset(PROJECT_NAME, name="dataset")
    count = 0

    for record in dataset_for_reading:
        count += 1
        assert record["id"] == dataset_id

        assert "metadata" in record
        metadata = record["metadata"]
        assert metadata is not None
        attachment = metadata["attachment"]
        assert isinstance(attachment, ReadonlyAttachment)

        # Must be different instances.
        assert attachment != text

        assert attachment.data == b"hello world"
        assert attachment.status() == {"upload_status": "done"}
        assert attachment.reference == text.reference

        # Log the ReadonlyAttachment to make sure it shows up in the logs.
        logger.log(input=dict(count=count), output=dict(attachment=attachment), allow_concurrent_with_spans=True)

    assert count == 1


if __name__ == "__main__":
    main()
