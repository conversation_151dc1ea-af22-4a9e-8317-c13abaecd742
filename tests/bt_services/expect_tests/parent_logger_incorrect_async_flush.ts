import * as braintrust from "braintrust";

async function testRetrieveAsyncFlushFromNonAsyncFlush() {
  braintrust.initLogger({ projectName: "p", asyncFlush: false });
  let error: unknown = null;
  try {
    braintrust.currentLogger({ asyncFlush: true });
  } catch (e) {
    error = e;
  }
  if (!error) {
    throw new Error();
  }
}

async function testRetrieveNonAsyncFlushFromAsyncFlush() {
  braintrust.initLogger({ projectName: "p", asyncFlush: true });
  let error: unknown = null;
  try {
    braintrust.currentLogger({ asyncFlush: false });
  } catch (e) {
    error = e;
  }
  if (!error) {
    throw new Error();
  }
}

async function main() {
  await testRetrieveAsyncFlushFromNonAsyncFlush();
  await testRetrieveNonAsyncFlushFromAsyncFlush();
}

main();
