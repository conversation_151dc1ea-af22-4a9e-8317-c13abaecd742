import * as braintrust from "braintrust";
import * as fs from "node:fs/promises";
import * as os from "node:os";
import * as path from "node:path";
import * as uuid from "uuid";

async function main() {
  const rowId = uuid.v4();
  const inputStr = "foobar";
  const tmpdirFailed = path.join(os.tmpdir(), uuid.v4());
  const tmpdirAll = path.join(os.tmpdir(), uuid.v4());

  const logger = braintrust._internalGetGlobalState().httpLogger();
  logger.syncFlush = true;
  logger.failedPublishPayloadsDir = tmpdirFailed;
  logger.allPublishPayloadsDir = tmpdirAll;
  logger.log([
    new braintrust.LazyValue(async () => ({
      id: rowId,
      input: inputStr,
      experiment_id: "bogus",
      _is_merge: false,
    })),
  ]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  let error: any = undefined;
  try {
    await braintrust.flush();
  } catch (e) {
    error = e;
  }
  if (!error) {
    throw new Error();
  }

  const e = braintrust.init("p");
  e.log({ id: rowId, input: inputStr, output: inputStr, scores: {} });
  await braintrust.flush();

  let filenames = await fs.readdir(tmpdirFailed);
  if (filenames.length !== 1) {
    throw new Error();
  }
  let fileContents = await fs.readFile(path.join(tmpdirFailed, filenames[0]), {
    encoding: "utf-8",
  });
  if (!fileContents.includes(rowId) || !fileContents.includes(inputStr)) {
    throw new Error();
  }
  const failedFileContents = fileContents;

  filenames = await fs.readdir(tmpdirAll);
  if (filenames.length !== 2) {
    throw new Error();
  }
  const allFileContents = await Promise.all(
    filenames.map((f) =>
      fs.readFile(path.join(tmpdirAll, f), { encoding: "utf-8" }),
    ),
  );
  for (fileContents of allFileContents) {
    if (!fileContents.includes(rowId) || !fileContents.includes(inputStr)) {
      throw new Error();
    }
  }
  if (!allFileContents.includes(failedFileContents)) {
    throw new Error();
  }
}

main();
