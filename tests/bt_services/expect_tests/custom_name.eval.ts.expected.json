[{"context": {}, "expected": "b", "id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "input": "a", "is_root": true, "metadata": {}, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "eval", "type": "eval"}, "span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "span_parents": null, "tags": null}, {"context": {}, "expected": null, "id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "input": "a", "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": "a", "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": null, "span_attributes": {"name": "task", "type": "task"}, "span_id": "add2a573a710668668fd331e8ab24e5b6a6f8381cd391570823b5bc564442c77", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "070f4d4c1e49db89ce8ae665081558a6f7a781ecab5c384cfcbf0832448ba2ce", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"JSONDiff": 0}, "span_attributes": {"name": "scorer_2", "type": "score"}, "span_id": "070f4d4c1e49db89ce8ae665081558a6f7a781ecab5c384cfcbf0832448ba2ce", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "15f185e86d37db8d0013a9a74c4bb79a292a9ae16fed2883b0bcb38d1ad58147", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"JSONDiff": 0}, "span_attributes": {"name": "myScorer", "type": "score"}, "span_id": "15f185e86d37db8d0013a9a74c4bb79a292a9ae16fed2883b0bcb38d1ad58147", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}, {"context": {}, "expected": null, "id": "9f1eebb586bbe37ec734a27b747956fdf472775a403bd968fcef980b8a0c0a96", "input": {"expected": "b", "input": "a", "metadata": {}, "output": "a"}, "is_root": false, "metadata": null, "metrics": {}, "origin": null, "output": {"score": 0.5}, "root_span_id": "a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a", "scores": {"Custom scorer": 0.5}, "span_attributes": {"name": "myHandrolledScorer", "type": "score"}, "span_id": "9f1eebb586bbe37ec734a27b747956fdf472775a403bd968fcef980b8a0c0a96", "span_parents": ["a501150e94c86bed540b928ace60a8fbfd92bfc0f4f8465088fd51442713490a"], "tags": null}]