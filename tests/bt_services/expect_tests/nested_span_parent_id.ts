import "../expect_test_imports/nested_span_parent_id_setenv";
import * as braintrust from "braintrust";

async function main() {
  const experiment = braintrust.init("p");
  for (let flushLevel = 0; flushLevel < 5; ++flushLevel) {
    await experiment.traced(
      async (span1) => {
        span1.log({ input: "one" });
        if (flushLevel > 0) {
          await experiment.flush();
        }
        await experiment.traced(
          async (span2) => {
            span2.log({ input: "two" });
            if (flushLevel > 1) {
              await experiment.flush();
            }
            await span2.traced(
              async (span3) => {
                span3.log({ input: "three" });
                if (flushLevel > 2) {
                  await experiment.flush();
                }
                await experiment.traced(
                  async (span4) => {
                    span4.log({ input: "four" });
                    if (flushLevel > 3) {
                      await experiment.flush();
                    }
                  },
                  { parent: await span3.export(), name: "s4" },
                );
                await span3.traced(
                  async (span5) => {
                    span5.log({ input: "five" });
                    if (flushLevel > 4) {
                      await experiment.flush();
                    }
                  },
                  { name: "s5" },
                );
              },
              { name: "s3" },
            );
          },
          { parent: await span1.export(), name: "s2" },
        );
      },
      { name: "s1" },
    );
  }
  console.log(await experiment.summarize());
}

main();
