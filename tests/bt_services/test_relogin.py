import os
from uuid import uuid4

import braintrust

from tests.braintrust_app_test_base import LOCAL_APP_URL, BraintrustAppTestBase


class ReLogin(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.extra_org_id, self.extra_org_name = self.createOrg()
        _, __, self.extra_org_api_key = self.createUserInOrg(self.extra_org_id)

    def tearDown(self):
        BraintrustAppTestBase.tearDownDb(self.extra_org_id)
        super().tearDown()

    def test_no_login(self):
        self.assertEqual(braintrust.org_id(), self.org_id)
        # Trying to login with new credentials should raise an exception, unless
        # we pass force_login=True.
        with self.assertRaisesRegex(Exception, "Re-logging in with different"):
            braintrust.login(
                org_name=self.extra_org_name,
                app_url=LOCAL_APP_URL,
                api_key=self.extra_org_api_key,
            )
        braintrust.login(
            org_name=self.extra_org_name, app_url=LOCAL_APP_URL, api_key=self.extra_org_api_key, force_login=True
        )
        self.assertEqual(braintrust.org_id(), self.extra_org_id)

    def test_app_public_url(self):
        os.environ["BRAINTRUST_APP_PUBLIC_URL"] = "foo:bar"
        braintrust.login(
            org_name=self.extra_org_name,
            app_url=LOCAL_APP_URL,
            api_key=self.extra_org_api_key,
            force_login=True,
        )
        experiment = braintrust.init(project="foo:bar")
        summary = experiment.summarize()
        self.assertTrue("foo:bar" in summary.project_url, summary.project_url)

    def test_quotes_in_api_key(self):
        braintrust.login(
            org_name=self.extra_org_name,
            app_url=LOCAL_APP_URL,
            api_key='"' + self.extra_org_api_key + '"',
            force_login=True,
        )

        braintrust.login(
            org_name=self.extra_org_name,
            app_url=LOCAL_APP_URL,
            api_key=" " + self.extra_org_api_key + " ",
            force_login=True,
        )

        braintrust.login(
            org_name=self.extra_org_name,
            app_url=LOCAL_APP_URL,
            api_key=" '" + self.extra_org_api_key + "' \t\t",
            force_login=True,
        )

        # If there's a space in the quotes, then it should actually fail
        self.assertRaises(
            Exception,
            lambda: braintrust.login(
                org_name=self.extra_org_name,
                app_url=LOCAL_APP_URL,
                api_key='" ' + self.extra_org_api_key + '"',
                force_login=True,
            ),
        )
