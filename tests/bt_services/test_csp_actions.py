import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class CspActionsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

    def tearDown(self):
        with BraintrustAppTestBase.connect_app_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    f"""
                    delete from organization_content_security_policies
                    where org_id = %s
                    """,
                    [self.org_id],
                )

        super().tearDown()

    def test_set_csp(self):
        self.run_server_action(
            self.org_api_key,
            "setOrganizationContentSecurityPolicies",
            dict(org_id=self.org_id, policy="test", policy_report_only="test report only"),
        ).json()

        csp = self.run_server_action(
            self.org_api_key,
            "getOrganizationContentSecurityPolicies",
            dict(org_name=self.org_name),
        ).json()

        self.assertEqual(csp["policy"], "test")
        self.assertEqual(csp["policy_report_only"], "test report only")

    # A user with only read access to the organization should not be able to set the CSP
    def test_set_csp_underprivileged_user(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.grant_acl(dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="read"))

        self.run_server_action(
            user_api_key,
            "setOrganizationContentSecurityPolicies",
            dict(org_id=self.org_id, policy="test with wrong permissions", policy_report_only="test report only with"),
            expect_error=True,
        )

    # But a user with only read access to the organization *should* be able to get the CSP
    def test_get_csp_underprivileged_user(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        self.grant_acl(dict(object_type="organization", object_id=self.org_id, user_id=user_id, permission="read"))

        # Set the CSP with the admin user
        self.run_server_action(
            self.org_api_key,
            "setOrganizationContentSecurityPolicies",
            dict(
                org_id=self.org_id,
                policy="test underprivileged user",
                policy_report_only="test report only underprivileged user",
            ),
        ).json()

        # Get the CSP with the underprivileged user
        csp = self.run_server_action(
            user_api_key,
            "getOrganizationContentSecurityPolicies",
            dict(org_name=self.org_name),
        ).json()

        self.assertEqual(csp["policy"], "test underprivileged user")
        self.assertEqual(csp["policy_report_only"], "test report only underprivileged user")
