import json
import os
import shutil
import subprocess
import uuid

from braintrust.xact_ids import prettify_xact
from braintrust_local.api_db_util import get_object_json
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, make_v1_url
from tests.bt_services.test_functions import CALCULATOR_PARAMS, FunctionTestBase


class PullPromptsTest(FunctionTestBase):
    def _dump_stdout(self, stdout, stderr):
        print("--- STDOUT")
        print("stdout", stdout)
        print("--- STDERR")
        print("stderr", stderr)
        print("--- END")

    def setUp(self):
        super().setUp()
        self.scratch_dir = os.path.join(os.path.dirname(__file__), "test-scratch", uuid.uuid4().hex)
        os.makedirs(self.scratch_dir, exist_ok=True)

    def tearDown(self):
        shutil.rmtree(self.scratch_dir)
        super().tearDown()

    @parameterized.expand(
        [
            (has_options, has_raw_tools, has_function_tools, lang)
            for has_options in [False, True]
            for has_raw_tools in [False, True]
            for has_function_tools in [False, True]
            for lang in ["typescript"]
        ]
    )
    def test_pull_prompts(self, has_options, has_raw_tools, has_function_tools, lang):
        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                ],
            },
            "options": {
                "model": "gpt-4o",
            },
        }

        if has_options:
            prompt_data["options"] = {**prompt_data["options"], "temperature": "0.1", "max_tokens": 100}

        if has_raw_tools:
            prompt_data["prompt"]["tools"] = json.dumps(
                [
                    {
                        "type": "function",
                        "function": {
                            "name": "calculator",
                            "description": "A simple calculator",
                            "parameters": CALCULATOR_PARAMS,
                        },
                    }
                ]
            )

        if has_function_tools:
            calculator = self._insert_calculator(lang)
            prompt_data["tool_functions"] = [
                {
                    "type": "function",
                    "id": calculator["id"],
                }
            ]

        # Create a simple prompt
        prompt = self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="Basic math",
            slug="basic-math",
        )

        scratch_dir = os.path.join(
            self.scratch_dir, f"test-pull-prompts-{has_options}-{has_raw_tools}-{has_function_tools}"
        )
        os.makedirs(scratch_dir, exist_ok=True)

        process = subprocess.Popen(
            ["npx", "braintrust", "pull", "--project-id", self.project.id, "--output-dir", scratch_dir],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        self.assertTrue(os.path.exists(os.path.join(scratch_dir, "function-test.ts")))

        # First, try to push. This should fail because if-exists=error
        process = subprocess.Popen(
            ["npx", "braintrust", "push", scratch_dir],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        self.assertNotEqual(ret, 0)

        # Next, try to ignore the error, and make sure the prompts were not updated
        process = subprocess.Popen(
            ["npx", "braintrust", "push", scratch_dir, "--if-exists", "ignore"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        self.assertEqual(ret, 0)

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                from: project_functions('{self.project.id}')
                | select: *
                | filter: slug='basic-math' """
            },
        )
        functions = resp.json()["data"]
        self.assertEqual(len(functions), 1)
        self.assertEqual(functions[0]["slug"], "basic-math")
        self.assertEqual(functions[0]["prompt_data"]["prompt"], prompt_data["prompt"])
        self.assertEqual(functions[0]["_xact_id"], prompt["_xact_id"])

        # Now run it with "replace" and make sure the prompt is updated
        process = subprocess.Popen(
            ["npx", "braintrust", "push", scratch_dir, "--if-exists", "replace"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                from: project_functions('{self.project.id}')
                | select: *
                | filter: slug='basic-math' """
            },
        )
        functions = resp.json()["data"]
        self.assertEqual(len(functions), 1)

        function = functions[0]
        if "tools" in function["prompt_data"]["prompt"] and function["prompt_data"]["prompt"]["tools"]:
            # Normalize the JSON to remove cross-language differences
            function["prompt_data"]["prompt"]["tools"] = json.dumps(
                json.loads(function["prompt_data"]["prompt"]["tools"])
            )

        self.assertEqual(functions[0]["slug"], "basic-math")
        self.assertEqual(functions[0]["prompt_data"]["prompt"], prompt_data["prompt"])
        self.assertGreater(functions[0]["_xact_id"], prompt["_xact_id"])

        # Now, write a test file that we'll use to run the prompt, next to it.
        test_file = """\
import { initLogger, wrapOpenAI } from "braintrust";
import { basicMath } from "./function-test";
import { OpenAI } from "openai";

async function main() {
  const openai = wrapOpenAI(
    new OpenAI()
  );
  const logger = initLogger({
    projectName: process.env.PROJECT_NAME,
  });
  const built = basicMath.build({
    formula: "1+1",
  });
  console.log(JSON.stringify(built, null, 2));

  const response = await openai.chat.completions.create(built);

  console.log(response);
  await logger.flush();
}

main();"""

        with open(os.path.join(scratch_dir, "test.ts"), "w") as f:
            f.write(test_file)

        process = subprocess.Popen(
            ["npx", "tsx", "test.ts"],
            cwd=scratch_dir,
            env={**self._get_testenv(), "PROJECT_NAME": self.project.name},
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        # Retrieve the LLM span from the logs and make sure it has the right values set
        rows = get_object_json("project_logs", self.project.id)[0]
        metadata = rows["metadata"]
        self.assertEqual(metadata["model"], "gpt-4o")
        self.assertEqual(metadata["prompt"]["id"], prompt["id"])
        self.assertEqual(metadata["prompt"]["project_id"], self.project.id)
        self.assertEqual(metadata["prompt"]["version"], prompt["_xact_id"])
        self.assertEqual(metadata["prompt"]["variables"]["formula"], "1+1")

    def test_pull_versioned_prompt(self):
        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [{"role": "user", "content": "ABC"}],
            },
            "options": {
                "model": "gpt-4o",
            },
        }
        prompt_version_1 = self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="ABC",
            slug="abc",
        )

        prompt_data["prompt"]["messages"][0]["content"] = "DEF"
        prompt_version_2 = self._insert_function(
            project_id=self.project.id,
            function_data={
                "type": "prompt",
            },
            prompt_data=prompt_data,
            name="ABC",
            slug="abc",
        )

        scratch_dir = os.path.join(self.scratch_dir, "test-pull-versioned-prompt")
        os.makedirs(scratch_dir, exist_ok=True)

        process = subprocess.Popen(
            ["npx", "braintrust", "pull", "--project-id", self.project.id, "--output-dir", scratch_dir],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        self.assertTrue(os.path.exists(os.path.join(scratch_dir, "function-test.ts")))

        # Check that "DEF" is in the file
        with open(os.path.join(scratch_dir, "function-test.ts")) as f:
            contents = f.read()
        print(contents)

        xact_id = prompt_version_1["_xact_id"]
        for prettify in [False, True]:
            os.remove(os.path.join(scratch_dir, "function-test.ts"))
            version = prettify_xact(xact_id) if prettify else xact_id
            process = subprocess.Popen(
                [
                    "npx",
                    "braintrust",
                    "pull",
                    "--project-id",
                    self.project.id,
                    "--output-dir",
                    scratch_dir,
                    "--version",
                    version,
                ],
                env=self._get_testenv(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )
            stdout, stderr = process.communicate()
            ret = process.returncode
            if ret != 0:
                self._dump_stdout(stdout, stderr)
                raise Exception(f"Bundled code test failed. Stderr: {stderr}")

            self.assertEqual(ret, 0)

            with open(os.path.join(scratch_dir, "function-test.ts")) as f:
                contents = f.read()
            self.assertIn("ABC", contents)
            self.assertNotIn("DEF", contents)

    def test_pull_non_function_data(self):
        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [
                    {"role": "user", "content": "What is {{formula}}? Just return the number, nothing else."}
                ],
            },
            "options": {
                "model": "gpt-4o",
            },
        }

        self.run_request(
            "post",
            make_v1_url("prompt"),
            json=dict(
                project_id=self.project.id,
                prompt_data=prompt_data,
                name="Basic math",
                slug="basic-math",
            ),
        ).json()

        scratch_dir = os.path.join(self.scratch_dir, "test-pull-non-function-data")
        os.makedirs(scratch_dir, exist_ok=True)

        process = subprocess.Popen(
            ["npx", "braintrust", "pull", "--project-id", self.project.id, "--output-dir", scratch_dir],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

        self.assertEqual(ret, 0)

        self.assertTrue(os.path.exists(os.path.join(scratch_dir, "function-test.ts")))

        process = subprocess.Popen(
            ["npx", "braintrust", "push", scratch_dir, "--if-exists", "replace"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Bundled code test failed. Stderr: {stderr}")

    def test_pull_long_prompt(self):
        # Generate a long prompt by repeating text to exceed 64kb
        base_message = "This is a very long message that will be repeated many times. " * 100
        long_message = base_message * 35  # This should create ~70kb of text

        prompt_data = {
            "prompt": {
                "type": "chat",
                "messages": [{"role": "user", "content": long_message}],
            },
            "options": {
                "model": "gpt-4",
            },
        }

        prompt = self.run_request(
            "post",
            make_v1_url("prompt"),
            json=dict(
                project_id=self.project.id,
                prompt_data=prompt_data,
                name="Long prompt test",
                slug="long-prompt",
            ),
        ).json()

        scratch_dir = os.path.join(self.scratch_dir, "test-pull-long-prompt")
        os.makedirs(scratch_dir, exist_ok=True)

        # Pull the prompt
        process = subprocess.Popen(
            ["npx", "braintrust", "pull", "--project-id", self.project.id, "--output-dir", scratch_dir],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Pull of long prompt failed. Stderr: {stderr}")

        self.assertEqual(ret, 0)
        self.assertTrue(os.path.exists(os.path.join(scratch_dir, "function-test.ts")))

        # Push it back to verify round-trip works
        process = subprocess.Popen(
            ["npx", "braintrust", "push", scratch_dir, "--if-exists", "replace"],
            env=self._get_testenv(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
        )
        stdout, stderr = process.communicate()
        ret = process.returncode
        if ret != 0:
            self._dump_stdout(stdout, stderr)
            raise Exception(f"Push of long prompt failed. Stderr: {stderr}")

        # Verify the prompt data matches
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json={
                "query": f"""
                from: project_functions('{self.project.id}')
                | select: *
                | filter: slug='long-prompt' """
            },
        )
        functions = resp.json()["data"]
        self.assertEqual(len(functions), 1)
        self.assertEqual(functions[0]["slug"], "long-prompt")
        self.assertEqual(functions[0]["prompt_data"]["prompt"], prompt_data["prompt"])
