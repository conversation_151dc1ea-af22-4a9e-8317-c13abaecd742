import braintrust
import redis
import requests
from braintrust.util import merge_dicts
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT, REDIS_OUTBOUND_RATE_LIMIT_COUNTER

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


def make_url(suffix):
    return f"{LOCAL_API_URL}/v1/{suffix}"


MIN_REQUESTS_WITHOUT_RATE_LIMIT_ERROR = 1
# We double the theoretical minimum because the rate limit counter is windowed
# by the minute, so we could get unlucky and cross the border.
MIN_REQUESTS_WITH_RATE_LIMIT_ERROR = 2 * (MIN_REQUESTS_WITHOUT_RATE_LIMIT_ERROR + 1)


def check_has_rate_limit_error(responses):
    return any(r.status_code == 429 for r in responses)


def set_override_max_requests(redis_conn, api_key, num_requests=None):
    key = f"{REDIS_OUTBOUND_RATE_LIMIT_COUNTER}_override_max_requests:{api_key}"
    redis_conn.set(key, MIN_REQUESTS_WITHOUT_RATE_LIMIT_ERROR if num_requests is None else num_requests, ex=3600)


class RateLimitTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0)
        set_override_max_requests(self.conn, self.org_api_key)

    def get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def test_rest_api_create_object(self):
        _, __, extra_api_key = self.createUserInOrg(self.org_id)
        set_override_max_requests(self.conn, extra_api_key)

        for api_key, num_requests, should_fail in [
            (self.org_api_key, MIN_REQUESTS_WITHOUT_RATE_LIMIT_ERROR, False),
            (extra_api_key, MIN_REQUESTS_WITH_RATE_LIMIT_ERROR, True),
        ]:

            def request_fn():
                return requests.post(
                    make_url("project"), headers={"Authorization": f"Bearer {api_key}"}, json=dict(name="p")
                )

            responses = [request_fn() for _ in range(num_requests)]
            self.assertEqual(should_fail, check_has_rate_limit_error(responses))

    def test_insert_many_different_objects(self):
        # Create each project using a different API key, to avoid the rate limit
        # issue.
        def request_fn(i):
            _, __, extra_api_key = self.createUserInOrg(self.org_id)
            return requests.post(
                make_url("project"), headers={"Authorization": f"Bearer {extra_api_key}"}, json=dict(name=f"p_{i}")
            )

        responses = [request_fn(i) for i in range(MIN_REQUESTS_WITH_RATE_LIMIT_ERROR)]
        self.assertFalse(check_has_rate_limit_error(responses))
        project_ids = [resp.json()["id"] for resp in responses]

        # But if we try to insert into all the experiments using a single API
        # key, it should fail.
        def request_fn2(project_id):
            return requests.post(
                make_url(f"project_logs/{project_id}/insert"),
                headers=self.get_request_headers(),
                json=dict(events=[dict()]),
            )

        responses = [request_fn2(project_id) for project_id in project_ids]
        self.assertTrue(check_has_rate_limit_error(responses))

    def test_insert_with_cached_resource_check(self):
        set_override_max_requests(self.conn, self.org_api_key, num_requests=10)
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            project_logs = braintrust.init_logger(project="p")
            # Since we are an unlimited org, we should be able to log as many
            # times to the backend as we want.
            for i in range(20):
                project_logs.log(input="foo", output="bar")
                logger.flush()
