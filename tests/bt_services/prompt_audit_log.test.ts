/**
 * Tests for Braintrust prompt audit log functionality.
 */

import { describe, expect, test } from "vitest";
import { LOCAL_API_URL, LOCAL_APP_URL } from "./setup";
import * as braintrust from "braintrust";

// Helper function to make API requests
async function makeRequest(
  path: string,
  apiKey: string,
  method: string = "GET",
  data?: unknown,
) {
  const url = `${LOCAL_API_URL}/v1/${path}`;
  const headers = {
    Authorization: `Bearer ${apiKey}`,
    "Content-Type": "application/json",
  };

  const options: RequestInit = {
    method,
    headers,
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(
      `Request failed: ${response.status} ${response.statusText} - ${errorText}`,
    );
  }

  return response.json();
}

describe("prompt audit log", () => {
  test("getPromptVersions returns transaction IDs for prompt versions", async ({
    orgId,
    orgApiKey,
  }) => {
    const slug = "audit-test-prompt";

    // Set up global braintrust state
    await braintrust.login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });

    // Create a project first
    const logger = braintrust.initLogger({
      projectName: `prompt-audit-test-${Date.now()}`,
      appUrl: LOCAL_API_URL,
      apiKey: orgApiKey,
    });
    const project = logger.project;

    // Create initial prompt
    const promptRecord = await makeRequest("prompt", orgApiKey, "POST", {
      project_id: (await project).id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "What is 1+1" }],
        },
        options: {
          model: "gpt-3.5-turbo",
        },
      },
      name: "audit test prompt",
      slug: slug,
    });

    const promptId = promptRecord.id;

    // Edit the prompt a few times
    await makeRequest(`prompt/${promptId}`, orgApiKey, "PATCH", {
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "What is 2+2" }],
        },
        options: {
          model: "gpt-3.5-turbo",
        },
      },
    });

    await makeRequest(`prompt/${promptId}`, orgApiKey, "PATCH", {
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "What is 3+3" }],
        },
        options: {
          model: "gpt-4",
        },
      },
    });

    // Get audit log entries
    const auditLogEntries = await braintrust.getPromptVersions(
      (await project).id,
      promptId,
    );

    // Should have 3 entries (1 create + 2 updates)
    expect(auditLogEntries).toHaveLength(3);

    // All entries should be transaction IDs (strings)
    for (const entry of auditLogEntries) {
      expect(typeof entry).toBe("string");
      expect(entry.length).toBeGreaterThan(0);
    }

    // Test loading specific versions using the returned transaction IDs
    // Load each version and verify we can find the expected content variations
    const loadedVersions = [];
    for (const xactId of auditLogEntries) {
      const prompt = await braintrust.loadPrompt({
        projectName: (await project).name,
        slug: slug,
        version: xactId,
        apiKey: orgApiKey,
        appUrl: LOCAL_API_URL,
      });
      expect(prompt).toBeDefined();
      expect(prompt.id).toBe(promptId);
      const built = prompt.build({});
      loadedVersions.push({
        content: built.messages[0].content,
        model: built.model,
        xactId,
      });
    }

    // Verify we have all expected versions
    const expectedVersions = [
      { content: "What is 1+1", model: "gpt-3.5-turbo" },
      { content: "What is 2+2", model: "gpt-3.5-turbo" },
      { content: "What is 3+3", model: "gpt-4" },
    ];

    const actualContentModelPairs = loadedVersions.map((v) => ({
      content: v.content,
      model: v.model,
    }));
    expect(actualContentModelPairs).toEqual(
      expect.arrayContaining(expectedVersions),
    );
    expect(actualContentModelPairs).toHaveLength(expectedVersions.length);
  });

  test("getPromptVersions handles nonexistent prompt gracefully", async ({
    orgApiKey,
  }) => {
    // Set up global braintrust state
    await braintrust.login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });

    // Create a project first
    const logger = braintrust.initLogger({
      projectName: `prompt-audit-test-${Date.now()}`,
      appUrl: LOCAL_API_URL,
      apiKey: orgApiKey,
    });
    const project = logger.project;

    // Try to get audit log for a non-existent prompt
    const auditLogEntries = await braintrust.getPromptVersions(
      (await project).id,
      "nonexistent-prompt-id",
    );

    // Should return an empty array
    expect(auditLogEntries).toEqual([]);
  });

  test("getPromptVersions handles invalid project ID gracefully", async ({
    orgApiKey,
  }) => {
    // Set up global braintrust state
    await braintrust.login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });

    // Try to get audit log with invalid project ID - should throw an error
    await expect(
      braintrust.getPromptVersions("invalid-project-id", "any-function-id"),
    ).rejects.toThrow();
  });
});
