import json

import braintrust

from tests.braintrust_app_test_base import BraintrustAppTestBase


class GroupActionsTest(BraintrustAppTestBase):
    def test_group_role_descriptions(self):
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)

        # If a privileged user fetches group descriptions, they should see all
        # of the system groups for that org.
        res = self.run_server_action(
            self.org_api_key,
            "getGroupRoleDescriptions",
            dict(orgName=self.org_name),
        ).json()
        expected_values = [
            "Unrestricted access to the organization, its data, and its settings. Can add, modify, and delete projects and all other resources. Can invite and remove members and can manage group membership.",
            "Can access, create, update, and delete projects and all resources within projects. Cannot invite or remove members or manage access to resources.",
            "Can access projects and all resources within projects. Cannot create, update, or delete any resources. Cannot invite or remove members or manage access to resources.",
        ]
        actual_values = list(res.values())
        self.assertEqual(sorted(actual_values), sorted(expected_values))

        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        # If an unprivileged user fetches group descriptions, they should see
        # nothing.
        res = self.run_server_action(
            user_api_key,
            "getGroupRoleDescriptions",
            dict(orgName=self.org_name),
        ).json()
        self.assertEqual(res, None)
