[["id"], ["_xact_id"], ["created"], ["org_id"], ["project_id"], ["experiment_id"], ["dataset_id"], ["prompt_session_id"], ["log_id"], ["span_id"], ["root_span_id"], ["span_parents"], ["metrics", "cached"], ["metrics", "start"], ["metrics", "end"], ["span_attributes", "exec_counter"], ["context", "caller_filename"], ["context", "caller_functionname"], ["context", "caller_lineno"], ["metrics", "time_to_first_token"], ["metadata", "scorer_errors"], ["metadata", "prompt", "id"], ["metadata", "prompt", "project_id"], ["metadata", "prompt", "version"], ["metadata", "user_id"], ["metadata", "http.response_time"], ["metadata", "ai.response.msToFinish"], ["metadata", "ai.response.msToFirstChunk"], ["metadata", "ai.stream.msToFirstChunk"], ["metrics", "ms_to_finish"], ["metrics", "ms_to_first_chunk"], ["origin", "object_id"], ["origin", "id"], ["origin", "_xact_id"], ["origin", "created"], ["comment", "parent_id"], ["metadata", "session_id"], ["metadata", "thread_id"], ["metadata", "organization_guid"], ["metadata", "langgraph_checkpoint_ns"]]