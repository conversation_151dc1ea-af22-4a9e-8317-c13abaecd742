import unittest
from uuid import uuid4

from braintrust.span_identifier_v1 import SpanComponentsV1, SpanObjectTypeV1, SpanRowIdsV1


class SpanComponentsV1Test(unittest.TestCase):
    def test_span_row_ids(self):
        # None of the components can be an empty string.
        SpanRowIdsV1(row_id="x", span_id="x", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV1(row_id="", span_id="x", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV1(row_id="x", span_id="", root_span_id="x")
        with self.assertRaises(Exception):
            SpanRowIdsV1(row_id="x", span_id="x", root_span_id="")

    def test_no_row_id(self):
        for object_type in SpanObjectTypeV1:
            object_id = str(uuid4())
            components = SpanComponentsV1(object_type=object_type, object_id=object_id)
            self.assertEqual(SpanComponentsV1.from_str(components.to_str()), components)

    def test_with_row_id(self):
        object_id, row_id, span_id, root_span_id = [str(uuid4()) for _ in range(4)]
        components = SpanComponentsV1(
            object_type=SpanObjectTypeV1.EXPERIMENT,
            object_id=object_id,
            row_ids=SpanRowIdsV1(row_id=row_id, span_id=span_id, root_span_id=root_span_id),
        )
        self.assertEqual(SpanComponentsV1.from_str(components.to_str()), components)

        # Should also work if row_id is not a UUID.
        components = SpanComponentsV1(
            object_type=SpanObjectTypeV1.EXPERIMENT,
            object_id=object_id,
            row_ids=SpanRowIdsV1(row_id="hello world", span_id=span_id, root_span_id=root_span_id),
        )
        self.assertEqual(SpanComponentsV1.from_str(components.to_str()), components)

        # But all the other IDs must be a UUID.
        def roundtrip_bad_component(object_id=None, **row_id_kwargs):
            x = str(uuid4())
            if object_id is None:
                object_id = x
            components = SpanComponentsV1(
                object_type=SpanObjectTypeV1.EXPERIMENT,
                object_id=object_id,
                row_ids=SpanRowIdsV1(**{**dict(row_id=x, span_id=x, root_span_id=x), **row_id_kwargs}),
            )
            SpanComponentsV1.from_str(components.to_str())

        with self.assertRaises(Exception):
            roundtrip_bad_component(object_id="hello")
        with self.assertRaises(Exception):
            roundtrip_bad_component(span_id="hello")
        with self.assertRaises(Exception):
            roundtrip_bad_component(root_span_id="hello")
