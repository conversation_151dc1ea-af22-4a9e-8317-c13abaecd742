/**
 * Tests for Braintrust push prompts functionality.
 */

import { describe, expect, test } from "vitest";
import { LOCAL_API_URL, LOCAL_APP_URL } from "./setup";
import * as braintrust from "braintrust";
import { randomUUID } from "crypto";

const CALCULATOR_PARAMS = {
  type: "object",
  properties: {
    op: { type: "string", enum: ["add", "subtract", "multiply", "divide"] },
    a: { type: "number" },
    b: { type: "number" },
  },
};

const CALCULATOR_CODE_TS = `
function calculator(args) {
    const { op, a, b } = args;
    switch (op) {
        case "add":
            return a + b;
        case "subtract":
            return a - b;
        case "multiply":
            return a * b;
        case "divide":
            if (b === 0) throw new Error("Division by zero");
            return a / b;
        default:
            throw new Error(\`Invalid operation: \${op}\`);
    }
}
`;

describe("push prompts", () => {
  // Test combinations similar to the Python parameterized test
  const testCombinations = [];
  for (const hasOptions of [false, true]) {
    for (const hasRawTools of [false, true]) {
      for (const hasFunctionTools of [false, true]) {
        for (const lang of ["typescript"]) {
          testCombinations.push({
            hasOptions,
            hasRawTools,
            hasFunctionTools,
            lang,
          });
        }
      }
    }
  }

  async function insertCalculator(
    projectId: string,
    orgApiKey: string,
    lang: string,
  ) {
    const response = await fetch(`${LOCAL_API_URL}/v1/function`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${orgApiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        project_id: projectId,
        function_data: {
          type: "code",
          data: {
            type: "inline",
            runtime_context:
              lang === "typescript"
                ? { runtime: "node", version: "20" }
                : { runtime: "python", version: "3.11" },
            code: CALCULATOR_CODE_TS,
          },
        },
        function_schema: {
          parameters: CALCULATOR_PARAMS,
          returns: { type: "number" },
        },
        name: "Calculator",
        slug: "calculator",
      }),
    });
    return response.json();
  }

  testCombinations.forEach(
    ({ hasOptions, hasRawTools, hasFunctionTools, lang }) => {
      test(`push prompts with options=${hasOptions}, rawTools=${hasRawTools}, functionTools=${hasFunctionTools}, lang=${lang}`, async ({
        orgApiKey,
      }) => {
        // Create a project
        const projectInfo = await fetch(`${LOCAL_API_URL}/v1/project`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${orgApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ name: "test-push-prompts" }),
        }).then((r) => r.json());

        const projectId = projectInfo.id;
        const messages = [
          {
            role: "user" as const,
            content:
              "What is {{formula}}? Just return the number, nothing else.",
          },
        ];

        await braintrust.login({
          appUrl: LOCAL_APP_URL,
          apiKey: orgApiKey,
          forceLogin: true,
        });

        const project = braintrust.projects.create({
          name: "test-push-prompts",
        });
        const slug = `test-prompt-${randomUUID()}`;
        const calculator = await insertCalculator(projectId, orgApiKey, lang);
        project.prompts.create({
          name: "test-prompt",
          slug,
          messages,
          params: hasOptions
            ? { temperature: "0.1", max_tokens: 100 }
            : undefined,
          model: "claude-3-5-sonnet-latest",
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
          tools: ([] as any[])
            .concat(
              hasRawTools
                ? [
                    {
                      type: "function",
                      function: {
                        name: "calculator",
                        description: "A simple calculator",
                        parameters: CALCULATOR_PARAMS,
                      },
                    },
                  ]
                : [],
            )
            .concat(
              hasFunctionTools
                ? [
                    {
                      type: "function",
                      id: calculator.id,
                    },
                  ]
                : [],
            ),
        });

        await project.publish();

        // Query the prompts using BTQL
        const btqlResp = await fetch(`${LOCAL_API_URL}/btql`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${orgApiKey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            query: `
            from: project_prompts('${projectId}')
            | select: *
            | filter: slug='${slug}'
          `,
            use_brainstore: true,
            brainstore_realtime: true,
          }),
        }).then((r) => r.json());

        const prompts = btqlResp.data;
        expect(prompts).toHaveLength(1);
        expect(prompts[0].slug).toBe(slug);
        expect(prompts[0].prompt_data.prompt.messages).toEqual(messages);
      });
    },
  );
});
