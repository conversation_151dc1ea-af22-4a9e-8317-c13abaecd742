from typing import List

from autoevals import <PERSON><PERSON><PERSON>ein
from braintrust import <PERSON><PERSON>, init_dataset
from pydantic import BaseModel, Field, validator


# Simple validators using Pydantic models with single fields
class BooleanParam(BaseModel):
    """Validator for boolean parameters."""

    value: bool = Field(default=False, description="Include a contextual prefix")

    class Config:
        # Allow the model to be created with just the value
        extra = "forbid"


class StringParam(BaseModel):
    """Validator for string parameters."""

    value: str = Field(default="this is a math problem", description="The prefix to include")

    class Config:
        extra = "forbid"


class Person(BaseModel):
    """Person object schema."""

    name: str
    age: int


class ArrayOfObjectsParam(BaseModel):
    """Validator for array of objects parameter."""

    value: List[Person] = Field(
        default=[
            Person(name="<PERSON>", age=30),
            Person(name="<PERSON>", age=25),
        ],
        description="List of people",
    )

    class Config:
        extra = "forbid"


def task(input, hooks):
    """Task function that uses parameters from hooks."""
    parameters = hooks.parameters

    # Access parameters directly like in TypeScript
    # The parameters are already validated by this point
    prefix = parameters.get("prefix", "this is a math problem")

    # Use the prefix from parameters
    return f"{prefix}: {input}"


# Run the evaluation with parameters
Eval(
    "Simple eval",
    data=init_dataset("local dev", name="sanity"),
    task=task,
    scores=[Levenshtein],
    parameters={
        # Prompt parameters
        "main": {
            "type": "prompt",
            "description": "This is the main prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        "another": {
            "type": "prompt",
            "description": "This is another prompt",
            "default": {
                "prompt": {"type": "chat", "messages": [{"role": "user", "content": "{{input}}"}]},
                "options": {"model": "gpt-4o"},
            },
        },
        # Individual parameter validators
        "include_prefix": BooleanParam,
        "prefix": StringParam,
        "array_of_objects": ArrayOfObjectsParam,
    },
)
