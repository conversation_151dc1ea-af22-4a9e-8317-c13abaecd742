import datetime
import os

import pytest
from braintrust_local.flaky_tests import FLAKY_TESTS_EXACT, FLAKY_TESTS_REGEX


def pytest_configure(config):
    """Ensure data plane service token exists before running any tests.

    This runs on both controller and workers, but we only execute on controller
    to avoid race conditions when using pytest-xdist.

    See https://github.com/pytest-dev/pytest-xdist/issues/783 for details on
    this workaround.
    """
    if hasattr(config, "workerinput"):
        # This is a worker process - skip the provisioning
        return

    if os.environ.get("BT_UNITTEST_NO_BT_SERVICES"):
        return

    # This is the controller process - do the provisioning once
    from braintrust_local.provision_dev_data_plane_service_token import provision_data_plane_service_token

    provision_data_plane_service_token()


@pytest.fixture(autouse=True)
def print_start_and_end_times(request):
    start = datetime.datetime.now()
    print(f"START TEST {request.node.name}: {start.strftime('%s')} ({start})")

    yield

    end = datetime.datetime.now()
    print(f"END TEST {request.node.name}: {end.strftime('%s')} ({end})")


def pytest_collection_modifyitems(items):
    for item in items:
        found_flake_info = FLAKY_TESTS_EXACT.get(item.nodeid)
        if found_flake_info is None:
            for regex, flake_info in FLAKY_TESTS_REGEX:
                if regex.search(item.nodeid):
                    found_flake_info = flake_info
                    break
        if found_flake_info:
            item.add_marker(pytest.mark.flaky(reruns=3, reruns_delay=1))
