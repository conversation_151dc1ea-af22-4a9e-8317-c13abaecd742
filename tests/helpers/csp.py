import re
from typing import Dict, List, Optional
from unittest.mock import ANY

from tests.braintrust_app_test_base import BraintrustAppTestBase

ParsedContentSecurityPolicy = Dict[str, List[str]]


def get_csp_from_response(response):
    return {
        "Content-Security-Policy": response.headers.get("content-security-policy"),
        "Content-Security-Policy-Report-Only": response.headers.get("content-security-policy-report-only"),
        "Report-To": response.headers.get("report-to"),
        "Report-Endpoints": response.headers.get("report-endpoints"),
    }


class csp:
    """
    A custom matcher for CSP headers. If provided with an empty string, it will match any non-empty CSP header.
    """

    def __init__(self, directives: Optional[ParsedContentSecurityPolicy] = None):
        self.expected_csp = directives or ANY

    def __eq__(self, actual_csp):
        if actual_csp is None:
            return False

        if self.expected_csp is ANY:
            return bool((actual_csp or "").strip())

        parsed = parse_content_security_policy(actual_csp)

        for directive, rule in self.expected_csp.items():
            if directive not in parsed or rule != parsed[directive]:
                return False

        return True

    def __repr__(self):
        return repr(ANY if self.expected_csp is ANY else parsed_csp_to_str(self.expected_csp))


def set_org_csp(org_id: str, policy: str, policy_report_only: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                insert into organization_content_security_policies(org_id, policy, policy_report_only)
                values (%s, %s, %s)
                """,
                [org_id, policy, policy_report_only],
            )


def get_org_csp(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                """
                select policy, policy_report_only
                from organization_content_security_policies
                where org_id = %s
                """,
                [org_id],
            )
            return cursor.fetchone()


# credit: https://github.com/helmetjs/content-security-policy-parser/blob/main/mod.ts
# ASCII whitespace is U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, or U+0020 SPACE.
ASCII_WHITESPACE_CHARS = "\t\n\f\r "
ASCII_WHITESPACE = re.compile(f"[{re.escape(ASCII_WHITESPACE_CHARS)}]+")
ASCII_WHITESPACE_AT_START = re.compile(f"^[{re.escape(ASCII_WHITESPACE_CHARS)}]+")
ASCII_WHITESPACE_AT_END = re.compile(f"[{re.escape(ASCII_WHITESPACE_CHARS)}]+$")

# ASCII is code points from U+0000 to U+007F inclusive
ASCII = re.compile(r"^[\x00-\x7f]*$")


def parse_content_security_policy(policy: str) -> ParsedContentSecurityPolicy:
    """
    Parse a serialized Content Security Policy via the spec.

    Args:
        policy: The serialized Content Security Policy to parse.

    Returns:
        A dictionary of Content Security Policy directives.

    Example:
        >>> parse_content_security_policy("default-src 'self'; script-src 'unsafe-eval' scripts.example; object-src; style-src styles.example")
        {
          "default-src": ["'self'"],
          "script-src": ["'unsafe-eval'", "scripts.example"],
          "object-src": [],
          "style-src": ["styles.example"],
        }
    """
    result: ParsedContentSecurityPolicy = {}

    # For each token returned by strictly splitting serialized on the SEMICOLON character (;)
    for token in policy.split(";"):
        # 1. Strip leading and trailing ASCII whitespace from token.
        token = ASCII_WHITESPACE_AT_START.sub("", token)
        token = ASCII_WHITESPACE_AT_END.sub("", token)

        # 2. If token is an empty string, or if token is not an ASCII string, continue.
        if not token or not ASCII.match(token):
            continue

        # 3/6. Let directive name be the first sequence of code points from token which are not ASCII whitespace.
        # And directive value be the result of splitting token on ASCII whitespace.
        parts = ASCII_WHITESPACE.split(token)
        raw_directive_name, *directive_value = parts

        # 4. Set directive name to be ASCII lowercase on directive name.
        directive_name = raw_directive_name.lower()

        # 5. If policy's directive set contains a directive whose name is directive name, continue.
        if directive_name in result:
            continue

        # 7/8. Append directive to policy's directive set.
        result[directive_name] = directive_value

    return result


def parsed_csp_to_str(parsed_csp: ParsedContentSecurityPolicy) -> str:
    csp = "; ".join(
        f"{directive_name} {' '.join(directive_value)}" for directive_name, directive_value in parsed_csp.items()
    )
    return f"{csp.strip()};" if csp else csp
