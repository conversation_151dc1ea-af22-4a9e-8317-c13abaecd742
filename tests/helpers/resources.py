import json
from collections import defaultdict
from datetime import date, datetime, timezone
from functools import lru_cache, wraps
from typing import Callable, Optional, Union, cast
from unittest import skip

import redis
from braintrust_local.constants import LOCAL_REDIS_HOST, LOCAL_REDIS_PORT

from tests.braintrust_app_test_base import BraintrustAppTestBase


def set_resource_limit(org_id: str, resource_name: str, limit: Optional[tuple[int, int]]):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                f"INSERT INTO resources (org_id, {resource_name}) VALUES (%s, %s) "
                f"ON CONFLICT (org_id) DO UPDATE SET {resource_name} = %s",
                [
                    org_id,
                    limit,
                    limit,
                ],
            )


def clear_resource_limits(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "DELETE FROM resources WHERE org_id = %s",
                [org_id],
            )


DEFAULT_RESOURCE_COLUMNS = [
    "num_private_experiment_row_actions",
    "num_production_log_row_actions",
    "num_dataset_row_actions",
    "num_log_bytes",
    "num_private_experiment_row_actions_calendar_months",
    "num_production_log_row_actions_calendar_months",
    "num_dataset_row_actions_calendar_months",
    "num_log_bytes_calendar_months",
]


def get_resource_limits(org_id: str, columns: Optional[list[str]] = None):
    columns = columns or DEFAULT_RESOURCE_COLUMNS

    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                f"SELECT {','.join(columns)} FROM resources WHERE org_id = %s",
                [org_id],
            )

            return {
                column: parse_window(cast(str, value)) if value else None
                for column, value in zip(columns, (cursor.fetchall() or [[]])[0])
            }


def get_resource_counts(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "select sum(count) as total, date_bucket as date, resource_name from resource_counts where org_id = %s group by date_bucket, resource_name",
                [org_id],
            )

            res = cursor.fetchall()

            result = defaultdict(dict)
            for total, date, resource_name in res:
                result[date.strftime("%Y-%m-%d")][resource_name] = int(total)

            return result


def parse_window(value: str):
    # Split by comma but handle the window_type which is a string
    parts = value[1:-1].split(",")
    # If there are only 2 parts, it's the old format (window_size, max_value)
    if len(parts) == 2:
        return tuple(int(x) for x in parts)
    # If there are 3 parts, it's the new format (window_size, max_value, window_type)
    # The window_type is a string like 'day' or 'calendar_month', so we need to handle it differently
    elif len(parts) == 3:
        # The window_type is wrapped in single quotes, so we need to remove them
        window_type = parts[2].strip().strip("'")
        return (int(parts[0]), int(parts[1]), window_type)
    # If there are more or fewer parts, something is wrong
    else:
        raise ValueError(f"Unexpected format for window value: {value}")


def clear_resource_counts(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "delete from resource_counts where org_id = %s",
                [org_id],
            )


def get_mocked_date(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "select _mock_current_date from _braintrust_testing_mock_current_date where org_id = %s",
                [org_id],
            )
            return cast(tuple[date], cursor.fetchone())[0].strftime("%Y-%m-%d")


def set_mocked_date(org_id: str, date: Union[str, datetime]):
    str_date = date if isinstance(date, str) else date.date()

    # we don't have a unique constraint on org_id, so we need to delete first
    clear_mocked_date(org_id)

    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "insert into _braintrust_testing_mock_current_date(org_id, _mock_current_date) values (%s, %s)",
                [org_id, str_date],
            )


def clear_mocked_date(org_id: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "delete from _braintrust_testing_mock_current_date where org_id = %s",
                [org_id],
            )


def clear_dataplane_resource_check_cache(token: str):
    redis_conn = redis.Redis(host=LOCAL_REDIS_HOST, port=LOCAL_REDIS_PORT, db=0, decode_responses=True)

    # Define the Redis key prefix based on api-ts/src/util.ts
    failed_key = f"insert_logs_resource_check_failed:{token or 'anon'}"
    redis_conn.delete(failed_key)

    redis_conn.close()


@lru_cache(maxsize=1)
def get_app_config_boolean(key: str):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "select value_boolean from app_config where key = %s",
                [key],
            )
            res = cursor.fetchone()
            return res == (True,)


def skip_if(condition: Callable[[], Optional[str]]):
    def decorator(fn):
        @wraps(fn)
        def wrapper(self, *args, **kwargs):
            if reason := condition():
                return self.skipTest(reason)
            return fn(self, *args, **kwargs)

        return wrapper

    return decorator


# TODO: fix these tests which fail locally for some reason
skip_if_calendar_months = skip_if(
    lambda: "Skipping non-calendar month tests" if get_app_config_boolean("use_calendar_months") else None
)

skip_unless_calendar_months = skip_if(
    lambda: None if get_app_config_boolean("use_calendar_months") else "Skipping calendar month tests"
)


def udf_update_resource_counts(auth_id: str, input: dict):
    with BraintrustAppTestBase.connect_app_db() as conn:
        with conn.cursor() as cursor:
            cursor.execute(
                "select update_resource_counts_for_insert(input => %s, num_shards => 10, auth_id => %s)",
                [
                    json.dumps(input),
                    auth_id,
                ],
            )
            return (cursor.fetchone() or [{}])[0]


def billing_period():
    return datetime.now(timezone.utc).date().replace(day=1).strftime("%Y-%m-%d")
