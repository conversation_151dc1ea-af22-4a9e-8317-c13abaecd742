from typing import Optional
from uuid import UUID


class uuid:
    def __init__(self, expected: Optional[str] = None):
        self.expected = expected

    def __eq__(self, actual) -> bool:
        if self.expected:
            return self.expected == actual

        try:
            return bool(UUID(actual))
        except ValueError:
            return False

    def __repr__(self) -> str:
        return f"uuid({self.expected})" if self.expected else "uuid()"
