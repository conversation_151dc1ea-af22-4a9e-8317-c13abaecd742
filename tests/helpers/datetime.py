from datetime import datetime, timezone
from typing import Optional


class iso8601:
    def __init__(self, expected: Optional[str] = None):
        self.expected = expected

    def __eq__(self, actual) -> bool:
        if not isinstance(actual, str):
            return False
        if self.expected:
            return self.expected == actual

        try:
            dt = datetime.fromisoformat(actual.replace("Z", "+00:00"))
            if dt.tzinfo is None:
                return False

            if dt.tzinfo == timezone.utc:
                return True

            delta = dt.tzinfo.utcoffset(None)
            return delta is None or delta.total_seconds() == 0

        except (ValueError, AttributeError):
            return False

    def __repr__(self) -> str:
        return f"iso8601({self.expected})" if self.expected else "iso8601()"


class timestamp:
    min_timestamp = datetime(2020, 1, 1, tzinfo=timezone.utc).timestamp()
    max_timestamp = datetime(2029, 1, 1, tzinfo=timezone.utc).timestamp()

    def __init__(self, expected: Optional[str] = None):
        self.expected = expected

    def __eq__(self, actual) -> bool:
        if not isinstance(actual, str):
            return False
        if self.expected:
            return self.expected == actual

        try:
            parsed = float(actual)
            if parsed < self.min_timestamp or parsed > self.max_timestamp:
                return False

            # Try to convert the timestamp to a datetime object
            datetime.fromtimestamp(parsed)
            return True
        except (ValueError, TypeError, OSError):
            return False

    def __repr__(self) -> str:
        return f"timestamp({self.expected})" if self.expected else "timestamp()"
