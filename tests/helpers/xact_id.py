from typing import Optional


class xact_id:
    def __init__(self, expected: Optional[str] = None):
        self.expected = expected

    def __eq__(self, actual) -> bool:
        if self.expected:
            return self.expected == actual

        # TODO: parse the actual value and check it
        return True

    def __repr__(self) -> str:
        return f"xact_id({self.expected})" if self.expected else "xact_id()"
