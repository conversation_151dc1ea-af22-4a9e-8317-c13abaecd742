from typing import Optional


class number:
    def __init__(
        self,
        *,
        eq: Optional[int | float] = None,
        gte: Optional[int | float] = None,
        lte: Optional[int | float] = None,
    ):
        self.eq = eq
        self.gte = gte
        self.lte = lte

    def __eq__(self, other: object):
        if not isinstance(other, (int, float)):
            return False

        if self.eq is not None:
            return self.eq == other

        if self.gte is not None:
            if self.lte is not None:
                return self.gte <= other <= self.lte
            return self.gte <= other

        if self.lte is not None:
            return self.lte >= other

        return True

    def __repr__(self):
        args = []
        if self.eq is not None:
            args.append(f"eq={self.eq}")
        if self.gte is not None:
            args.append(f"gte={self.gte}")
        if self.lte is not None:
            args.append(f"lte={self.lte}")
        return f"number({', '.join(args)})"
