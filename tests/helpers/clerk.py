import os
from dataclasses import dataclass
from datetime import date
from functools import lru_cache
from time import time

import requests
from requests.exceptions import HTTPError

from tests.braintrust_app_test_base import BraintrustAppTestBase

CLERK_API_BASE_URL = "https://api.clerk.com"
FIVE_MINUTES = 300  # in seconds


@dataclass
class User:
    id: str


@dataclass
class ClerkJWT:
    id: str
    token: str
    created: int


@lru_cache
def create_clerk_user(*, email_address: str, auth_id: str, **data):
    public_metadata = {**data.pop("public_metadata", {}), "bt_auth_id": auth_id}
    data.setdefault("password", email_address)

    res = requests.post(
        f"{CLERK_API_BASE_URL}/v1/users",
        json={
            "email_address": [email_address + "@example.com"],
            **data,
            "create_organization_enabled": True,
            "public_metadata": public_metadata,
        },
        headers=get_headers(),
    )
    res.raise_for_status()
    return User(id=res.json()["id"])


def delete_clerk_user(clerk_user_id: str):
    res = requests.delete(f"{CLERK_API_BASE_URL}/v1/users/{clerk_user_id}", headers=get_headers())

    try:
        res.raise_for_status()
    except HTTPError as e:
        print(f"Error deleting clerk user: {e}")


@lru_cache
def get_clerk_session(clerk_user_id: str):
    res = requests.post(f"{CLERK_API_BASE_URL}/v1/sessions", json={"user_id": clerk_user_id}, headers=get_headers())
    res.raise_for_status()

    session_id = res.json()["id"]

    res = requests.post(
        f"{CLERK_API_BASE_URL}/v1/sessions/{session_id}/tokens",
        json={"expires_in_seconds": FIVE_MINUTES},
        headers=get_headers(),
    )
    res.raise_for_status()

    return ClerkJWT(id=session_id, token=res.json()["jwt"], created=int(time()) - 1000)


def get_headers():
    secret = os.getenv("CLERK_SECRET_KEY")
    if secret is None:
        raise ValueError("CLERK_SECRET_KEY environment variable is not set")

    return {"Authorization": f"Bearer {secret}", "Content-Type": "application/json"}


def get_clerk_cookies(clerk_jwt: ClerkJWT):
    return {
        "__clerk_db_jwt": "needstobeset",
        "__session": clerk_jwt.token,
        "__client_uat": str(clerk_jwt.created),
    }


class ClerkBraintrustAppTestBase(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()
        self.clerk_user = create_clerk_user(email_address=self.user_email, auth_id=self.user_auth_id)
        self.clerk_jwt = get_clerk_session(self.clerk_user.id)

    def tearDown(self):
        super().tearDown()
        if self.clerk_user:
            delete_clerk_user(self.clerk_user.id)
