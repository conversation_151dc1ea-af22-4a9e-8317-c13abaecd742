import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

// Only enable cascade delete if we're not running through the Makefile
// (where Python tests would have already done it)
export async function setup() {
  if (!process.env.RUNNING_IN_MAKE) {
    await execAsync(
      "python3 -m braintrust_local.app_db_util update_foreign_keys_enable_cascade_delete",
    );
  }
}

// Similarly, only disable if we enabled it
export async function teardown() {
  if (!process.env.RUNNING_IN_MAKE) {
    await execAsync(
      "python3 -m braintrust_local.app_db_util update_foreign_keys_disable_cascade_delete",
    );
  }
}
