# Local development

## System requirements

- python 3.11

  - On ubuntu this can be done with `sudo apt install python3.11`
  - on MacOS this can be done by downloading and running the [Python installer](https://www.python.org/downloads/macos/). Be sure to download version 3.11.9: ![image](https://github.com/braintrustdata/braintrust/assets/943344/8a31dd40-11f3-4f32-aae8-62c97e72ef35)
    - After installing, you'll need to run the 'Install Certificates' command that is bundled with Python. Go to Macintosh HD > Applications > Python3.11 folder > Double-click on the "Install Certificates.command" file.

- nodeJS >= 20
  - We recommend installing through [`nvm`](https://github.com/nvm-sh/nvm).
- docker
  - [Installation](https://docs.docker.com/engine/install/) instructions.
- pnpm == 8.15.5
  - [Installation instructions](https://pnpm.io/installation#using-npm): we
    suggest installing it with the rest of your node installation, through
    `npm`, rather than on its own.

## Setup repository

```sh
<NAME_EMAIL>:braintrustdata/braintrust.git
cd braintrust && git submodule update --init
```

Clone the repository to local. Then make sure to initialize submodules in the
project.

## Setup root environment

Ensure that Docker is running and then run the following commands:

```sh
make develop
source env.sh
make services
```

This should start up all necessary braintrust services. As recommended in the
output of `make develop`, you should make sure to `source env.sh` in any
terminal that is running braintrust applications, including for _subsequent steps_
in this README.

## Migrations and DB schema

If this is your first time setting up braintrust (or any time we make changes to
the [webapp migrations](app/supabase/migrations) or [API
migrations](api-schema/migrations)), you will need to run the corresponding
migration scripts.

For the webapp DB:

```sh
cd app && npx supabase migration up
```

For the API DB:

```sh
python api-schema/lambda_function.py --execute --run-all-in-foreground
```

## Managing services

There are several scripts run in `make services` to launch all braintrust
services:

- `docker compose -f services/docker-compose.yml`: This compose file controls
  certain braintrust docker containers used in the local deployment.

- `./services/bt_services.py`: This script manages the remaining braintrust
  applications used in the local deployment. The script can be used to
  start/stop the applications and inspect their status. See the `SERVICES`
  variable definition in the script for more details about how each individual
  application is built and launched.

You can also run `make services-down` to stop all services.

### Logging into the app.

If this is your first time running braintrust, you will need to seed the webapp
DB with a default `braintrustdata.com` organization.

```sh
./scripts/run_sql_files.py app/supabase/seed.sql
```

Open http://localhost:3000/app and sign in with test credentials: `ankur` /
`ASDFasdf1!`. This will create a user record in supabase.

## Environment variables

There are a few environment variables used by the Braintrust SDKs to interact
with the application. You probably want to have them exported before running any
scripts:

- `BRAINTRUST_APP_URL`: This is the URL of the web application. Locally it
  should be set to `http://localhost:3000`.

- `BRAINTRUST_API_KEY`: This is an API key used to authenticate yourself to
  Braintrust. You can visit the [settings
  page](http://localhost:3000/app/braintrustdata.com/settings/api-keys) to
  generate an API key, and set it as the value of `BRAINTRUST_API_KEY`.

## Run unit tests

To verify that everything is working as intended, you can run the unit tests
from the braintrust root directory with `make test`. The unittest runner can be
invoked alone as `python -m bt_unittest`. Pass `--help` to get a list of
configuration options.

### Unit test isolation

Unit tests are run against the local Braintrust instance in an isolated per-test
organization which is cleaned up after the test completes. This makes it
slightly tricky to poke around a failing test using the product itself. One way
to work around this is to add a `breakpoint()` line before the test exits (you
will also want to run the unit tests with `--serial` so they run in the main
process). The interpreter should stop within the test, and you can look at the
data in `self.org_name` with API key `self.org_api_key`.

### Expect tests

A number of our unit tests are "expect tests", which don't have explicit
pass/fail criterion, but instead capture the result of running a braintrust
program on the DB and check that nothing has changed. The tests are defined in
`tests/expect_tests/[test_name]`, and their expected outputs are saved in
`tests/expect_tests/[test_name].expected.json`.

To add a new expect test, just add your program to `tests/expect_tests`. You
will need to run the unit test runner with `--update` in order to save the
expected output:

```
# If you are running a test which uses OpenAI, make sure you have the
# OPENAI_API_KEY env var set.
python -m bt_unittest test_expect --update --filter tests/expect_tests/[your_new_test_file]
```

### Playwright integration

To run playwright tests, first install the browsers

```
cd app; npx playwright install
```

```
cd app
pnpm test-playwright [--debug] [path/to/playwright/files]
```

NOTE: many of our playwright tests create Evals during the tests, so make sure
you have `BRAINTRUST_APP_URL` and `BRAINTRUST_API_KEY` set

### Tailing development logs

To tail development logs in your terminal, run a command of the following template:

```
tail -f $(./services/bt_services.py logfile ${service_name})
```

For example, to tail the Next.js development server logs, run the following:

```
tail -f $(./services/bt_services.py logfile webapp)
```

If you prefer, it's also possible to run the Next.js development server directly
from your terminal.

From the root of this repository, run:

```
./services/bt_services.py stop --include webapp
cd app
pnpm dev
```

### Analyze bundles

To analyze bundle sizes

```
cd app
pnpm analyze-build
```

# Running example

## Demo using OpenAI

In a new terminal window, clone
[bt-demo](https://github.com/braintrustdata/bt-demo):

```sh
cd ..
<NAME_EMAIL>:braintrustdata/bt-demo.git
cd bt-demo
```

Create a python virtual env for bt-demo (or use direnv):

```sh
python -m venv ./venv
source venv/bin/activate
```

Install dependencies:

```sh
pip install -r requirements.txt
pip install -e ../braintrust/sdk/py
```

Go to http://localhost:3000/app/braintrustdata.com/settings/api-keys to copy or
create a new API key.

Run the demo:

```sh
BRAINTRUST_APP_URL=http://localhost:3000 BRAINTRUST_API_KEY=YOUR_API_KEY python3 ./evaluate.py
```

Now if you navigate to http://localhost:3000/app/braintrustdata.com/p/text2sql,
you should see the results of the experiment.
