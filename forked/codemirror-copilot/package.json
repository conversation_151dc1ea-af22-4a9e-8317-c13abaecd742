{"name": "codemirror-copilot", "description": "This CodeMirror extension lets you use GPT to autocomplete code in CodeMirror.", "license": "MIT", "version": "0.0.7", "type": "module", "keywords": ["codemirror", "extension", "autocomplete"], "files": ["dist", "example.png", "index.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/asadm/codemirror-copilot.git"}, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "scripts": {"dev": "nodemon --watch 'src/**/*.ts' --ext 'ts' --exec \"npm run build\"", "lint": "eslint --ext .ts,.tsx src", "lint:fix": "eslint --fix --ext .ts,.tsx src", "build": "tsc && vite build", "prepublish": "npm run lint && npm run build", "test": "npm run lint"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "nodemon": "^3.0.2", "prettier": "3.1.1", "typescript": "^5.3.3", "vite": "^5.4.19", "vite-plugin-dts": "^3.6.4"}, "peerDependencies": {"@codemirror/state": "^6.2.0", "@codemirror/view": "^6.34.3"}, "engines": {"node": "*"}}