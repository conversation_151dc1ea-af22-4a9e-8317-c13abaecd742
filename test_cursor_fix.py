"""
Test script to verify that cursor-based pagination works correctly.
This will help debug if the same records are being fetched.
"""

from init_limited_dataset import init_limited_dataset

def test_cursor_pagination():
    """Test that cursor pagination fetches different records."""
    
    print("=== Testing Cursor Pagination ===\n")
    
    # First page - fetch 2 records
    print("Fetching first page (2 records)...")
    dataset1 = init_limited_dataset(
        project="pedro-project1", 
        name="themes", 
        max_records=2
    )
    
    records1 = list(dataset1)
    print(f"Page 1: Got {len(records1)} records")
    print(f"Has more: {dataset1.has_more}")
    print(f"Cursor: {dataset1.cursor}")
    
    # Show record IDs or some identifying info
    if records1:
        print("Page 1 record IDs:")
        for i, record in enumerate(records1):
            record_id = record.get('id', f'no-id-{i}')
            print(f"  Record {i}: ID = {record_id}")
    
    print()
    
    # Second page - use cursor from first page
    if dataset1.has_more and dataset1.cursor:
        print("Fetching second page (2 records) using cursor...")
        dataset2 = init_limited_dataset(
            project="pedro-project1", 
            name="themes", 
            max_records=2,
            cursor=dataset1.cursor
        )
        
        records2 = list(dataset2)
        print(f"Page 2: Got {len(records2)} records")
        print(f"Has more: {dataset2.has_more}")
        print(f"Cursor: {dataset2.cursor}")
        
        # Show record IDs
        if records2:
            print("Page 2 record IDs:")
            for i, record in enumerate(records2):
                record_id = record.get('id', f'no-id-{i}')
                print(f"  Record {i}: ID = {record_id}")
        
        print()
        
        # Check if records are different
        if records1 and records2:
            ids1 = [r.get('id') for r in records1]
            ids2 = [r.get('id') for r in records2]
            
            print("=== Comparison ===")
            print(f"Page 1 IDs: {ids1}")
            print(f"Page 2 IDs: {ids2}")
            
            if ids1 == ids2:
                print("❌ ERROR: Same records fetched! Cursor pagination is not working.")
            else:
                print("✅ SUCCESS: Different records fetched. Cursor pagination is working!")
        
    else:
        print("No more records available or no cursor returned.")
    
    print()
    
    # Test 3: Fetch without cursor to compare
    print("=== Comparison: Fetching without cursor ===")
    dataset_no_cursor = init_limited_dataset(
        project="pedro-project1", 
        name="themes", 
        max_records=2
        # No cursor parameter
    )
    
    records_no_cursor = list(dataset_no_cursor)
    print(f"No cursor: Got {len(records_no_cursor)} records")
    
    if records_no_cursor:
        print("No cursor record IDs:")
        for i, record in enumerate(records_no_cursor):
            record_id = record.get('id', f'no-id-{i}')
            print(f"  Record {i}: ID = {record_id}")
    
    # Compare with first page
    if records1 and records_no_cursor:
        ids1 = [r.get('id') for r in records1]
        ids_no_cursor = [r.get('id') for r in records_no_cursor]
        
        print(f"\nFirst page IDs: {ids1}")
        print(f"No cursor IDs: {ids_no_cursor}")
        
        if ids1 == ids_no_cursor:
            print("✅ Correct: No cursor gives same results as first page")
        else:
            print("❌ Unexpected: No cursor gives different results than first page")


def debug_cursor_values():
    """Debug what cursor values we're getting."""
    
    print("=== Debug Cursor Values ===\n")
    
    dataset = init_limited_dataset(
        project="pedro-project1", 
        name="themes", 
        max_records=1
    )
    
    # Force fetching
    records = list(dataset)
    
    print(f"Records fetched: {len(records)}")
    print(f"Has more: {dataset.has_more}")
    print(f"Cursor type: {type(dataset.cursor)}")
    print(f"Cursor value: {repr(dataset.cursor)}")
    
    if dataset.cursor:
        print(f"Cursor length: {len(dataset.cursor)}")
        print(f"Cursor first 100 chars: {dataset.cursor[:100]}")


if __name__ == "__main__":
    debug_cursor_values()
    print("\n" + "="*60 + "\n")
    test_cursor_pagination()
