Date,Host,Service,log,Message
"2025-09-12T15:14:09.176Z",,"braintrust.api","{""level"":40,""time"":1757690049176,""pid"":20,""hostname"":""ip-10-41-121-101.ec2.internal"",""name"":""braintrust-api"",""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""spanId"":""45e0fde624d743f1"",""attributes"":{""http.url"":""http://ai-braintrust.ramp.com/btql"",""http.host"":""ai-braintrust.ramp.com"",""net.host.name"":""ai-braintrust.ramp.com"",""http.method"":""POST"",""http.scheme"":""http"",""http.client_ip"":""*************"",""http.target"":""/btql"",""http.user_agent"":""Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"",""http.request_content_length_uncompressed"":674,""http.flavor"":""1.1"",""net.transport"":""ip_tcp"",""commit"":""ad263a97bafedbd526917ec1e2158757d48ccb69"",""net.host.ip"":""*************"",""net.host.port"":8000,""net.peer.ip"":""*************"",""net.peer.port"":19076,""http.status_code"":500,""http.status_text"":""INTERNAL SERVER ERROR""},""events"":[],""startTime"":""2025-09-12T15:14:09.078Z"",""durationMs"":""97.51"",""spanName"":""POST"",""msg"":""POST""}","{""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""msg"":""POST"",""log"":""{\""level\"":40,\""time\"":1757690049176,\""pid\"":20,\""hostname\"":\""ip-10-41-121-101.ec2.internal\"",\""name\"":\""braintrust-api\"",\""traceId\"":\""e64e310ec7e3d80a3573ce2942353361\"",\""spanId\"":\""45e0fde624d743f1\"",\""attributes\"":{\""http.url\"":\""http://ai-braintrust.ramp.com/btql\"",\""http.host\"":\""ai-braintrust.ramp.com\"",\""net.host.name\"":\""ai-braintrust.ramp.com\"",\""http.method\"":\""POST\"",\""http.scheme\"":\""http\"",\""http.client_ip\"":\""*************\"",\""http.target\"":\""/btql\"",\""http.user_agent\"":\""Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"",\""http.request_content_length_uncompressed\"":674,\""http.flavor\"":\""1.1\"",\""net.transport\"":\""ip_tcp\"",\""commit\"":\""ad263a97bafedbd526917ec1e2158757d48ccb69\"",\""net.host.ip\"":\""*************\"",\""net.host.port\"":8000,\""net.peer.ip\"":\""*************\"",\""net.peer.port\"":19076,\""http.status_code\"":500,\""http.status_text\"":\""INTERNAL SERVER ERROR\""},\""events\"":[],\""startTime\"":\""2025-09-12T15:14:09.078Z\"",\""durationMs\"":\""97.51\"",\""spanName\"":\""POST\"",\""msg\"":\""POST\""}"",""level"":40,""pid"":20,""source"":""stdout"",""spanName"":""POST"",""network"":{""ip"":{""attributes"":[{""ip"":""*************"",""source"":[""@log"",""@attributes.net.host.ip""]},{""ip"":""*************"",""source"":[""@log"",""@attributes.net.peer.ip""]},{""ip"":""*********"",""source"":[""@log"",""@attributes.http.user_agent""]},{""ip"":""*************"",""source"":[""@log"",""@attributes.http.client_ip""]}],""list"":[""*************"",""*************"",""*********"",""*************""]}},""spanId"":""45e0fde624d743f1"",""hostname"":""ip-10-41-121-101.ec2.internal"",""service"":""braintrust.api"",""name"":""braintrust-api"",""attributes"":{""commit"":""ad263a97bafedbd526917ec1e2158757d48ccb69"",""http"":{""flavor"":""1.1"",""status_code"":500,""method"":""POST"",""scheme"":""http"",""host"":""ai-braintrust.ramp.com"",""request_content_length_uncompressed"":674,""client_ip"":""*************"",""status_text"":""INTERNAL SERVER ERROR"",""url"":""http://ai-braintrust.ramp.com/btql"",""user_agent"":""Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"",""target"":""/btql""},""net"":{""peer"":{""port"":19076,""ip"":""*************""},""host"":{""port"":8000,""ip"":""*************"",""name"":""ai-braintrust.ramp.com""},""transport"":""ip_tcp""}},""startTime"":""2025-09-12T15:14:09.078Z"",""time"":1757690049176,""durationMs"":""97.51"",""timestamp"":1757690049176}"
"2025-09-12T15:14:09.176Z",,"braintrust.api","{""level"":50,""time"":1757690049175,""pid"":20,""hostname"":""ip-10-41-121-101.ec2.internal"",""name"":""braintrust-api"",""method"":""POST"",""path"":""/btql"",""statusCode"":500,""error"":""\nenn [RequestRetryError]: Request failed\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\n at TLSSocket.emit (node:events:518:28) {\n code: 'UND_ERR_REQ_RETRY',\n statusCode: 500,\n data: { count: 1 },\n headers: {\n 'cache-control': 'public, max-age=0, must-revalidate',\n 'content-length': '66',\n 'content-security-policy': \""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0\"",\n 'content-type': 'text/plain; charset=utf-8',\n date: 'Fri, 12 Sep 2025 15:14:09 GMT',\n etag: '\""dw1l82qs1n1u\""',\n 'reporting-endpoints': 'csp-endpoint-0=\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\""',\n server: 'Vercel',\n 'strict-transport-security': 'max-age=63072000',\n 'x-content-type-options': 'nosniff',\n 'x-frame-options': 'SAMEORIGIN',\n 'x-matched-path': '/api/project/get_id',\n 'x-nonce': 'OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj',\n 'x-vercel-cache': 'MISS',\n 'x-vercel-id': 'iad1::iad1::fk67f-1757690049091-8fd776249368'\n }\n}"",""errorContext"":{""userId"":""1ea9a20b-f449-44bb-8d84-3851cdec8ca0"",""userEmail"":""<EMAIL>"",""orgName"":null},""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""spanId"":""45e0fde624d743f1"",""spanName"":""POST"",""msg"":""REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Request failed""}","{""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""msg"":""REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Request failed"",""errorContext"":{""userEmail"":""<EMAIL>"",""userId"":""1ea9a20b-f449-44bb-8d84-3851cdec8ca0""},""method"":""POST"",""log"":""{\""level\"":50,\""time\"":1757690049175,\""pid\"":20,\""hostname\"":\""ip-10-41-121-101.ec2.internal\"",\""name\"":\""braintrust-api\"",\""method\"":\""POST\"",\""path\"":\""/btql\"",\""statusCode\"":500,\""error\"":\""\\nenn [RequestRetryError]: Request failed\\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\\n at TLSSocket.emit (node:events:518:28) {\\n code: 'UND_ERR_REQ_RETRY',\\n statusCode: 500,\\n data: { count: 1 },\\n headers: {\\n 'cache-control': 'public, max-age=0, must-revalidate',\\n 'content-length': '66',\\n 'content-security-policy': \\\""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0\\\"",\\n 'content-type': 'text/plain; charset=utf-8',\\n date: 'Fri, 12 Sep 2025 15:14:09 GMT',\\n etag: '\\\""dw1l82qs1n1u\\\""',\\n 'reporting-endpoints': 'csp-endpoint-0=\\\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\\\""',\\n server: 'Vercel',\\n 'strict-transport-security': 'max-age=63072000',\\n 'x-content-type-options': 'nosniff',\\n 'x-frame-options': 'SAMEORIGIN',\\n 'x-matched-path': '/api/project/get_id',\\n 'x-nonce': 'OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj',\\n 'x-vercel-cache': 'MISS',\\n 'x-vercel-id': 'iad1::iad1::fk67f-1757690049091-8fd776249368'\\n }\\n}\"",\""errorContext\"":{\""userId\"":\""1ea9a20b-f449-44bb-8d84-3851cdec8ca0\"",\""userEmail\"":\""<EMAIL>\"",\""orgName\"":null},\""traceId\"":\""e64e310ec7e3d80a3573ce2942353361\"",\""spanId\"":\""45e0fde624d743f1\"",\""spanName\"":\""POST\"",\""msg\"":\""REQUEST WILL FAIL ( INTERNAL SERVER ERROR ): Request failed\""}"",""level"":50,""pid"":20,""source"":""stdout"",""error"":""\nenn [RequestRetryError]: Request failed\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\n at TLSSocket.emit (node:events:518:28) {\n code: 'UND_ERR_REQ_RETRY',\n statusCode: 500,\n data: { count: 1 },\n headers: {\n 'cache-control': 'public, max-age=0, must-revalidate',\n 'content-length': '66',\n 'content-security-policy': \""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0\"",\n 'content-type': 'text/plain; charset=utf-8',\n date: 'Fri, 12 Sep 2025 15:14:09 GMT',\n etag: '\""dw1l82qs1n1u\""',\n 'reporting-endpoints': 'csp-endpoint-0=\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\""',\n server: 'Vercel',\n 'strict-transport-security': 'max-age=63072000',\n 'x-content-type-options': 'nosniff',\n 'x-frame-options': 'SAMEORIGIN',\n 'x-matched-path': '/api/project/get_id',\n 'x-nonce': 'OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj',\n 'x-vercel-cache': 'MISS',\n 'x-vercel-id': 'iad1::iad1::fk67f-1757690049091-8fd776249368'\n }\n}"",""spanName"":""POST"",""spanId"":""45e0fde624d743f1"",""path"":""/btql"",""hostname"":""ip-10-41-121-101.ec2.internal"",""service"":""braintrust.api"",""name"":""braintrust-api"",""time"":1757690049175,""timestamp"":1757690049176,""statusCode"":500}"
"2025-09-12T15:14:09.175Z",,"braintrust.api","{""level"":40,""time"":1757690049175,""pid"":20,""hostname"":""ip-10-41-121-101.ec2.internal"",""name"":""braintrust-api"",""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""spanId"":""2a2da4e96e066745"",""attributes"":{""error.type"":""enn"",""error.status_code"":200,""error_context.userId"":""1ea9a20b-f449-44bb-8d84-3851cdec8ca0"",""error_context.userEmail"":""<EMAIL>""},""events"":[{""name"":""exception"",""attributes"":{""exception.type"":""UND_ERR_REQ_RETRY"",""exception.message"":""Request failed"",""exception.stacktrace"":""RequestRetryError: Request failed\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\n at TLSSocket.emit (node:events:518:28)""},""time"":[1757690049,175066111],""droppedAttributesCount"":0}],""startTime"":""2025-09-12T15:14:09.175Z"",""durationMs"":""0.07"",""spanName"":""error"",""msg"":""error""}","{""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""msg"":""error"",""log"":""{\""level\"":40,\""time\"":1757690049175,\""pid\"":20,\""hostname\"":\""ip-10-41-121-101.ec2.internal\"",\""name\"":\""braintrust-api\"",\""traceId\"":\""e64e310ec7e3d80a3573ce2942353361\"",\""spanId\"":\""2a2da4e96e066745\"",\""attributes\"":{\""error.type\"":\""enn\"",\""error.status_code\"":200,\""error_context.userId\"":\""1ea9a20b-f449-44bb-8d84-3851cdec8ca0\"",\""error_context.userEmail\"":\""<EMAIL>\""},\""events\"":[{\""name\"":\""exception\"",\""attributes\"":{\""exception.type\"":\""UND_ERR_REQ_RETRY\"",\""exception.message\"":\""Request failed\"",\""exception.stacktrace\"":\""RequestRetryError: Request failed\\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\\n at TLSSocket.emit (node:events:518:28)\""},\""time\"":[1757690049,175066111],\""droppedAttributesCount\"":0}],\""startTime\"":\""2025-09-12T15:14:09.175Z\"",\""durationMs\"":\""0.07\"",\""spanName\"":\""error\"",\""msg\"":\""error\""}"",""level"":40,""pid"":20,""source"":""stdout"",""spanName"":""error"",""spanId"":""2a2da4e96e066745"",""hostname"":""ip-10-41-121-101.ec2.internal"",""service"":""braintrust.api"",""name"":""braintrust-api"",""attributes"":{""error_context"":{""userEmail"":""<EMAIL>"",""userId"":""1ea9a20b-f449-44bb-8d84-3851cdec8ca0""},""error"":{""status_code"":200,""type"":""enn""}},""startTime"":""2025-09-12T15:14:09.175Z"",""time"":1757690049175,""durationMs"":""0.07"",""events"":[{""name"":""exception"",""attributes"":{""exception"":{""stacktrace"":""RequestRetryError: Request failed\n at t.onHeaders (/braintrust/api-ts/local/local.js:732:20065)\n at cnn.onHeaders (/braintrust/api-ts/local/local.js:694:118175)\n at Ynn.onHeadersComplete (/braintrust/api-ts/local/local.js:705:9602)\n at wasm_on_headers_complete (/braintrust/api-ts/local/local.js:705:4570)\n at wasm://wasm/0002f80e:wasm-function[10]:0x474\n at wasm://wasm/0002f80e:wasm-function[20]:0x67b2\n at Ynn.execute (/braintrust/api-ts/local/local.js:705:6416)\n at Ynn.readMore (/braintrust/api-ts/local/local.js:705:6157)\n at TLSSocket.<anonymous> (/braintrust/api-ts/local/local.js:705:11561)\n at TLSSocket.emit (node:events:518:28)"",""type"":""UND_ERR_REQ_RETRY"",""message"":""Request failed""}},""time"":[1757690049,175066111],""droppedAttributesCount"":0}],""timestamp"":1757690049175}"
"2025-09-12T15:14:09.174Z",,"braintrust.api","{""level"":50,""time"":1757690049172,""pid"":20,""hostname"":""ip-10-41-121-101.ec2.internal"",""name"":""braintrust-api"",""url"":""https://loop-logs.preview.braintrust.dev/api/project/get_id"",""error"":{""name"":""RequestRetryError"",""code"":""UND_ERR_REQ_RETRY"",""statusCode"":500,""data"":{""count"":1},""headers"":{""cache-control"":""public, max-age=0, must-revalidate"",""content-length"":""66"",""content-security-policy"":""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0"",""content-type"":""text/plain; charset=utf-8"",""date"":""Fri, 12 Sep 2025 15:14:09 GMT"",""etag"":""\""dw1l82qs1n1u\"""",""reporting-endpoints"":""csp-endpoint-0=\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\"""",""server"":""Vercel"",""strict-transport-security"":""max-age=63072000"",""x-content-type-options"":""nosniff"",""x-frame-options"":""SAMEORIGIN"",""x-matched-path"":""/api/project/get_id"",""x-nonce"":""OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj"",""x-vercel-cache"":""MISS"",""x-vercel-id"":""iad1::iad1::fk67f-1757690049091-8fd776249368""}},""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""spanId"":""b929b59c082475e6"",""spanName"":""fetchProject"",""msg"":""Error when requesting url""}","{""traceId"":""e64e310ec7e3d80a3573ce2942353361"",""msg"":""Error when requesting url"",""log"":""{\""level\"":50,\""time\"":1757690049172,\""pid\"":20,\""hostname\"":\""ip-10-41-121-101.ec2.internal\"",\""name\"":\""braintrust-api\"",\""url\"":\""https://loop-logs.preview.braintrust.dev/api/project/get_id\"",\""error\"":{\""name\"":\""RequestRetryError\"",\""code\"":\""UND_ERR_REQ_RETRY\"",\""statusCode\"":500,\""data\"":{\""count\"":1},\""headers\"":{\""cache-control\"":\""public, max-age=0, must-revalidate\"",\""content-length\"":\""66\"",\""content-security-policy\"":\""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0\"",\""content-type\"":\""text/plain; charset=utf-8\"",\""date\"":\""Fri, 12 Sep 2025 15:14:09 GMT\"",\""etag\"":\""\\\""dw1l82qs1n1u\\\""\"",\""reporting-endpoints\"":\""csp-endpoint-0=\\\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\\\""\"",\""server\"":\""Vercel\"",\""strict-transport-security\"":\""max-age=63072000\"",\""x-content-type-options\"":\""nosniff\"",\""x-frame-options\"":\""SAMEORIGIN\"",\""x-matched-path\"":\""/api/project/get_id\"",\""x-nonce\"":\""OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj\"",\""x-vercel-cache\"":\""MISS\"",\""x-vercel-id\"":\""iad1::iad1::fk67f-1757690049091-8fd776249368\""}},\""traceId\"":\""e64e310ec7e3d80a3573ce2942353361\"",\""spanId\"":\""b929b59c082475e6\"",\""spanName\"":\""fetchProject\"",\""msg\"":\""Error when requesting url\""}"",""level"":50,""pid"":20,""source"":""stdout"",""error"":{""headers"":{""date"":""Fri, 12 Sep 2025 15:14:09 GMT"",""content-length"":""66"",""server"":""Vercel"",""x-nonce"":""OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj"",""x-frame-options"":""SAMEORIGIN"",""x-matched-path"":""/api/project/get_id"",""strict-transport-security"":""max-age=63072000"",""reporting-endpoints"":""csp-endpoint-0=\""https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14\"""",""content-security-policy"":""script-src 'self' 'unsafe-eval' 'wasm-unsafe-eval' 'strict-dynamic' 'nonce-OTRkZGY1NTgtMDY1Zi00N2EyLTljYTQtMDYxY2Q4NTAxZTFj' *.js.stripe.com js.stripe.com maps.googleapis.com ; style-src 'self' 'unsafe-inline' *.braintrust.dev fonts.googleapis.com www.gstatic.com; font-src 'self' data: fonts.gstatic.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self'; worker-src 'self' blob:; report-uri https://o4507221741076480.ingest.us.sentry.io/api/4507221754380288/security/?sentry_key=27fa5ac907cf7c6ce4a1ab2a03f805b4&sentry_environment=production&sentry_release=14; report-to csp-endpoint-0"",""x-content-type-options"":""nosniff"",""x-vercel-cache"":""MISS"",""x-vercel-id"":""iad1::iad1::fk67f-1757690049091-8fd776249368"",""content-type"":""text/plain; charset=utf-8"",""etag"":""\""dw1l82qs1n1u\"""",""cache-control"":""public, max-age=0, must-revalidate""},""code"":""UND_ERR_REQ_RETRY"",""data"":{""count"":1},""name"":""RequestRetryError"",""statusCode"":500},""url"":""https://loop-logs.preview.braintrust.dev/api/project/get_id"",""spanName"":""fetchProject"",""spanId"":""b929b59c082475e6"",""hostname"":""ip-10-41-121-101.ec2.internal"",""service"":""braintrust.api"",""name"":""braintrust-api"",""time"":1757690049172,""timestamp"":1757690049174}"