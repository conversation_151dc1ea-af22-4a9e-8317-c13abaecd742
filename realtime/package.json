{"name": "@braintrust/realtime", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev --port 8788 --inspector-port 9333", "build": "tsup --clean --dts", "watch": "tsup --watch --onSuccess \"tsup --dts-only\""}, "exports": {"./package.json": "./package.json", "./types": {"types": "./types/dist/index.d.ts", "import": "./types/dist/index.mjs", "module": "./types/dist/index.mjs", "require": "./types/dist/index.js"}}, "devDependencies": {"@cloudflare/workers-types": "^4.20241022.0", "tsup": "^8.4.0", "typescript": "^5.0.4", "wrangler": "^3.107.3"}, "dependencies": {"limiter": "^2.1.0", "zod": "^3.22.4"}}