import { z } from "zod";

export const rowSchema = z.strictObject({
  id: z.string(),
  spanId: z.string().optional(),
});
export type OpenedRow = z.infer<typeof rowSchema>;

export const userInfoSchema = z
  .strictObject({
    id: z.string(),
    user_id: z.string(),
    email: z.string().nullish(),
    avatar_url: z.string().nullish(),
    row: rowSchema.optional(),
  })
  .strip();
export type UserInfo = z.infer<typeof userInfoSchema>;

export const joinMessageSchema = z
  .strictObject({
    type: z.literal("join"),
    user: userInfoSchema,
  })
  .strip();
export type JoinMessage = z.infer<typeof joinMessageSchema>;

export const joinedMessageSchema = z
  .strictObject({
    type: z.literal("joined"),
    user: userInfoSchema,
  })
  .strip();
export type JoinedMessage = z.infer<typeof joinedMessageSchema>;

export const openedRowMessageSchema = z
  .strictObject({
    type: z.literal("openedRow"),
    sessionId: z.string(),
    row: rowSchema,
  })
  .strip();
export type OpenedRowMessage = z.infer<typeof openedRowMessageSchema>;

export const quitMessageSchema = z
  .strictObject({
    type: z.literal("quit"),
    id: z.string(),
  })
  .strip();
export type QuitMessage = z.infer<typeof quitMessageSchema>;

export const readyMessageSchema = z
  .strictObject({
    type: z.literal("ready"),
  })
  .strip();
export type ReadyMessage = z.infer<typeof readyMessageSchema>;

export const dataMessageSchema = z
  .strictObject({
    type: z.literal("data"),
    value: z.strictObject({
      iv: z.string(),
      data: z.string(),
    }),
  })
  .strip();
export type DataMessage = z.infer<typeof dataMessageSchema>;

export const realtimeMessageSchema = z.union([
  joinMessageSchema,
  joinedMessageSchema,
  openedRowMessageSchema,
  quitMessageSchema,
  readyMessageSchema,
  dataMessageSchema,
]);
export type RealtimeMessage = z.infer<typeof realtimeMessageSchema>;
