import signal
import subprocess
import sys


def sigterm_handler(signum, frame):
    del signum, frame
    print(f"Captured sigterm. Exiting container.", file=sys.stderr)
    sys.exit(0)


if __name__ == "__main__":
    signal.signal(signal.SIGTERM, sigterm_handler)
    proc = subprocess.Popen(
        ["npx", "wrangler", "dev", "index.js", "--ip", "0.0.0.0", "--port", "8788"],
        cwd="/braintrust/realtime",
    )
    proc.wait()
