name = "realtime"
main = "src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat_v2"]

# Variable bindings. These are arbitrary, plaintext strings (similar to environment variables)
# Note: Use secrets to store sensitive data.
# Docs: https://developers.cloudflare.com/workers/platform/environment-variables
[vars]
REQUESTS_PER_SECOND = 1000

# Bind a Durable Object. Durable objects are a scale-to-zero compute primitive based on the actor model.
# Durable Objects can live for as long as needed. Use these when you need a long-running "server", such as in realtime apps.
# Docs: https://developers.cloudflare.com/workers/runtime-apis/durable-objects
[[durable_objects.bindings]]
name = "CHANNELS_H"
class_name = "ChannelHibernatable"

[[durable_objects.bindings]]
name = "LIMITERS"
class_name = "RateLimiter"

[[migrations]]
tag = "v1"                               # Should be unique for each entry
new_classes = ["Channel", "RateLimiter"] # Array of new classes

[[migrations]]
tag = "v2"
deleted_classes = ["Channel"]
new_classes = ["ChannelHibernatable"]

# Durable Object migrations.
# Docs: https://developers.cloudflare.com/workers/learning/using-durable-objects#configure-durable-object-classes-with-migrations
# [[migrations]]
# tag = "v1"
# new_classes = ["MyDurableObject"]
