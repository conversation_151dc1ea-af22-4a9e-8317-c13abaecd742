# The build context of this Dockerfile is assumed to be the root of the
# repository.

FROM node:22-bookworm-slim AS base

# Build the realtime server.
FROM base AS builder
RUN corepack enable
RUN corepack prepare pnpm@8.15.9 --activate

# Install dependencies.
RUN apt-get update && apt-get upgrade -y && apt-get install -y zip python3

WORKDIR /braintrust

COPY realtime realtime
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml turbo.json .npmrc .eslintrc.cjs ./

# Run update scripts.
COPY scripts/docker_update_root_package_json.py .
RUN python3 docker_update_root_package_json.py

RUN pnpm install
RUN pnpm build --filter '@braintrust/realtime'
RUN cd realtime && npx wrangler deploy --dry-run --minify --outdir dist

# -----
# Production image: copy necessary files.
FROM base AS runner

# install runtime and debugging tools
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    python3 \
    curl vim dstat procps net-tools ripgrep

WORKDIR /braintrust

COPY --from=builder /braintrust/node_modules node_modules
COPY --from=builder /braintrust/realtime/node_modules realtime/node_modules
COPY --from=builder /braintrust/realtime/dist/index.js /braintrust/realtime/wrangler.toml  realtime/

COPY realtime/scripts/docker_entrypoint.py .

EXPOSE 8788
ENTRYPOINT ["python3", "docker_entrypoint.py"]

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8788/ || exit 1
