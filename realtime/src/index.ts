import {
  RealtimeMessage,
  realtimeMessageSchema,
  userInfoSchema,
} from "../types";
import { RateLimiter as RateLimiterMem } from "limiter";
import { z } from "zod";
import { v4 as uuiv4 } from "uuid";

export interface Env {
  CHANNELS_H: DurableObjectNamespace;
  LIMITERS: DurableObjectNamespace;

  REQUESTS_PER_SECOND?: number;
}

// https://developers.cloudflare.com/workers/examples/cors-header-proxy/
const corsHeaders = {
  "access-control-allow-origin": "*",
  "access-control-allow-credentials": "true",
  "access-control-allow-methods": "GET,OPTIONS,POST",
};
async function handleOptions(request: Request) {
  if (
    request.headers.get("Origin") !== null &&
    request.headers.get("Access-Control-Request-Method") !== null &&
    request.headers.get("Access-Control-Request-Headers") !== null
  ) {
    // Handle CORS preflight requests.
    return new Response(null, {
      headers: {
        ...corsHeaders,
        "access-control-allow-headers": request.headers.get(
          "Access-Control-Request-Headers",
        )!,
      },
    });
  } else {
    // Handle standard OPTIONS request.
    return new Response(null, {
      headers: {
        Allow: "GET, HEAD, POST, OPTIONS",
      },
    });
  }
}
export default {
  async fetch(
    request: Request,
    env: Env,
    ctx: ExecutionContext,
  ): Promise<Response> {
    if (request.method === "OPTIONS") {
      return handleOptions(request);
    }

    const url = new URL(request.url);
    const path = url.pathname.slice(1).split("/");

    if (!path[0]) {
      return new Response("Hello World!", {
        headers: {
          "access-control-allow-origin": "*",
          "access-control-allow-methods": "GET,OPTIONS",
        },
      });
    }

    // TODO We should authenticate users here

    // There are two ways to interact with a channel:
    // /channel/<channel_id>/websocket
    //    returns a websocket connection
    // /channel/<channel_id>/broadcast
    //    convenience method to broadcast a message to a channel. Runs
    //    in the background and returns immediately.
    switch (path[0]) {
      case "channel":
        // Compute a new URL with `/channel/<channel_id>` removed. We'll forward the rest of the path
        // to the Durable Object.
        const newUrl = new URL(request.url);
        newUrl.pathname = "/" + path.slice(2).join("/");

        const id = env.CHANNELS_H.idFromName(path[1]);
        const channel = env.CHANNELS_H.get(id);
        return channel.fetch(newUrl, request);
      case "broadcast":
        const data = await request.json();
        if (!Array.isArray(data)) {
          return new Response("Must provide an array", { status: 400 });
        }
        for (const item of data) {
          const { channel: channelName, ...data } = item;
          const id = env.CHANNELS_H.idFromName(channelName);
          const channel = env.CHANNELS_H.get(id);

          ctx.waitUntil(
            channel.fetch(request.url, {
              method: "POST",
              body: JSON.stringify(data),
            }),
          );
        }
        return new Response(null, { status: 204 });
      default:
        return new Response("Not found", { status: 404 });
    }
  },
};

const sessionSchema = z.strictObject({
  id: z.string(),
  limiterId: z.string(),
  user: userInfoSchema.optional(),
  quit: z.boolean(),
});

type Session = z.infer<typeof sessionSchema>;
type BlockedMessages = string[];

export class ChannelHibernatable {
  state: DurableObjectState;
  env: Env;

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname.slice(1).split("/");

    if (path.length != 1) {
      return new Response("Not found", { status: 404 });
    }

    switch (path[0]) {
      case "websocket":
        return this.handleWebsocket(request);
      case "broadcast":
        return this.handleBroadcast(request);
      default:
        return new Response("Not found", { status: 404 });
    }
  }

  async handleWebsocket(request: Request): Promise<Response> {
    // NOTE: Most of this code is copied from
    // https://github.com/cloudflare/workers-chat-demo/blob/master/src/chat.mjs#L238

    // The request is to `/api/room/<name>/websocket`. A client is trying to establish a new
    // WebSocket session.
    if (request.headers.get("Upgrade") != "websocket") {
      return new Response("expected websocket", { status: 400 });
    }

    // Get the client's IP address for use with the rate limiter.
    const ip = request.headers.get("CF-Connecting-IP");

    // To accept the WebSocket request, we create a WebSocketPair (which is like a socketpair,
    // i.e. two WebSockets that talk to each other), we return one end of the pair in the
    // response, and we operate on the other end. Note that this API is not part of the
    // Fetch API standard; unfortunately, the Fetch API / Service Workers specs do not define
    // any way to act as a WebSocket server today.
    const [client, server] = Object.values(new WebSocketPair());

    // We're going to take pair[1] as our end, and return pair[0] to the client.
    await this.handleSession(server, ip);

    // Now we return the other end of the pair to the client.
    return new Response(null, { status: 101, webSocket: client });
  }

  async handleSession(webSocket: WebSocket, ip: string | null) {
    // Set up our rate limiter client.
    const limiterId = this.env.LIMITERS.idFromName(ip || "");

    // Create our session and add it to the sessions list.
    // We don't send any messages to the client until it has sent us the initial user info
    // message. Until then, we will queue messages in `session.blockedMessages`.
    const blockedMessages: BlockedMessages = [];
    const session: Session = {
      id: uuiv4(),
      limiterId: limiterId.toString(),
      user: undefined,
      quit: false,
    };

    // Queue "join" messages for all online users, to populate the client's roster.
    this.state.getWebSockets("session").forEach((otherWebSocket) => {
      const otherSession = sessionSchema.parse(
        otherWebSocket.deserializeAttachment(),
      );
      if (otherSession.user) {
        blockedMessages.push(
          JSON.stringify({ type: "joined", user: otherSession.user }),
        );
      }
    });

    // TODO: Implement message backlog. Note that we'll want to tweak how we store from the below
    // snippet, because we store other stuff in the durable object's state (like blocked messages).
    // Load the last 100 messages from the chat history stored on disk, and send them to the
    // client.
    // const storage = await this.state.storage.list<string>({
    //   reverse: true,
    //   limit: 100,
    // });
    // const backlog = [...storage.values()];
    // backlog.reverse();
    // backlog.forEach((value) => {
    //  blockedMessages.push(value);
    // });

    this.state.storage.put(
      `session-${session.id}-blocked-messages`,
      blockedMessages,
    );
    webSocket.serializeAttachment(session);

    // Calling `acceptWebSocket()` informs the runtime that this WebSocket is to begin terminating
    // request within the Durable Object. It has the effect of "accepting" the connection,
    // and allowing the WebSocket to send and receive messages.
    // Unlike `ws.accept()`, `state.acceptWebSocket(ws)` informs the Workers Runtime that the WebSocket
    // is "hibernatable", so the runtime does not need to pin this Durable Object to memory while
    // the connection is open. During periods of inactivity, the Durable Object can be evicted
    // from memory, but the WebSocket connection will remain open. If at some later point the
    // WebSocket receives a message, the runtime will recreate the Durable Object
    // (run the `constructor`) and deliver the message to the appropriate handler.
    this.state.acceptWebSocket(webSocket, ["session"]);
  }

  async webSocketMessage(webSocket: WebSocket, msg: ArrayBuffer | string) {
    const session = sessionSchema.parse(webSocket.deserializeAttachment());
    const receivedUserInfo = !!session.user;

    const limiter = new RateLimiterClient(
      () =>
        this.env.LIMITERS.get(
          this.env.LIMITERS.idFromString(session.limiterId),
        ),
      (err: Error) => webSocket.close(1011, err.stack),
    );

    try {
      if (session.quit) {
        // Whoops, when trying to send to this WebSocket in the past, it threw an exception and
        // we marked it broken. But somehow we got another message? I guess try sending a
        // close(), which might throw, in which case we'll try to send an error, which will also
        // throw, and whatever, at least we won't accept the message. (This probably can't
        // actually happen. This is defensive coding.)
        webSocket.close(1011, "WebSocket broken.");
        return;
      }

      // Check if the user is over their rate limit and reject the message if so.
      if (!limiter.checkLimit()) {
        webSocket.send(
          JSON.stringify({
            error: "Your IP is being rate-limited, please try again later.",
          }),
        );
        return;
      }

      const dataString =
        typeof msg === "string" ? msg : new TextDecoder().decode(msg);

      const message = realtimeMessageSchema.safeParse(JSON.parse(dataString));
      if (!receivedUserInfo) {
        // I guess we'll use JSON.
        if (!message.success) {
          webSocket.send(JSON.stringify({ error: "Invalid message received" }));
          return;
        }

        const data = message.data;
        if (data.type !== "join") {
          webSocket.send(
            JSON.stringify({
              error: "First message must be a join message.",
            }),
          );
          return;
        }

        // The first message the client sends is the user info message with their name. Save it
        // into their session object.
        session.user = data.user;

        // Deliver all the messages we queued up since the user connected.
        const blockedMessages =
          (await this.state.storage.get<BlockedMessages>(
            `session-${session.id}-blocked-messages`,
          )) ?? [];
        blockedMessages.forEach((queued) => {
          webSocket.send(queued);
        });

        // This does not need to be awaited
        this.state.storage.delete(`session-${session.id}-blocked-messages`);

        // Register ourselves, so that we receive our own join message.
        webSocket.serializeAttachment(session);

        // Broadcast to all other connections that this user has joined.
        this.broadcast({ type: "joined", user: session.user });

        webSocket.send(JSON.stringify({ type: "ready" }));
        return;
      }

      // Update the last opened row for the user, so it's automatically sent to new users.
      if (message.success && message.data.type === "openedRow") {
        const data = message.data;
        if (session.user) {
          session.user.row = data.row;
        }
      }

      this.broadcast(dataString);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (err: any) {
      // Report any exceptions directly back to the client. As with our handleErrors() this
      // probably isn't what you'd want to do in production, but it's convenient when testing.
      webSocket.send(JSON.stringify({ error: err.stack }));
    } finally {
      webSocket.serializeAttachment(session);
    }
  }

  async webSocketClose(
    ws: WebSocket,
    code: number,
    reason: string,
    wasClean: boolean,
  ) {
    const session = sessionSchema.parse(ws.deserializeAttachment());
    session.quit = true;
    ws.serializeAttachment(session);
    this.state.storage.delete(`session-${session.id}-blocked-messages`);

    if (session.user) {
      this.broadcast({ type: "quit", id: session.user.id });
    }
    ws.close(code, reason);
  }

  // broadcast() broadcasts a message to all clients.
  broadcast(message: string | RealtimeMessage) {
    // Apply JSON if we weren't given a string to start with.
    const messageS: string =
      typeof message === "string" ? message : JSON.stringify(message);

    // Iterate over all the sessions sending them messages.
    const quitters: Session[] = [];
    this.state.getWebSockets("session").forEach((ws) => {
      const session = sessionSchema.parse(ws.deserializeAttachment());
      if (session.user) {
        try {
          ws.send(messageS);
        } catch (err) {
          // Whoops, this connection is dead. Remove it from the list and arrange to notify
          // everyone below.
          session.quit = true;
          quitters.push(session);
        }
      } else {
        // This session hasn't sent the initial user info message yet, so we're not sending them
        // messages yet (no secret lurking!). Queue the message to be sent later.
        //
        // The await on put() should complete instantly, but per https://developers.cloudflare.com/durable-objects/api/transactional-storage-api/
        // awaiting allows workerd to handle backpressure.
        this.state.waitUntil(
          (async () => {
            const key = `session-${session.id}-blocked-messages`;
            await this.state.storage.put(
              key,
              (await this.state.storage.get<BlockedMessages>(
                `session-${session.id}-blocked-messages`,
              )) ?? [],
            );
          })(),
        );
      }
    });

    // Remove any sessions that have quit.
    for (const session of quitters) {
      if (session.user) {
        this.broadcast({ type: "quit", id: session.user?.id });
      }
    }
  }

  async handleBroadcast(request: Request): Promise<Response> {
    const data = await request.text();
    this.broadcast(data);
    return new Response(null, {
      status: 204,
    });
  }
}

// Copied from https://github.com/cloudflare/workers-chat-demo/blob/master/src/chat.mjs#L437
// =======================================================================================
// The RateLimiter Durable Object class.

// `handleErrors()` is a little utility function that can wrap an HTTP request handler in a
// try/catch and return errors to the client. You probably wouldn't want to use this in production
// code but it is convenient when debugging and iterating.
async function handleErrors(request: Request, func: () => Promise<Response>) {
  try {
    return await func();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  } catch (err: any) {
    if (request.headers.get("Upgrade") == "websocket") {
      // Annoyingly, if we return an HTTP error in response to a WebSocket request, Chrome devtools
      // won't show us the response body! So... let's send a WebSocket response with an error
      // frame instead.
      const pair = new WebSocketPair();
      pair[1].accept();
      pair[1].send(JSON.stringify({ error: err.stack }));
      pair[1].close(1011, "Uncaught exception during session setup");
      return new Response(null, { status: 101, webSocket: pair[0] });
    } else {
      return new Response(err.stack, { status: 500 });
    }
  }
}

// RateLimiter implements a Durable Object that tracks the frequency of messages from a particular
// source and decides when messages should be dropped because the source is sending too many
// messages.
//
// We utilize this in ChatRoom, above, to apply a per-IP-address rate limit. These limits are
// global, i.e. they apply across all chat rooms, so if a user spams one chat room, they will find
// themselves rate limited in all other chat rooms simultaneously.
export class RateLimiter {
  private limiter: RateLimiterMem;
  constructor(controller: DurableObjectState, env: Env) {
    // Timestamp at which this IP will next be allowed to send a message. Start in the distant
    // past, i.e. the IP can send a message now.
    this.limiter = new RateLimiterMem({
      tokensPerInterval: env.REQUESTS_PER_SECOND ?? 1,
      interval: "second",
      fireImmediately: true,
    });
  }

  // Our protocol is: POST when the IP performs an action, or GET to simply read the current limit.
  // Either way, the result is the number of seconds to wait before allowing the IP to perform its
  // next action.
  async fetch(request: Request) {
    return await handleErrors(request, async () => {
      let remaining = 0;
      if (request.method === "POST") {
        // Will be -1 if the IP is currently rate limited.
        remaining = await this.limiter.removeTokens(1);
      }
      const cooldown = remaining >= 0 ? 0 : 1;
      return new Response(`${cooldown}`);
    });
  }
}

// RateLimiterClient implements rate limiting logic on the caller's side.
class RateLimiterClient {
  private limiter: DurableObjectStub;
  private inCooldown: boolean;
  // The constructor takes two functions:
  // * getLimiterStub() returns a new Durable Object stub for the RateLimiter object that manages
  //   the limit. This may be called multiple times as needed to reconnect, if the connection is
  //   lost.
  // * reportError(err) is called when something goes wrong and the rate limiter is broken. It
  //   should probably disconnect the client, so that they can reconnect and start over.
  constructor(
    private getLimiterStub: () => DurableObjectStub,
    private reportError: (err: Error) => void,
  ) {
    // Call the callback to get the initial stub.
    this.limiter = getLimiterStub();

    // When `inCooldown` is true, the rate limit is currently applied and checkLimit() will return
    // false.
    this.inCooldown = false;
  }

  // Call checkLimit() when a message is received to decide if it should be blocked due to the
  // rate limit. Returns `true` if the message should be accepted, `false` to reject.
  checkLimit() {
    if (this.inCooldown) {
      return false;
    }
    // The original code from cloudflare set cooldown to true here, which basically means that if you
    // check again before the first check even completes, you'll get rate limited. For a low rate limit
    // (like the 5 requests per 20 seconds they had originally) that makes sense, but we want a very
    // high rate limit (many requests per second) so we do not preemptively activate the cooldown.
    this.callLimiter();
    return true;
  }

  // callLimiter() is an internal method which talks to the rate limiter.
  async callLimiter() {
    try {
      let response;
      try {
        // Currently, fetch() needs a valid URL even though it's not actually going to the
        // internet. We may loosen this in the future to accept an arbitrary string. But for now,
        // we have to provide a dummy URL that will be ignored at the other end anyway.
        response = await this.limiter.fetch("https://dummy-url", {
          method: "POST",
        });
      } catch (err) {
        // `fetch()` threw an exception. This is probably because the limiter has been
        // disconnected. Stubs implement E-order semantics, meaning that calls to the same stub
        // are delivered to the remote object in order, until the stub becomes disconnected, after
        // which point all further calls fail. This guarantee makes a lot of complex interaction
        // patterns easier, but it means we must be prepared for the occasional disconnect, as
        // networks are inherently unreliable.
        //
        // Anyway, get a new limiter and try again. If it fails again, something else is probably
        // wrong.
        this.limiter = this.getLimiterStub();
        response = await this.limiter.fetch("https://dummy-url", {
          method: "POST",
        });
      }

      // The response indicates how long we want to pause before accepting more requests.
      const cooldown = +(await response.text());
      if (cooldown > 0) {
        this.inCooldown = true;
        await new Promise((resolve) => setTimeout(resolve, cooldown * 1000));
      }

      // Done waiting.
      this.inCooldown = false;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    } catch (err: any) {
      this.reportError(err);
    }
  }
}
