import braintrust
from typing import Optional, Dict, Any, Union, List, Iterator
import itertools


class LimitedDatasetWrapper:
    """
    A wrapper around a Braintrust Dataset that properly limits the number of records fetched.
    
    This class provides the same interface as a Dataset but respects the max_records limit.
    """
    
    def __init__(self, dataset, max_records: Optional[int] = None):
        self._dataset = dataset
        self.max_records = max_records
        self._cached_records = None
    
    def __iter__(self):
        """Iterator that respects the max_records limit."""
        if self.max_records is None:
            # No limit, return all records
            return iter(self._dataset)
        else:
            # Limit the number of records
            return itertools.islice(self._dataset, self.max_records)
    
    def __getattr__(self, name):
        """Delegate all other attributes to the underlying dataset."""
        return getattr(self._dataset, name)
    
    def fetch_limited(self) -> List[Any]:
        """
        Fetch records with the limit applied.
        
        Returns:
            List of records up to max_records limit.
        """
        if self._cached_records is None:
            if self.max_records is None:
                self._cached_records = list(self._dataset)
            else:
                self._cached_records = list(itertools.islice(self._dataset, self.max_records))
        return self._cached_records
    
    def clear_cache(self):
        """Clear the cached records."""
        self._cached_records = None
        if hasattr(self._dataset, '_clear_cache'):
            self._dataset._clear_cache()


def init_limited_dataset(
    project: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
    version: Optional[Union[str, int]] = None,
    app_url: Optional[str] = None,
    api_key: Optional[str] = None,
    org_name: Optional[str] = None,
    project_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    use_output: bool = braintrust.DEFAULT_IS_LEGACY_DATASET,
    _internal_btql: Optional[Dict[str, Any]] = None,
    state: Optional[Any] = None,
    max_records: Optional[int] = None,
) -> LimitedDatasetWrapper:
    """
    Create a new limited dataset that respects the max_records parameter.
    
    This function works exactly like braintrust.init_dataset() but properly
    limits the number of records fetched during iteration.
    
    Args:
        max_records: Maximum number of records to fetch. If None, fetches all records.
        All other parameters are the same as braintrust.init_dataset()
    
    Returns:
        A LimitedDatasetWrapper object that behaves like a regular Dataset but respects limits.
    
    Example:
        # Fetch only 5 records from the dataset
        dataset = init_limited_dataset(
            project="my-project", 
            name="my-dataset", 
            max_records=5
        )
        
        records = []
        for record in dataset:
            records.append(record)
        
        print(f"Fetched {len(records)} records")  # Will print "Fetched 5 records"
    """
    
    # Create the regular dataset using braintrust.init_dataset
    regular_dataset = braintrust.init_dataset(
        project=project,
        name=name,
        description=description,
        version=version,
        app_url=app_url,
        api_key=api_key,
        org_name=org_name,
        project_id=project_id,
        metadata=metadata,
        use_output=use_output,
        _internal_btql=_internal_btql,
        state=state,
    )
    
    # Wrap it with our limiting functionality
    return LimitedDatasetWrapper(regular_dataset, max_records)


def fetch_dataset_with_limit(
    project: str,
    name: str,
    max_records: int,
    **kwargs
) -> List[Any]:
    """
    Convenience function to fetch a limited number of records from a dataset.
    
    Args:
        project: Project name
        name: Dataset name  
        max_records: Maximum number of records to fetch
        **kwargs: Additional arguments to pass to init_dataset
    
    Returns:
        List of records (up to max_records)
    
    Example:
        records = fetch_dataset_with_limit("my-project", "my-dataset", 10)
        print(f"Got {len(records)} records")
    """
    dataset = init_limited_dataset(
        project=project,
        name=name,
        max_records=max_records,
        **kwargs
    )
    return dataset.fetch_limited()


# Example usage and test
if __name__ == "__main__":
    print("Testing limited dataset functionality...")
    
    # Test 1: Using the wrapper with iteration
    print("\n=== Test 1: Limited iteration ===")
    try:
        dataset = init_limited_dataset(
            project="pedro-project1", 
            name="themes", 
            max_records=1
        )
        
        records = []
        for record in dataset:
            records.append(record)
        
        print(f"Fetched {len(records)} records using iteration")
        for i, record in enumerate(records):
            print(f"Record {i}: {record}")
            
    except Exception as e:
        print(f"Error in test 1: {e}")
    
    # Test 2: Using the convenience function
    print("\n=== Test 2: Convenience function ===")
    try:
        records = fetch_dataset_with_limit("pedro-project1", "themes", 2)
        print(f"Fetched {len(records)} records using convenience function")
        for i, record in enumerate(records):
            print(f"Record {i}: {record}")
            
    except Exception as e:
        print(f"Error in test 2: {e}")
    
    # Test 3: No limit (should work like regular dataset)
    print("\n=== Test 3: No limit ===")
    try:
        dataset = init_limited_dataset(
            project="pedro-project1", 
            name="themes", 
            max_records=None  # No limit
        )
        
        # Just get first 3 for testing
        records = []
        for i, record in enumerate(dataset):
            if i >= 3:  # Stop after 3 for demo
                break
            records.append(record)
        
        print(f"Fetched {len(records)} records (stopped at 3 for demo)")
        
    except Exception as e:
        print(f"Error in test 3: {e}")
    
    print("\nTesting complete!")
