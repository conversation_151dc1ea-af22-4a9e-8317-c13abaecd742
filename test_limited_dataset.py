#!/usr/bin/env python3
"""
Test script for the limited dataset functionality.
This replaces your original script and should only fetch the specified number of records.
"""

from init_limited_dataset import init_limited_dataset

# Your original script, but using the limited dataset function
dataset = init_limited_dataset(
    project="pedro-project1",
    name="themes",
    max_records=1  # This will actually work!
)

records = []
for record in dataset:
    records.append(record)

print(f"Total records fetched: {len(records)}")
for i, record in enumerate(records):
    print(f"Record {i}: {record}")

# You can also access all the normal dataset properties and methods
print(f"\nDataset ID: {dataset.id}")
print(f"Dataset name: {dataset.name}")
