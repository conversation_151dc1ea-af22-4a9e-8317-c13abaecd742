{"name": "@braintrust/btql", "version": "0.0.1", "description": "", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"btql": "./cli/dist/index.js"}, "scripts": {"build": "run-p build:*", "build:typecheck": "tsc --noEmit", "build:tsup": "tsup --clean --dts", "watch": "tsup --watch --dts", "clean": "rm -rf dist", "test": "vitest run"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}, "./cli": {"types": "./cli/dist/index.d.ts", "import": "./cli/dist/index.mjs", "module": "./cli/dist/index.mjs", "require": "./cli/dist/index.js"}, "./parser": {"types": "./parser/dist/index.d.ts", "import": "./parser/dist/index.mjs", "module": "./parser/dist/index.mjs", "require": "./parser/dist/index.js"}, "./schema": {"types": "./schema/dist/index.d.ts", "import": "./schema/dist/index.mjs", "module": "./schema/dist/index.mjs", "require": "./schema/dist/index.js"}, "./binder": {"types": "./binder/dist/index.d.ts", "import": "./binder/dist/index.mjs", "module": "./binder/dist/index.mjs", "require": "./binder/dist/index.js"}, "./planner": {"types": "./planner/dist/index.d.ts", "import": "./planner/dist/index.mjs", "module": "./planner/dist/index.mjs", "require": "./planner/dist/index.js"}}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/ajv": "^1.0.0", "@types/argparse": "^2.0.16", "@types/react": "^19.0.2", "@typescript-eslint/eslint-plugin": "^8.11.0", "duckdb": "^1.0.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "json-refs": "^3.0.15", "json-schema-to-zod": "^2.0.14", "npm-run-all": "^4.1.5", "tsup": "^8.4.0", "typescript": "^5.0.4", "vite-tsconfig-paths": "^4.3.1", "vitest": "^2.1.9"}, "dependencies": {"ajv": "^8.12.0", "argparse": "^2.0.1", "chalk": "^4.1.2", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}}