import { expect, test } from "vitest";
import { coerceAllTypes } from "./types";
import { ScalarType } from "#/schema";

test("coerceAllTypes", () => {
  // Test with single type
  expect(coerceAllTypes(["string"], true)).toBe("string");
  expect(coerceAllTypes(["number"], true)).toBe("number");

  // Test with multiple same types
  expect(coerceAllTypes(["string", "string", "string"], true)).toBe("string");
  expect(coerceAllTypes(["number", "number", "number"], true)).toBe("number");

  // Test with null values
  expect(coerceAllTypes(["null", "null", "null"], true)).toBe("null");
  expect(coerceAllTypes(["string", "null", "string"], true)).toBe("string");

  // Test with mixed types
  expect(coerceAllTypes(["string", "number"], true)).toBe("number");
  expect(coerceAllTypes(["number", "integer"], true)).toBe("number");
  expect(coerceAllTypes(["boolean", "integer", "number"], true)).toBe("number");

  // Test with datetime and interval
  expect(coerceAllTypes(["datetime", "interval"], true)).toBe("datetime");
  expect(coerceAllTypes(["string", "interval"], true)).toBe("datetime");

  // Test with object and array
  expect(coerceAllTypes(["object", "array"], true)).toBe("array");
  expect(coerceAllTypes(["string", "array", "object"], true)).toBe("string");

  // Test with all types
  const allTypes: ScalarType[] = [
    "string",
    "number",
    "integer",
    "boolean",
    "null",
    "array",
    "object",
    "datetime",
    "date",
    "interval",
  ];
  expect(coerceAllTypes(allTypes, true)).toBe("datetime");
});
