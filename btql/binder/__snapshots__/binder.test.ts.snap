// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`bind IN operator 1`] = `
{
  "children": [
    {
      "left": {
        "name": [
          "a",
        ],
        "op": "field",
        "type": {
          "type": "integer",
        },
      },
      "op": "eq",
      "right": {
        "op": "literal",
        "type": "integer",
        "value": 1,
      },
    },
    {
      "left": {
        "name": [
          "a",
        ],
        "op": "field",
        "type": {
          "type": "integer",
        },
      },
      "op": "eq",
      "right": {
        "op": "literal",
        "type": "integer",
        "value": 2,
      },
    },
    {
      "left": {
        "name": [
          "a",
        ],
        "op": "field",
        "type": {
          "type": "integer",
        },
      },
      "op": "eq",
      "right": {
        "op": "literal",
        "type": "integer",
        "value": 3,
      },
    },
  ],
  "op": "or",
}
`;

exports[`bind NOT IN operator 1`] = `
{
  "expr": {
    "children": [
      {
        "left": {
          "name": [
            "a",
          ],
          "op": "field",
          "type": {
            "type": "integer",
          },
        },
        "op": "eq",
        "right": {
          "op": "literal",
          "type": "integer",
          "value": 1,
        },
      },
      {
        "left": {
          "name": [
            "a",
          ],
          "op": "field",
          "type": {
            "type": "integer",
          },
        },
        "op": "eq",
        "right": {
          "op": "literal",
          "type": "integer",
          "value": 2,
        },
      },
      {
        "left": {
          "name": [
            "a",
          ],
          "op": "field",
          "type": {
            "type": "integer",
          },
        },
        "op": "eq",
        "right": {
          "op": "literal",
          "type": "integer",
          "value": 3,
        },
      },
    ],
    "op": "or",
  },
  "op": "not",
}
`;

exports[`bind timestamp functions 1`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 2`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 3`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 4`] = `
{
  "args": [],
  "name": "current_timestamp",
  "op": "function",
}
`;

exports[`bind timestamp functions 5`] = `
{
  "args": [],
  "name": "current_date",
  "op": "function",
}
`;

exports[`bind timestamp functions 6`] = `
{
  "left": {
    "args": [],
    "name": "current_date",
    "op": "function",
  },
  "op": "add",
  "right": {
    "op": "interval",
    "unit": "day",
    "value": 1,
  },
}
`;

exports[`bind timestamp functions 7`] = `
{
  "left": {
    "args": [],
    "name": "current_timestamp",
    "op": "function",
  },
  "op": "add",
  "right": {
    "op": "interval",
    "unit": "day",
    "value": 1,
  },
}
`;

exports[`bind timestamp functions 8`] = `
{
  "left": {
    "expr": {
      "args": [],
      "name": "current_date",
      "op": "function",
    },
    "op": "cast",
    "type": "datetime",
  },
  "op": "gt",
  "right": {
    "args": [],
    "name": "current_timestamp",
    "op": "function",
  },
}
`;

exports[`bind timestamp functions 9`] = `
{
  "name": [
    "current_date",
  ],
  "op": "field",
  "type": {
    "type": "integer",
  },
}
`;
