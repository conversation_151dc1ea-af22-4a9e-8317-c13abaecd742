import { test, expect } from "vitest";
import { Token, Tokenizer } from "./lexer";

test("edge cases", () => {
  const cases: { query: string; expected: Token[] }[] = [
    {
      query: "filter",
      expected: ["filter"],
    },
    {
      query: "FILTER",
      expected: ["filter"],
    },
    {
      query: "filterabc",
      expected: ["word"],
    },
    {
      query: "100%",
      expected: ["percentNumber"],
    },
    {
      query: "100.3%",
      expected: ["percentNumber"],
    },
    {
      query: "100.3 % 50.2%",
      expected: ["number", "percent", "percentNumber"],
    },
    {
      query: "`hello`",
      expected: ["backtickQuotedString"],
    },
  ];

  for (const { query, expected } of cases) {
    const tokens = [];
    for (const token of new Tokenizer(query).tokens()) {
      tokens.push(token);
    }
    expect(tokens.map((t) => t.type)).toEqual(expected);
  }
});
