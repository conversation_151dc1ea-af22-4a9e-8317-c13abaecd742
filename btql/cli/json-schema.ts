import { boundExprSchema } from "#/binder";
import { mkdirSync, writeFileSync } from "fs";
import { z } from "zod";
import zodToJsonSchema from "zod-to-json-schema";

export function jsonSchema(args: { out: string }) {
  // mkdir -p out
  mkdirSync(args.out, { recursive: true });

  writeFileSync(
    `${args.out}/binder-ast.json`,
    JSON.stringify(
      zodToJsonSchema(z.null(), {
        definitions: {
          expr: boundExprSchema,
          //   from: boundFromSchema,
          //   unpivot: boundUnpivotExprSchema,
          //   sort: boundSortSchema,
          //   query: boundQuerySchema,
        },
        $refStrategy: "relative",
      }),
      null,
      2,
    ),
  );
}
