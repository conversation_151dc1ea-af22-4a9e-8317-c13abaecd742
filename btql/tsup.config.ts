import { defineConfig } from "tsup";

// https://github.com/egoist/tsup/issues/840 discusses how there can
// be an infinite loop bug with --watch, and we work around that by
// calling build with --dts.
export default defineConfig([
  {
    entry: ["src/index.ts"],
    format: ["cjs", "esm"],
    outDir: "dist",
  },
  {
    entry: ["cli/index.ts"],
    format: ["cjs", "esm"],
    outDir: "cli/dist",
  },
  {
    entry: ["parser/index.ts"],
    format: ["cjs", "esm"],
    outDir: "parser/dist",
  },
  {
    entry: ["schema/index.ts"],
    format: ["cjs", "esm"],
    outDir: "schema/dist",
  },
  {
    entry: ["binder/index.ts"],
    format: ["cjs", "esm"],
    outDir: "binder/dist",
  },
  {
    entry: ["planner/index.ts"],
    format: ["cjs", "esm"],
    outDir: "planner/dist",
  },
]);
