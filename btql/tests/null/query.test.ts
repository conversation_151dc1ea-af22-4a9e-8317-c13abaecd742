import path from "path";
import { beforeAll, expect, test, describe } from "vitest";
import { evaluateQuery, loadDataFile } from "../util";

describe("datasets", () => {
  beforeAll(async () => {
    await loadDataFile(path.join(__dirname, "/datasets.jsonl"), "datasets");
  });

  test("filter by is null/is not null", async () => {
    for (const { query, expected } of [
      {
        query: `
        from: datasets
        | select: id, description
        | filter: description is null
        | sort: id asc
      `,
        expected: [
          { id: "null", description: null },
          { id: "null_string", description: "null" },
        ],
      },
      {
        query: `
        from: datasets
        | select: id, description
        | filter: description is not null
        | sort: id asc
      `,
        expected: [
          { id: "empty_value", description: "" },
          { id: "valid", description: "valid description" },
        ],
      },
      {
        query: `
        from: datasets
        | select: id, num_examples
        | filter: num_examples is null
        | sort: id asc
      `,
        expected: [
          { id: "null", num_examples: null },
          { id: "null_string", num_examples: null },
        ],
      },
      {
        query: `
        from: datasets
        | select: id, num_examples
        | filter: num_examples is not null
        | sort: id asc
      `,
        expected: [
          { id: "empty_value", num_examples: 0 },
          { id: "valid", num_examples: 100 },
        ],
      },
    ]) {
      const result = await evaluateQuery(query, "datasets");
      expect(result).toMatchSnapshot();
      expect(result.resultData).toEqual(expected);
    }
  });
});

describe("logs", () => {
  beforeAll(async () => {
    await loadDataFile(path.join(__dirname, "/logs.jsonl"), "logs");
  });

  test("filter by is null/is not null", async () => {
    for (const { query, expected } of [
      {
        query: `
        from: logs
        | select: id, output
        | filter: output is null
        | sort: id asc
      `,
        expected: [
          { id: "null", output: null },
          { id: "null_string", output: null },
        ],
      },
      {
        query: `
        from: logs
        | select: id, output
        | filter: output is not null
      `,
        expected: [
          { id: "empty_string", output: "" },
          { id: "valid", output: "valid output" },
        ],
      },
    ]) {
      const result = await evaluateQuery(query, "logs");
      expect(result).toMatchSnapshot();
      expect(result.resultData).toEqual(expected);
    }
  });
});
