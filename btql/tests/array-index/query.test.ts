import path from "path";
import { beforeAll, expect, test } from "vitest";
import { evaluateQuery, loadDataFile } from "../util";

beforeAll(async () => {
  await loadDataFile(path.join(__dirname, "/data.jsonl"), "logs");
});

test("filter by array index", async () => {
  for (const { query, expected } of [
    {
      query: `
        from: logs
        | dimensions:
            id                       AS id,
            metadata.users[0]        AS first_user,
            metadata.users[-2][1]    AS some_char,
            metadata.users[2].foo[1] AS foo
        | sort: id
      `,
      expected: [
        {
          id: "a",
          first_user: "austin",
          some_char: "d",
          foo: null,
        },
        {
          id: "b",
          first_user: "brad",
          some_char: "a",
          foo: "y",
        },
        {
          id: "c",
          first_user: "cory",
          some_char: "b",
          foo: "z",
        },
      ],
    },
    {
      query: `
        from: logs
        | dimensions:
            id,
            metadata.users[2].foo[2] is null AS foo_is_null
        | filter: metadata.users[1][-1] = "b"
        | sort: id
      `,
      expected: [
        {
          id: "b",
          foo_is_null: true,
        },
        {
          id: "c",
          foo_is_null: false,
        },
      ],
    },
  ]) {
    const result = await evaluateQuery(query, "logs");
    expect(result).toMatchSnapshot();
    expect(result.resultData).toEqual(expected);
  }
});
