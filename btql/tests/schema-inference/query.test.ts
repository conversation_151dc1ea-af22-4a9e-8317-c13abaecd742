import { beforeAll, expect, test } from "vitest";
import { evaluateQuery, loadDataFile } from "../util";
import path from "path";

beforeAll(async () => {
  await loadDataFile(path.join(__dirname, "/data.json"), "logs");
});

test("filtered schema inference", async () => {
  for (const { query, type } of [
    {
      query:
        "from: logs | dimensions: metadata.model | measures: count(1) | sort: metadata.model",
      type: ["string", "integer"],
    },
    {
      query:
        "from: logs | dimensions: metadata.model | measures: count(1) | sort: metadata.model | filter: tags includes 'string-model'",
      type: "string",
    },
    {
      query:
        "from: logs | dimensions: metadata.model | measures: count(1) | sort: metadata.model | filter: tags includes 'number-model'",
      type: "integer",
    },
  ]) {
    const result = await evaluateQuery(query, "logs", { inferenceDepth: 2 });
    expect(result).toMatchSnapshot();
    expect(result.resultSchema.items.properties.model.type).toEqual(type);
  }
});

test("unpivot schema inference", async () => {
  const unpivotResult = await evaluateQuery(
    `
      from: logs
    | unpivot: scores AS (score, value)
    | dimensions: metadata.model, score
    | measures: count(1)
    | sort: metadata.model, score
    `,
    "logs",
    {
      inferenceDepth: 2,
    },
  );
  expect(unpivotResult).toMatchSnapshot();
  expect(unpivotResult.resultSchema.items.properties.model.type).toEqual([
    "string",
    "integer",
  ]);

  const pivotResult = await evaluateQuery(
    `
      from: logs
    | unpivot: scores AS (score, value)
    | dimensions: metadata.model
    | pivot: score
    | measures: count(1)
    | sort: metadata.model, score
    `,
    "logs",
    {
      inferenceDepth: 2,
    },
  );
  console.log(pivotResult.resultData);
  expect(pivotResult).toMatchSnapshot();
  console.log(pivotResult.resultSchema);
  expect(pivotResult.resultSchema.items.properties.model.type).toEqual([
    "string",
    "integer",
  ]);
});
