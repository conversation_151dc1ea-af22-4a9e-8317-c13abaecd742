import {
  DuckDBTableDefinition,
  jsonSchemaObjectSchema,
  LogicalSchema,
  PhysicalSchema,
} from "#/schema";
import { z } from "zod";
import fs from "fs";
import { v4 as uuidv4 } from "uuid";
import * as duckdb from "duckdb";
import { join, planQuery, sql } from "#/planner";
import { ParsedQuery, parseQuery } from "#/parser";
import { bindQuery, BoundQuery, ResponseSchema } from "#/binder";
import { PlannedQuery } from "#/planner/ast";
import { customZodToJsonSchema } from "#/schema";

const logsSchema = z.object({
  id: z.string(),
  span_id: z.string(),
  root_span_id: z.string(),
  _xact_id: z.string(),
  created: z.string(),
  input: z.unknown(),
  output: z.unknown(),
  expected: z.unknown(),
  scores: z.record(z.number().min(0).max(1)).default({}),
  metadata: z.record(z.unknown()).default({}),
  tags: z.array(z.string()).default([]),
});
const logsLogicalSchema = z.object({
  logs: z.array(logsSchema),
});

const logsPhysicalSchema: DuckDBTableDefinition["columns"] = {
  id: { path: ["id"], type: { type: "varchar" } },
  span_id: { path: ["span_id"], type: { type: "varchar" } },
  root_span_id: { path: ["root_span_id"], type: { type: "varchar" } },
  _xact_id: { path: ["_xact_id"], type: { type: "varchar" } },
  created: { path: ["created"], type: { type: "varchar" } },
  input: { path: ["input"], type: { type: "json" } },
  output: { path: ["output"], type: { type: "json" } },
  expected: { path: ["expected"], type: { type: "json" } },
  scores: {
    path: ["scores_map"],
    type: { type: "map", key: { type: "varchar" }, value: { type: "double" } },
  },
  metadata: { path: ["metadata"], type: { type: "json" } },
  tags: { path: ["tags"], type: { type: "json" } },
};

const datasetsSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  last_updated: z.string().datetime(),
  num_examples: z.number().nullish(),
  metadata: z.record(z.any()).optional(),
});
const datasetsLogicalSchema = z.object({
  datasets: z.array(datasetsSchema),
});

const datasetsPhysicalSchema: DuckDBTableDefinition["columns"] = {
  id: { path: ["id"], type: { type: "varchar" } },
  span_id: { path: ["span_id"], type: { type: "varchar" } },
  root_span_id: { path: ["root_span_id"], type: { type: "varchar" } },
  _xact_id: { path: ["_xact_id"], type: { type: "varchar" } },
  created: { path: ["created"], type: { type: "varchar" } },
  name: { path: ["name"], type: { type: "varchar" } },
  description: { path: ["description"], type: { type: "varchar" } },
  last_updated: { path: ["last_updated"], type: { type: "varchar" } },
  num_examples: { path: ["num_examples"], type: { type: "bigint" } },
  metadata: { path: ["metadata"], type: { type: "json" } },
};

const LOGICAL_SCHEMAS: { [key: string]: LogicalSchema } = {
  logs: jsonSchemaObjectSchema.parse(customZodToJsonSchema(logsLogicalSchema)),
  datasets: jsonSchemaObjectSchema.parse(
    customZodToJsonSchema(datasetsLogicalSchema),
  ),
};

const PHYSICAL_SCHEMA_TABLES = {
  logs: { columns: logsPhysicalSchema },
  datasets: { columns: datasetsPhysicalSchema },
};
const DUCKDB_PHYSICAL_SCHEMA: PhysicalSchema = {
  type: "duckdb",
  tables: PHYSICAL_SCHEMA_TABLES,
};

export function loadDataFile(
  path: string,
  tableName: keyof typeof PHYSICAL_SCHEMA_TABLES,
) {
  switch (tableName) {
    case "logs":
      return loadLogsDataFile(path);
    case "datasets":
      return loadDatasetsDataFile(path);
    default:
      const _: never = tableName;
  }
}

async function loadLogsDataFile(path: string) {
  const duckConn = getDuckDBConn();
  await duckq(duckConn, sql`DROP TABLE IF EXISTS logs`.toPlainStringQuery());
  await duckq(
    duckConn,
    `CREATE TABLE logs (
    id VARCHAR,
    span_id VARCHAR,
    root_span_id VARCHAR,
    _xact_id VARCHAR,
    created VARCHAR,
    input JSON,
    output JSON,
    expected JSON,
    scores_map MAP(VARCHAR, DOUBLE),
    metadata JSON,
    tags JSON,
  )`,
  );
  const data = fs.readFileSync(path, "utf8");
  for (const line of data.split("\n")) {
    const row = parseLine(line, logsSchema);
    if (row == null) {
      continue;
    }
    const query = sql`
      INSERT INTO logs (id, span_id, root_span_id, _xact_id, created, input, output, expected, metadata, tags, scores_map)
      VALUES (${row.id}, ${row.span_id}, ${row.root_span_id}, ${row._xact_id}, ${row.created},
              ${row.input}, ${row.output}, ${row.expected}, ${row.metadata}, ${row.tags}, MAP {
                ${join(
                  Object.entries(row.scores).map(
                    ([key, value]) => sql`${key}: ${value}`,
                  ),
                  ",\n",
                )}
              })
    `;
    await duckq(duckConn, query.toPlainStringQuery());
  }
}

async function loadDatasetsDataFile(path: string) {
  const duckConn = getDuckDBConn();
  await duckq(
    duckConn,
    sql`DROP TABLE IF EXISTS datasets`.toPlainStringQuery(),
  );
  await duckq(
    duckConn,
    `CREATE TABLE datasets (
      id VARCHAR,
      span_id VARCHAR,
      root_span_id VARCHAR,
      _xact_id VARCHAR,
      created VARCHAR,
      name VARCHAR,
      description VARCHAR,
      last_updated VARCHAR,
      num_examples BIGINT,
      metadata JSON,
  )`,
  );
  const data = fs.readFileSync(path, "utf8");
  for (const line of data.split("\n")) {
    const row = parseLine(line, datasetsSchema);
    if (row == null) {
      continue;
    }
    const query = sql`
      INSERT INTO datasets (id, span_id, root_span_id, _xact_id, created, name, description, last_updated, num_examples, metadata)
      VALUES (${row.id}, ${row.span_id}, ${row.root_span_id}, ${row._xact_id}, ${row.created},
              ${row.name}, ${row.description}, ${row.last_updated}, ${row.num_examples}, ${row.metadata})
    `;
    await duckq(duckConn, query.toPlainStringQuery());
  }
}

function parseLine(line: string, schema: z.Schema) {
  if (!line.trim()) {
    return;
  }
  const parsedLine = JSON.parse(line);
  for (const uuidField of ["id", "span_id", "root_span_id"]) {
    if (!parsedLine[uuidField]) {
      parsedLine[uuidField] = uuidv4();
    }
  }
  if (!parsedLine._xact_id) {
    parsedLine._xact_id = "1";
  }
  if (!parsedLine.created) {
    parsedLine.created = new Date().toISOString();
  }
  if (!parsedLine.last_updated) {
    parsedLine.last_updated = new Date().toISOString();
  }
  // Make sure the data is valid
  const parsed = schema.safeParse(parsedLine);
  if (!parsed.success) {
    console.warn("failed to parse line", line, parsed.error);
    return;
  }

  return parsed.data;
}

export type EvaluateStage = "parse" | "bind" | "plan" | "execute";
export interface EvaluateResult<Stage extends EvaluateStage> {
  parsed: Stage extends "parse" | "bind" | "plan" | "execute"
    ? ParsedQuery
    : never;
  bound: Stage extends "bind" | "plan" | "execute" ? BoundQuery : never;
  plan: Stage extends "plan" | "execute" ? PlannedQuery : never;
  resultData: Stage extends "execute" ? Record<string, unknown>[] : never;
  resultSchema: Stage extends "execute" ? ResponseSchema : never;
}

export async function evaluateQuery<Stage extends EvaluateStage>(
  query: string,
  tableName: keyof typeof LOGICAL_SCHEMAS,
  opts?: {
    stage?: Stage;
    inferenceDepth?: number;
  },
): Promise<EvaluateResult<Stage>> {
  const stage = opts?.stage ?? "execute";

  const parsed = parseQuery(query);
  if (stage === "parse") {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return { parsed } as EvaluateResult<Stage>;
  }
  const bound = bindQuery({
    query: parsed,
    schema: LOGICAL_SCHEMAS[tableName],
  });
  if (stage === "bind") {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return { parsed, bound } as EvaluateResult<Stage>;
  }
  const plan = planQuery(DUCKDB_PHYSICAL_SCHEMA, bound, {
    parseJSONFields: true,
    inferenceDepth: opts?.inferenceDepth,
  });
  if (stage === "plan") {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return { parsed, bound, plan } as EvaluateResult<Stage>;
  }
  const resultData = await duckq(
    getDuckDBConn(),
    plan.sql.toPlainStringQuery(),
  );

  const processedRows: Record<string, unknown>[] = [];
  for await (const row of plan.postProcess(rowIterator(resultData))) {
    processedRows.push(row);
  }

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return {
    parsed,
    bound,
    plan,
    resultData: processedRows,
    resultSchema: plan.schema,
  } as EvaluateResult<Stage>;
}

export async function duckq(
  con: duckdb.Connection,
  sql: string,
): Promise<duckdb.TableData> {
  return new Promise((resolve, reject) => {
    con.all(sql, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

let _duckdb_db: duckdb.Database | null = null;
export function getDuckDBConn() {
  if (!_duckdb_db) {
    _duckdb_db = new duckdb.Database(":memory:");
  }
  return _duckdb_db.connect();
}

export async function* rowIterator(rows: Record<string, unknown>[]) {
  for (const row of rows) {
    yield row;
  }
}
