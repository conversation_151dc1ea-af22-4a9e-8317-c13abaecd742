import zodToJsonSchema, {
  Options,
  Targets,
  ignoreOverride,
} from "zod-to-json-schema";

export function customZodToJsonSchema<Target extends Targets = "jsonSchema7">(
  schema: Parameters<typeof zodToJsonSchema<Target>>[0],
  options?: Partial<Options<Target>>,
): ReturnType<typeof zodToJsonSchema<Target>> {
  return zodToJsonSchema(schema, {
    $refStrategy: "none",
    // It turns out that both this library and the zod-to-openapi library rely
    // on a schema being a ZodString with a datetime check in order to add the
    // format date-time. But now that our `datetimeStringSchema` in our
    // typespecs is a `ZodEffects` object which sanitizes the date string, it
    // is no longer auto-detected as a date-time. So we must override the
    // format setting for such objects.
    override: (def, _refs) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const defAny: any = def;
      if (
        defAny instanceof Object &&
        defAny?.typeName === "ZodEffects" &&
        defAny?.openapi?.metadata?.format === "date-time"
      ) {
        return {
          type: "string",
          format: "date-time",
          description: defAny?.description ?? "Creation date",
        };
      } else {
        return ignoreOverride;
      }
    },
    ...options,
  });
}
