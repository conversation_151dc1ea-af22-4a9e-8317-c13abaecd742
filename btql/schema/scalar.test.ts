import { expect, test } from "vitest";
import {
  deriveScalarTypes,
  scalarTypes,
  scalarTypeSchema,
  scalarTypeToLogicalSchema,
} from "./scalar";

test("scalar types", () => {
  for (const type of scalarTypes) {
    const logicalSchema = scalarTypeToLogicalSchema(type);
    const roundtripped = deriveScalarTypes(logicalSchema);
    expect(roundtripped).toEqual(
      type === "unknown" ? scalarTypeSchema.options : [type],
    );
  }
});
