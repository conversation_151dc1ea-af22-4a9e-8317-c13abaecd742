import { z } from "zod";
import { JSONSchema, JSONSchemaObject, SchemaArray } from "./json-schema";

export const scalarTypes = [
  "unknown",
  "array",
  "boolean",
  "integer",
  "null",
  "number",
  "object",
  "string",
  "date",
  "datetime",
  "interval",
] as const;
export const scalarTypeSchema = z.enum(scalarTypes);
export type ScalarType = z.infer<typeof scalarTypeSchema>;

export function scalarTypeToLogicalSchema(
  scalarType: ScalarType,
): JSONSchemaObject {
  switch (scalarType) {
    case "unknown":
      return {};
    case "array":
    case "boolean":
    case "integer":
    case "object":
    case "null":
    case "number":
    case "string":
      return { type: scalarType };
    case "date":
      return { type: "string", format: "date" };
    case "datetime":
      return { type: "string", format: "date-time" };
    case "interval":
      return { type: "string", format: "duration" };
  }
}

// Returns an array of _any_ of the scalar types that the schema could represent.
export function deriveScalarTypes(
  schema: JSONSchema | SchemaArray,
): ScalarType[] {
  if (typeof schema !== "object") {
    // true means anything, false means nothing
    return schema ? [...scalarTypes] : ["null"];
  } else if (
    Array.isArray(schema) ||
    Array.isArray(schema.type) ||
    schema.anyOf ||
    schema.oneOf
  ) {
    const typeArray = Array.isArray(schema)
      ? schema
      : schema.type || schema.anyOf || schema.oneOf;
    if (!Array.isArray(typeArray)) {
      throw new Error("Impossible");
    }

    const subTypes = typeArray.flatMap((t) =>
      typeof t === "string" ? [t] : deriveScalarTypes(t),
    );
    return [...new Set(subTypes)];
  } else if (schema.type && !Array.isArray(schema.type)) {
    return [
      schema.type === "string" && schema.format === "date-time"
        ? "datetime"
        : schema.type === "string" && schema.format === "date"
          ? "date"
          : schema.type === "string" && schema.format === "duration"
            ? "interval"
            : schema.type,
    ];
  } else if (schema.allOf) {
    const subTypes = schema.allOf.map((t) => deriveScalarTypes(t));
    const intersection = subTypes.reduce(
      (acc, curr) => {
        return acc.filter((x) => curr.includes(x));
      },
      [...scalarTypes],
    );
    return intersection.length > 0 ? intersection : ["null"];
  } else if (schema.properties || schema.additionalProperties) {
    return ["object"];
  } else if (schema.items || schema.additionalItems) {
    return ["array"];
  } else {
    // We just don't know
    return [...scalarTypes];
  }
}

export function isScalar(schema: JSONSchema): boolean {
  const simpleTypes = deriveScalarTypes(schema);
  return (
    !simpleTypes.includes("array") &&
    !simpleTypes.includes("object") &&
    !simpleTypes.includes("unknown")
  );
}
