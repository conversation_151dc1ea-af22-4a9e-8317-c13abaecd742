import { expect, test } from "vitest";
import { DEFAULT_INFERENCE_DEPTH, inferSchema } from "./inference";
import { JSONSchemaObject } from "./json-schema";

// test.only("single case", () => {
// });

test("infer schema", () => {
  expect(
    inferSchema({ schema: {}, value: 1, depth: DEFAULT_INFERENCE_DEPTH }),
  ).toEqual({ type: "integer" });
  expect(
    inferSchema({ schema: {}, value: "hello", depth: DEFAULT_INFERENCE_DEPTH }),
  ).toEqual({
    type: "string",
  });
  expect(
    inferSchema({ schema: {}, value: true, depth: DEFAULT_INFERENCE_DEPTH }),
  ).toEqual({
    type: "boolean",
  });
  expect(
    inferSchema({ schema: {}, value: null, depth: DEFAULT_INFERENCE_DEPTH }),
  ).toEqual({
    type: "null",
  });
  expect(
    inferSchema({ schema: {}, value: 3.14, depth: DEFAULT_INFERENCE_DEPTH }),
  ).toEqual({
    type: "number",
  });
  expect(
    inferSchema({
      schema: {},
      value: [1, 2, 3],
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: "array",
    items: { type: "integer" },
  });
  expect(
    inferSchema({
      schema: {},
      value: ["a", "b", "c"],
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: "array",
    items: { type: "string" },
  });
  expect(
    inferSchema({
      schema: {},
      value: { a: 1, b: "hello" },
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: "object",
    properties: {
      a: { type: "integer" },
      b: { type: "string" },
    },
  });
  expect(
    inferSchema({
      schema: {},
      value: { nested: { a: 1, b: [true, false] } },
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: "object",
    properties: {
      nested: {
        type: "object",
        properties: {
          a: { type: "integer" },
          b: { type: "array", items: { type: "boolean" } },
        },
      },
    },
  });
  expect(
    inferSchema({
      schema: {},
      value: [{ a: 1 }, { a: 2, b: "hello" }],
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: "array",
    items: {
      type: "object",
      properties: {
        a: { type: "integer" },
        b: { type: "string" },
      },
    },
  });
  expect(
    inferSchema({
      schema: {
        type: "object",
        properties: Object.fromEntries(
          ["prop_1", "prop_2"].map((c) => [c, {}]),
        ),
      },
      value: { prop_1: 1, prop_2: 2, prop_3: "hello" },
      depth: 1,
      ignoreAdditionalProperties: true,
    }),
  ).toEqual({
    type: "object",
    properties: {
      prop_1: { type: "integer" },
      prop_2: { type: "integer" },
    },
  });
});

test("conflicting types", () => {
  expect(
    inferSchema({
      schema: { type: "integer" },
      value: "hello",
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: ["integer", "string"],
  });

  expect(
    inferSchema({
      schema: { type: "string" },
      value: 42,
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: ["string", "integer"],
  });

  expect(
    inferSchema({
      schema: { type: "boolean" },
      value: "true",
      depth: DEFAULT_INFERENCE_DEPTH,
    }),
  ).toEqual({
    type: ["boolean", "string"],
  });

  const mixedArray = [1, "two", true, { key: "value" }];
  let mixedSchema = {};
  for (const item of mixedArray) {
    mixedSchema = inferSchema({
      schema: mixedSchema,
      value: item,
      depth: DEFAULT_INFERENCE_DEPTH,
    });
  }
  expect(mixedSchema).toEqual({});

  const mixedArraySchema = inferSchema({
    schema: {},
    value: mixedArray,
    depth: DEFAULT_INFERENCE_DEPTH,
  });
  console.log(mixedArraySchema);
  expect(mixedArraySchema).toEqual({ type: "array", items: {} });

  const nestedObjects = [
    { a: 1, b: "hello", e: 1 },
    { a: 2, b: "world", c: true, e: "foo" },
    { a: 3, b: "!", c: false, d: { nested: "object" } },
  ];
  let nestedSchema: JSONSchemaObject = {};
  for (const obj of nestedObjects) {
    nestedSchema = inferSchema({
      schema: nestedSchema,
      value: obj,
      depth: DEFAULT_INFERENCE_DEPTH,
    });
  }
  expect(nestedSchema).toEqual({
    type: "object",
    properties: {
      a: { type: "integer" },
      b: { type: "string" },
      c: { type: "boolean" },
      d: {
        type: "object",
        properties: {
          nested: { type: "string" },
        },
      },
      e: { type: ["integer", "string"] },
    },
  });

  const nestedObjectsSchema = inferSchema({
    schema: {},
    value: nestedObjects,
    depth: 3,
  });
  expect(nestedObjectsSchema).toEqual({
    type: "array",
    items: nestedSchema,
  });

  const nestedObjectsSchemaLowDepth = inferSchema({
    schema: {},
    value: nestedObjects,
    depth: 2,
  });
  expect(nestedObjectsSchemaLowDepth).toEqual({
    type: "array",
    items: {
      ...nestedSchema,
      properties: {
        ...(nestedSchema.properties ?? {}),
        d: { type: "object" },
      },
    },
  });

  const nestedObjectsSchemaNoDepth = inferSchema({
    schema: {},
    value: nestedObjects,
    depth: 1,
  });
  expect(nestedObjectsSchemaNoDepth).toEqual({
    type: "array",
    items: {
      type: "object",
    },
  });
});

test("updated row", () => {
  const row = { model: "basdf", "count(1)": BigInt(1) };
  const schema: JSONSchemaObject = {
    type: "object",
    properties: {
      model: {},
      "count(1)": {
        type: "integer",
      },
    },
  };
  const updatedSchema = inferSchema({
    schema,
    value: row,
    depth: DEFAULT_INFERENCE_DEPTH,
  });
  expect(updatedSchema).toEqual({
    type: "object",
    properties: {
      model: {
        type: "string",
      },
      "count(1)": {
        type: "integer",
      },
    },
  });
});
