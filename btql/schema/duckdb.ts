import { z } from "zod";

export const duckdbScalarTypes = [
  "bigint",
  "bit",
  "blob",
  "boolean",
  "date",
  "double",
  "hugeint",
  "integer",
  "interval",
  "real",
  "smallint",
  "time",
  "timestamp with time zone",
  "timestamp",
  "tinyint",
  "ubigint",
  "uhugeint",
  "uinteger",
  "usmallint",
  "utinyint",
  "uuid",
  "varchar",
  "json",
] as const;

export const duckdbScalarSchema = z.union([
  z.strictObject({
    type: z.enum(duckdbScalarTypes),
  }),
  z.strictObject({
    type: z.literal("decimal"),
    prec: z.number().int().optional(),
    scale: z.number().int().optional(),
  }),
]);
export type DuckDBScalar = z.infer<typeof duckdbScalarSchema>;

export type DuckDBArray = {
  type: "array";
  items: DuckDBType;
};
export const duckDBArraySchema: z.ZodType<DuckDBArray> = z.object({
  type: z.literal("array"),
  items: z.lazy(() => duckDBTypeSchema),
});

export type DuckDBStruct = {
  type: "struct";
  fields: Record<string, DuckDBType>;
};
export const duckDBStructSchema: z.ZodType<DuckDBStruct> = z.object({
  type: z.literal("struct"),
  fields: z.lazy(() => z.record(duckDBTypeSchema)),
});

export type DuckDBMap = {
  type: "map";
  key: DuckDBScalar;
  value: DuckDBType;
};
export const duckDBMapSchema: z.ZodType<DuckDBMap> = z.object({
  type: z.literal("map"),
  key: z.lazy(() => duckdbScalarSchema),
  value: z.lazy(() => duckDBTypeSchema),
});

// This is a btql-specific type that represents a struct whose field names are unknown
// but whose values are known to be of a specific type (like scores and metrics). It allows
// us to generate code exactly like a struct, but without specifying the field names.
export type DuckDBStructMap = {
  type: "struct_map";
  value: DuckDBType;
};

export const duckDBStructMapSchema: z.ZodType<DuckDBStructMap> = z.object({
  type: z.literal("struct_map"),
  value: z.lazy(() => duckDBTypeSchema),
});

export type DuckDBType =
  | DuckDBScalar
  | DuckDBArray
  | DuckDBStruct
  | DuckDBMap
  | DuckDBStructMap;

export const duckDBTypeSchema: z.ZodType<DuckDBType> = z.union([
  duckdbScalarSchema,
  duckDBArraySchema,
  duckDBMapSchema,
  duckDBStructSchema,
  duckDBStructMapSchema,
]);

export const duckDBColumnDefinition = z.strictObject({
  path: z.array(z.string()),
  type: duckDBTypeSchema,
});
export type DuckDBColumnDefinition = z.infer<typeof duckDBColumnDefinition>;

export const duckdbTableDefinition = z.strictObject({
  columns: z.record(duckDBColumnDefinition),
});
export type DuckDBTableDefinition = z.infer<typeof duckdbTableDefinition>;
