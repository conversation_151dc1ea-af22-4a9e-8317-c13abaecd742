import { jsonSchemaObjectSchema, JSONSchemaObject } from "./json-schema";
import { duckdbTableDefinition } from "./duckdb";
import { z } from "zod";
import { postgresTableDefinition } from "./postgres";
import { clickhouseTableDefinition } from "./clickhouse";

export * from "./clickhouse";
export * from "./duckdb";
export * from "./json-schema";
export * from "./json-util";
export * from "./parse";
export * from "./postgres";
export * from "./scalar";
export * from "./custom_zod_to_json_schema";
export * from "./inference";

export type LogicalSchema = JSONSchemaObject;

export function parseLogicalSchema(schema: unknown): LogicalSchema {
  return jsonSchemaObjectSchema.parse(schema);
}

export const tableDefinitionSchema = z.union([
  duckdbTableDefinition,
  postgresTableDefinition,
  clickhouseTableDefinition,
]);
export type TableDefinition = z.infer<typeof tableDefinitionSchema>;

export const physicalSchemaSchema = z.union([
  z.strictObject({
    type: z.literal("duckdb"),
    tables: z.record(duckdbTableDefinition),
  }),
  z.strictObject({
    type: z.literal("postgres"),
    tables: z.record(postgresTableDefinition),
  }),
  z.strictObject({
    type: z.literal("clickhouse"),
    tables: z.record(clickhouseTableDefinition),
  }),
]);
export type PhysicalSchema = z.infer<typeof physicalSchemaSchema>;
