import { JSONSchema } from "./json-schema";
import Ajv from "ajv";

export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
  }
}

const ajv = new Ajv({ coerceTypes: false });

ajv.addFormat("uuid", {
  type: "string",
  validate: (uuid) =>
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
      uuid,
    ),
});

ajv.addFormat("date-time", {
  type: "string",
  validate: (date) => !isNaN(Date.parse(date)),
});

export function validateSchema({
  data,
  schema,
  strict,
}: {
  data: unknown;
  schema: JSONSchema;
  strict?: boolean;
}): unknown {
  try {
    const validate = ajv.compile(schema);
    let valid = validate(data);

    if (!valid) {
      // It works out to be very difficult to validate BigInt ahead of time, so we'll post-validate it instead.
      let hasError = false;
      validate.errors?.forEach((error) => {
        if (error.keyword === "type" && error.params.type === "integer") {
          if (
            !(
              typeof data === "object" &&
              data &&
              // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              typeof (data as any)[error.instancePath.slice(1)] === "bigint"
            )
          ) {
            console.log(error);
            hasError = true;
          }
        }
      });
      valid = !hasError;
    }

    if (!valid) {
      const errmsg = `Validation failed. Some types may not be converted:\n${JSON.stringify(
        validate.errors,
        null,
        2,
      )}`;
      if (strict) {
        console.error(errmsg);
        throw new ValidationError(errmsg);
      } else {
        console.warn(errmsg);
      }
    }
  } catch (e) {
    throw new ValidationError(`Invalid data: ${e}`);
  }
  return data;
}
