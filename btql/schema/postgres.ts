import { z } from "zod";

export const postgresScalarTypes = [
  "bigint",
  "bigserial",
  "bit",
  "bit varying",
  "boolean",
  "box",
  "bytea",
  "character",
  "character varying",
  "cidr",
  "circle",
  "date",
  "double precision",
  "inet",
  "integer",
  "interval",
  "json",
  "jsonb",
  "line",
  "lseg",
  "macaddr",
  "macaddr8",
  "money",
  "path",
  "pg_lsn",
  "pg_snapshot",
  "point",
  "polygon",
  "real",
  "smallint",
  "smallserial",
  "serial",
  "text",
  "time",
  "time with time zone",
  "timestamp",
  "timestamptz",
  "timestamp with time zone",
  "tsquery",
  "tsvector",
  "txid_snapshot",
  "uuid",
  "xml",
] as const;

export type PostgresScalarType = (typeof postgresScalarTypes)[number];

export const postgresScalarSchema = z.union([
  z.strictObject({
    type: z.enum(postgresScalarTypes),
  }),
  z.strictObject({
    type: z.literal("numeric"),
    prec: z.number().int().optional(),
    scale: z.number().int().optional(),
  }),
]);
export type PostgresScalar = z.infer<typeof postgresScalarSchema>;

// We don't do any of the recursive types in Postgres for now
export type PostgresType = PostgresScalar;
export const postgresTypeSchema: z.ZodType<PostgresType> = postgresScalarSchema;

export const postgresColumnDefinition = z.strictObject({
  path: z.array(z.string()),
  expr: z.string().optional(),
  type: postgresTypeSchema,
});
export type PostgresColumnDefinition = z.infer<typeof postgresColumnDefinition>;

export const postgresTableDefinition = z.strictObject({
  columns: z.record(postgresColumnDefinition),
});
export type PostgresTableDefinition = z.infer<typeof postgresTableDefinition>;
