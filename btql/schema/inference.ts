import { literalValueToScalarType } from "#/binder/types";
import {
  ArrayOfSimpleTypes,
  JSONSchema,
  JSONSchemaObject,
  jsonSchemaObjectSchema,
} from "./json-schema";
import { toSchemaObject } from "./json-util";
import { scalarTypeToLogicalSchema } from "./scalar";

export const DEFAULT_INFERENCE_DEPTH = 2;

// Updates the schema to (a) be more specific if the type is unknown
// or (b) more general if the type is known, but too specific. It may
// update the schema in place, but you must use the returned value as
// the latest schema.
export function inferSchema({
  schema,
  value,
  depth,
  ignoreAdditionalProperties = false,
}: {
  schema: JSONSchemaObject;
  value: unknown;
  depth: number;
  ignoreAdditionalProperties?: boolean;
}): JSONSchemaObject {
  const valueType = literalValueToScalarType(value);
  const valueSchema = scalarTypeToLogicalSchema(valueType);

  if (valueType === "object" && depth > 0) {
    const rootProperties = new Set(Object.keys(schema?.properties ?? {}));
    if (!value || typeof value !== "object") {
      throw new Error(`value is not an object: ${JSON.stringify(value)}`);
    }
    valueSchema.properties = valueSchema.properties ?? {};
    for (const [k, v] of Object.entries(value)) {
      if (ignoreAdditionalProperties && !rootProperties.has(k)) {
        continue;
      }
      valueSchema.properties[k] = inferSchema({
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        schema: valueSchema.properties[k] as JSONSchemaObject,
        value: v,
        depth: depth - 1,
      });
    }
  } else if (valueType === "array") {
    if (!value || !Array.isArray(value)) {
      throw new Error(`value is not an array: ${JSON.stringify(value)}`);
    }
    let itemsSchema = toSchemaObject(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      (valueSchema.items ?? {}) as JSONSchemaObject,
    );
    for (const v of value) {
      itemsSchema = inferSchema({
        schema: itemsSchema,
        value: v,
        depth: depth - 1,
      });
    }
    valueSchema.items = itemsSchema;
  }

  let result = undefined;
  try {
    result = mergeSchemas(schema, valueSchema);
  } catch (e) {
    console.warn("mergeInto failed", e);
  }
  if (result === undefined) {
    console.warn("mergeInto returned undefined, using original schema");
    return schema;
  }
  return result;
}

export function mergeSchemas(
  target: JSONSchema | undefined,
  source: JSONSchema | undefined,
): JSONSchemaObject | undefined {
  if (target === undefined) {
    return source ? toSchemaObject(source) : undefined;
  } else if (source === undefined) {
    return toSchemaObject(target);
  }

  target = toSchemaObject(target);
  source = toSchemaObject(source);

  if (target.properties || source.properties) {
    const allProperties = new Set<string>();
    for (const prop of Object.keys(target.properties ?? {})) {
      allProperties.add(prop);
    }
    for (const prop of Object.keys(source.properties ?? {})) {
      allProperties.add(prop);
    }
    target.properties = target.properties ?? {};
    for (const prop of allProperties) {
      target.properties[prop] = mergeSchemas(
        jsonSchemaObjectSchema.optional().parse(target.properties[prop]),
        jsonSchemaObjectSchema.optional().parse(source.properties?.[prop]),
      );
    }
  }

  if (target.additionalProperties || source.additionalProperties) {
    target.additionalProperties = mergeSchemas(
      target.additionalProperties,
      source.additionalProperties,
    );
  }

  if (target.items || source.items) {
    target.items = mergeSchemas(
      jsonSchemaObjectSchema.optional().parse(target.items),
      jsonSchemaObjectSchema.optional().parse(source.items),
    );
  }

  if (target.additionalItems || source.additionalItems) {
    target.additionalItems = mergeSchemas(
      target.additionalItems,
      source.additionalItems,
    );
  }

  if (target.properties || target.additionalProperties) {
    if (target.type && target.type !== "object") {
      // This is after merging, so it's basically testing that there's a discrepancy
      // between the target type (a non-object) and the source type (which has properties)
      return {};
    }
    target.type = "object";
  } else if (target.items || target.additionalItems) {
    if (target.type && target.type !== "array") {
      // This is after merging, so it's basically testing that there's a discrepancy
      // between the target type (a non-array) and the source type (which has properties)
      return {};
    }
    target.type = "array";
  } else {
    const mergedType = mergeScalarTypes(target.type, source.type);
    if (mergedType !== undefined) {
      target.type = mergedType;
    }
  }
  return target;
}

function toArrayOfSimpleTypes(
  type: NonNullable<JSONSchemaObject["type"]>,
): ArrayOfSimpleTypes | undefined {
  return Array.isArray(type) ? type : [type];
}

function mergeScalarTypes(
  target: JSONSchemaObject["type"],
  source: JSONSchemaObject["type"],
): JSONSchemaObject["type"] {
  if (target === undefined && source === undefined) {
    return undefined;
  } else if (source === undefined) {
    return target;
  } else if (target === undefined || target === source) {
    return source;
  } else {
    const targets = toArrayOfSimpleTypes(target) ?? [];
    const sources = toArrayOfSimpleTypes(source) ?? [];
    const ret = [...new Set([...targets, ...sources])];
    if (ret.length === 1) {
      return ret[0];
    }
    return ret;
  }
}
