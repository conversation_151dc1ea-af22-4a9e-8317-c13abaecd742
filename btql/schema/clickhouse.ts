import { z } from "zod";
/*
Integer types: signed and unsigned integers (UInt8, UInt16, UInt32, UInt64, UInt128, UInt256, Int8, Int16, Int32, Int64, Int128, Int256)
Floating-point numbers: floats(<PERSON>loat32 and <PERSON>loat64) and Decimal values
Boolean: ClickHouse has a Boolean type
Strings: String and FixedString
Dates: use Date and Date32 for days, and DateTime and DateTime64 for instances in time
JSON: the JSON object stores a JSON document in a single column
UUID: a performant option for storing UUID values
Low cardinality types: use an Enum when you have a handful of unique values, or use LowCardinality when you have up to 10,000 unique values of a column
Arrays: any column can be defined as an Array of values
Maps: use Map for storing key/value pairs
Aggregation function types: use SimpleAggregateFunction and AggregateFunction for storing the intermediate status of aggregate function results
Nested data structures: A Nested data structure is like a table inside a cell
Tuples: A Tuple of elements, each having an individual type.
Nullable: Nullable allows you to store a value as NULL when a value is "missing" (instead of the column settings its default value for the data type)
IP addresses: use IPv4 and IPv6 to efficiently store IP addresses
Geo types: for geographical data, including Point, Ring, Polygon and MultiPolygon
Special data types: including Expression, Set, Nothing and Interval
*/

export const clickhouseScalarTypes = [
  "UInt8",
  "UInt16",
  "UInt32",
  "UInt64",
  "UInt128",
  "UInt256",
  "Int8",
  "Int16",
  "Int32",
  "Int64",
  "Int128",
  "Int256",
  "Float32",
  "Float64",
  // "Decimal", (P, S)
  "Boolean",
  "String",
  "FixedString",
  "Date",
  "DateTime",
  "DateTime64",
  "JSON",
  "UUID",
  // "Enum", Enum('a' = 1, 'b' = 2, 'c' = 3)
  // "LowCardinality", LowCardinality(String)
  // "Array", Array(String)
  // "Map", Map(String, Int32)
  // "SimpleAggregateFunction", SimpleAggregateFunction(String, UInt64)
  // "AggregateFunction", AggregateFunction(String, UInt64)
  // "Nested", Nested(ID UInt32, Name String)
  // "Tuple", Tuple(UInt32, String)
  // "Nullable", Nullable(String)
  "IPv4",
  "IPv6",
  "Point",
  "Ring",
  "Polygon",
  "MultiPolygon",
  // "Expression", Expression(String)
  // "Set", Set(String)
  "Nothing",
  "Interval",
] as const;

export const clickhouseScalarSchema = z.union([
  z.strictObject({
    type: z.enum(clickhouseScalarTypes),
  }),
  z.strictObject({
    type: z.literal("Decimal"),
    precision: z.number(),
    scale: z.number(),
  }),
]);

export type ClickhouseScalar = z.infer<typeof clickhouseScalarSchema>;

export interface ClickhouseArray {
  type: "Array";
  items: ClickhouseType;
}
export const clickhouseArraySchema: z.ZodType<ClickhouseArray> = z.object({
  type: z.literal("Array"),
  items: z.lazy(() => clickhouseTypeSchema),
});

export interface ClickhouseNullable {
  type: "Nullable";
  inner: ClickhouseType;
}
export const clickhouseNullableSchema: z.ZodType<ClickhouseNullable> = z.object(
  {
    type: z.literal("Nullable"),
    inner: z.lazy(() => clickhouseTypeSchema),
  },
);

export interface ClickhouseEnum {
  type: "Enum";
  values: Record<string, number>;
}

export const clickhouseEnumSchema: z.ZodType<ClickhouseEnum> = z.object({
  type: z.literal("Enum"),
  values: z.record(z.number()),
});

export interface ClickhouseLowCardinality {
  type: "LowCardinality";
  inner: ClickhouseType;
}

export const clickhouseLowCardinalitySchema: z.ZodType<ClickhouseLowCardinality> =
  z.object({
    type: z.literal("LowCardinality"),
    inner: z.lazy(() => clickhouseTypeSchema),
  });

export interface ClickhouseMap {
  type: "Map";
  key: ClickhouseType;
  value: ClickhouseType;
}

export const clickhouseMapSchema: z.ZodType<ClickhouseMap> = z.object({
  type: z.literal("Map"),
  key: z.lazy(() => clickhouseTypeSchema),
  value: z.lazy(() => clickhouseTypeSchema),
});

export type ClickhouseType =
  | ClickhouseScalar
  | ClickhouseArray
  | ClickhouseNullable
  | ClickhouseEnum
  | ClickhouseLowCardinality
  | ClickhouseMap;

export const clickhouseTypeSchema: z.ZodType<ClickhouseType> = z.union([
  clickhouseScalarSchema,
  clickhouseArraySchema,
  clickhouseNullableSchema,
  clickhouseEnumSchema,
  clickhouseLowCardinalitySchema,
  clickhouseMapSchema,
]);
/* TODO
  | ClickhouseSimpleAggregateFunction
  | ClickhouseAggregateFunction
  | ClickhouseNested
  | ClickhouseTuple
  | ClickhouseSet
  | ClickhouseExpression
  */

export const clickhouseColumnDefinition = z.strictObject({
  path: z.array(z.string()),
  materializedMapPath: z.array(z.string()).optional(),
  type: clickhouseTypeSchema,
});
export type ClickhouseColumnDefinition = z.infer<
  typeof clickhouseColumnDefinition
>;

export const clickhouseTableDefinition = z.strictObject({
  columns: z.record(clickhouseColumnDefinition),
});
export type ClickhouseTableDefinition = z.infer<
  typeof clickhouseTableDefinition
>;

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace Clickhouse {
  export function scalar(
    name: (typeof clickhouseScalarTypes)[number],
  ): ClickhouseType {
    return { type: name };
  }
  export function nullable(
    name: (typeof clickhouseScalarTypes)[number],
  ): ClickhouseType {
    return { type: "Nullable", inner: scalar(name) };
  }
  export function array(
    name: (typeof clickhouseScalarTypes)[number],
  ): ClickhouseType {
    return { type: "Array", items: scalar(name) };
  }
  export function record(value: ClickhouseType): ClickhouseType {
    return { type: "Map", key: scalar("String"), value };
  }
}
