import { z } from "zod";

// BEGIN typedefs

// These types are modified from the JSONSchema type definition in
// @json-schema-tools/meta-schema.

type $Id = string;
type $Schema = string;
type $Ref = string;
type $Comment = string;
type Title = string;
type Description = string;
type AlwaysTrue = unknown;
type ReadOnly = boolean;
type Examples = AlwaysTrue[];
type MultipleOf = number;
type Maximum = number;
type ExclusiveMaximum = number;
type Minimum = number;
type ExclusiveMinimum = number;
type NonNegativeInteger = number;
type NonNegativeIntegerDefaultZero = number;
type Pattern = string;
type SchemaArray = JSONSchema[];
/**
 *
 * @default true
 *
 */
type Items = JSONSchema | SchemaArray;
type UniqueItems = boolean;
type StringDoaGddGA = string;
/**
 *
 * @default []
 *
 */
type StringArray = StringDoaGddGA[];
/**
 *
 * @default {}
 *
 */
interface Definitions {
  [key: string]: unknown;
}
/**
 *
 * @default {}
 *
 */
interface Properties {
  [key: string]: unknown;
}
/**
 *
 * @default {}
 *
 */
interface PatternProperties {
  [key: string]: unknown;
}
export interface Dependencies {
  [key: string]: unknown;
}
type Enum = AlwaysTrue[];
type SimpleTypes =
  | "array"
  | "boolean"
  | "integer"
  | "null"
  | "number"
  | "object"
  | "string";
export type ArrayOfSimpleTypes = SimpleTypes[];
type Type = SimpleTypes | ArrayOfSimpleTypes;
type Format = string;
type ContentMediaType = string;
type ContentEncoding = string;

interface JSONSchemaObject {
  $id?: $Id;
  $schema?: $Schema;
  $ref?: $Ref;
  $comment?: $Comment;
  title?: Title;
  description?: Description;
  default?: AlwaysTrue;
  readOnly?: ReadOnly;
  examples?: Examples;
  multipleOf?: MultipleOf;
  maximum?: Maximum;
  exclusiveMaximum?: ExclusiveMaximum;
  minimum?: Minimum;
  exclusiveMinimum?: ExclusiveMinimum;
  maxLength?: NonNegativeInteger;
  minLength?: NonNegativeIntegerDefaultZero;
  pattern?: Pattern;
  additionalItems?: JSONSchema;
  items?: Items;
  maxItems?: NonNegativeInteger;
  minItems?: NonNegativeIntegerDefaultZero;
  uniqueItems?: UniqueItems;
  contains?: JSONSchema;
  maxProperties?: NonNegativeInteger;
  minProperties?: NonNegativeIntegerDefaultZero;
  required?: StringArray;
  additionalProperties?: JSONSchema;
  definitions?: Definitions;
  properties?: Properties;
  patternProperties?: PatternProperties;
  dependencies?: Dependencies;
  propertyNames?: JSONSchema;
  const?: AlwaysTrue;
  enum?: Enum;
  type?: Type;
  format?: Format;
  contentMediaType?: ContentMediaType;
  contentEncoding?: ContentEncoding;
  if?: JSONSchema;
  then?: JSONSchema;
  else?: JSONSchema;
  allOf?: SchemaArray;
  anyOf?: SchemaArray;
  oneOf?: SchemaArray;
  not?: JSONSchema;
}

/**
 *
 * Always valid if true. Never valid if false. Is constant.
 *
 */
type JSONSchemaBoolean = boolean;
/**
 *
 * @default {}
 *
 */
type JSONSchema = JSONSchemaObject | JSONSchemaBoolean;

// END typedefs

// BEGIN schema defs

// These schema definitions reflect the types defined above. We have to define
// the types separately because they are mutually recursive, so zod cannot
// automatically infer them.

const $idSchema = z.string();
const $schemaSchema = z.string();
const $refSchema = z.string();
const $commentSchema = z.string();
const titleSchema = z.string();
const descriptionSchema = z.string();
const alwaysTrueSchema = z.unknown();
const readOnlySchema = z.boolean();
const examplesSchema = z.array(alwaysTrueSchema);
const multipleOfSchema = z.number();
const maximumSchema = z.number();
const exclusiveMaximumSchema = z.number();
const minimumSchema = z.number();
const exclusiveMinimumSchema = z.number();
const nonNegativeIntegerSchema = z.number().nonnegative();
const nonNegativeIntegerDefaultZeroSchema = z.number().nonnegative().default(0);
const patternSchema = z.string();
const schemaArraySchema = z.array(z.lazy(() => jsonSchemaSchema));
const itemsSchema = z
  .union([z.lazy(() => jsonSchemaSchema), schemaArraySchema])
  .default(true);
const uniqueItemsSchema = z.boolean();
const stringDoaGddGAaSchema = z.string();
const stringArraySchema = z.array(stringDoaGddGAaSchema).default([]);
const definitionsSchema = z.record(z.unknown()).default({});
const propertiesSchema = z.record(z.unknown()).default({});
const patternPropertiesSchema = z.record(z.unknown()).default({});
const dependenciesSchema = z.record(z.unknown());
const enumSchema = z.array(alwaysTrueSchema);

const simpleTypesSchema = z.enum([
  "array",
  "boolean",
  "integer",
  "null",
  "number",
  "object",
  "string",
]);
const arrayOfSimpleTypesSchema = z.array(simpleTypesSchema);
const typeSchema = z.union([simpleTypesSchema, arrayOfSimpleTypesSchema]);
const formatSchema = z.string();
const contentMediaTypeSchema = z.string();
const contentEncodingSchema = z.string();

const jsonSchemaObjectSchema: z.ZodType<JSONSchemaObject> = z.object({
  $id: $idSchema.optional(),
  $schema: $schemaSchema.optional(),
  $ref: $refSchema.optional(),
  $comment: $commentSchema.optional(),
  title: titleSchema.optional(),
  description: descriptionSchema.optional(),
  default: alwaysTrueSchema.optional(),
  readOnly: readOnlySchema.optional(),
  examples: examplesSchema.optional(),
  multipleOf: multipleOfSchema.optional(),
  maximum: maximumSchema.optional(),
  exclusiveMaximum: exclusiveMaximumSchema.optional(),
  minimum: minimumSchema.optional(),
  exclusiveMinimum: exclusiveMinimumSchema.optional(),
  maxLength: nonNegativeIntegerSchema.optional(),
  minLength: nonNegativeIntegerDefaultZeroSchema.optional(),
  pattern: patternSchema.optional(),
  additionalItems: z.lazy(() => jsonSchemaSchema.optional()),
  items: itemsSchema.optional(),
  maxItems: nonNegativeIntegerSchema.optional(),
  minItems: nonNegativeIntegerDefaultZeroSchema.optional(),
  uniqueItems: uniqueItemsSchema.optional(),
  contains: z.lazy(() => jsonSchemaSchema.optional()),
  maxProperties: nonNegativeIntegerSchema.optional(),
  minProperties: nonNegativeIntegerDefaultZeroSchema.optional(),
  required: stringArraySchema.optional(),
  additionalProperties: z.lazy(() => jsonSchemaSchema.optional()),
  definitions: definitionsSchema.optional(),
  properties: propertiesSchema.optional(),
  patternProperties: patternPropertiesSchema.optional(),
  dependencies: dependenciesSchema.optional(),
  propertyNames: z.lazy(() => jsonSchemaSchema.optional()),
  const: alwaysTrueSchema.optional(),
  enum: enumSchema.optional(),
  type: typeSchema.optional(),
  format: formatSchema.optional(),
  contentMediaType: contentMediaTypeSchema.optional(),
  contentEncoding: contentEncodingSchema.optional(),
  if: z.lazy(() => jsonSchemaSchema.optional()),
  then: z.lazy(() => jsonSchemaSchema.optional()),
  else: z.lazy(() => jsonSchemaSchema.optional()),
  allOf: schemaArraySchema.optional(),
  anyOf: schemaArraySchema.optional(),
  oneOf: schemaArraySchema.optional(),
  not: z.lazy(() => jsonSchemaSchema.optional()),
});

const jsonSchemaSchema = z.union([jsonSchemaObjectSchema, z.boolean()]);

// END schema defs

export {
  JSONSchema,
  JSONSchemaObject,
  Items,
  SchemaArray,
  jsonSchemaSchema,
  jsonSchemaObjectSchema,
  itemsSchema,
  schemaArraySchema,
};
