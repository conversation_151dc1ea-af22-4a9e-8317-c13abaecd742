import { doubleQuote, singleQuote } from "./util";

export type Fragment = ToSQL | unknown;

export interface CompiledSQL {
  query: string;
  params: unknown[];
}

export abstract class ToSQL {
  abstract toNumericParamQuery(
    start?: number,
    pastFragmentIndices?: Map<unknown, number>,
  ): CompiledSQL;
  abstract toPlainStringQuery(): string;
}

export class PlainSnippet extends ToSQL {
  public constructor(public text: string) {
    super();
  }

  public toNumericParamQuery(): CompiledSQL {
    return { query: this.text, params: [] };
  }

  public toPlainStringQuery(): string {
    return this.text;
  }
}

export class ParameterizedSnippet extends ToSQL {
  public constructor(
    // The ith fragment is followed by the ith param.
    public fragments: Fragment[],
  ) {
    super();
  }

  public toNumericParamQuery(
    startArg?: number,
    pastFragmentIndicesArg?: Map<unknown, number>,
  ): CompiledSQL {
    const start = startArg ?? 0;
    const pastFragmentIndices =
      pastFragmentIndicesArg ?? new Map<unknown, number>();
    const params = [];
    let query = "";
    for (let i = 0; i < this.fragments.length; i++) {
      const fragment = this.fragments[i];
      const pastFragmentIndex = pastFragmentIndices.get(fragment);
      if (pastFragmentIndex !== undefined) {
        query += `$${pastFragmentIndex}`;
      } else if (fragment instanceof ToSQL) {
        const { query: subQuery, params: subParams } =
          fragment.toNumericParamQuery(
            start + params.length,
            pastFragmentIndices,
          );
        query += subQuery;
        params.push(...subParams);
      } else {
        params.push(this.fragments[i]);
        const fragmentIndex = start + params.length;
        query += `$${fragmentIndex}`;
        pastFragmentIndices.set(this.fragments[i], fragmentIndex);
      }
    }

    return { query, params };
  }

  public toPlainStringQuery(): string {
    function fragmentToPlainString(f: unknown): string {
      if (f instanceof ToSQL) {
        return f.toPlainStringQuery();
      } else if (typeof f === "string") {
        return singleQuote(f);
      } else if (typeof f === "number") {
        return f.toString();
      } else if (f === null || f === undefined) {
        return "null";
      } else if (Array.isArray(f)) {
        return "array [" + f.map(fragmentToPlainString).join(", ") + "]";
      } else {
        return singleQuote(JSON.stringify(f));
      }
    }
    return this.fragments.map(fragmentToPlainString).join("");
  }
}

export class RawSnippet extends ToSQL {
  public constructor(public query: string) {
    super();
  }

  public toNumericParamQuery(): CompiledSQL {
    return { query: this.query, params: [] };
  }

  public toPlainStringQuery(): string {
    return this.query;
  }
}

export class Ident extends ToSQL {
  public constructor(public name: string[]) {
    super();
  }

  // Can eventually ge generalized to other quoting mechanisms
  public toNumericParamQuery(): CompiledSQL {
    return { query: this.name.map(doubleQuote).join("."), params: [] };
  }

  public toPlainStringQuery(): string {
    return this.name.map(doubleQuote).join(".");
  }
}

export function ident(parts: string | string[]): ToSQL {
  return new Ident(Array.isArray(parts) ? parts : [parts]);
}

export function snippet(sqlCode: string) {
  return new PlainSnippet(sqlCode);
}

export function join(snippets: ToSQL[], separator: string | ToSQL): ToSQL {
  const sep =
    separator instanceof ToSQL ? separator : new RawSnippet(separator);
  return new ParameterizedSnippet(
    snippets.flatMap((s, i) => (i === 0 ? [s] : [sep, s])),
  );
}

export function sql(
  strings: TemplateStringsArray,
  ...paramValues: unknown[]
): ToSQL {
  const fragments: Fragment[] = [];

  for (let i = 0; i < strings.length; i++) {
    fragments.push(snippet(strings[i]));
    if (i < paramValues.length) {
      const param = paramValues[i];
      fragments.push(param);
    }
  }

  return new ParameterizedSnippet(fragments);
}
