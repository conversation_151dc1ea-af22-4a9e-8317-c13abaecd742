import {
  ArithmeticExpr,
  BoolExpr,
  BoundExpr,
  ComparisonExpr,
  Interval,
  Literal,
  TernaryExpr,
  UnaryArithmetic,
  UnaryExpr,
} from "#/binder/ast";
import { join, RawSnippet, snippet, sql, ToSQL } from "./snippet";
import { fail, PlanContext } from "./util";

function opTostring(op: BoundExpr["op"]): ToSQL {
  return new RawSnippet(
    (() => {
      switch (op) {
        case "eq":
          return "=";
        case "ne":
          return "!=";
        case "lt":
          return "<";
        case "le":
          return "<=";
        case "gt":
          return ">";
        case "ge":
          return ">=";
        case "like":
          return "LIKE";
        case "ilike":
          return "ILIKE";
        case "is":
          return "IS";
        case "and":
          return "AND";
        case "or":
          return "OR";
        case "add":
          return "+";
        case "sub":
          return "-";
        case "mul":
          return "*";
        case "div":
          return "/";
        case "mod":
          return "%";
        case "neg":
          return "-";
        case "not":
          return "NOT";
        case "in":
          return "IN";

        case "field":
        case "function":
        case "literal":
        case "interval":
        case "cast":
        case "isnull":
        case "isnotnull":
        case "if":
        case "includes":
        case "match":
          throw new Error(`Unexpected op: ${op}`);
      }
    })(),
  );
}

export type StandardSQLExpr =
  | Literal
  | Interval
  | ComparisonExpr
  | BoolExpr
  | ArithmeticExpr
  | UnaryArithmetic
  | UnaryExpr
  | TernaryExpr;

export function planSQLExpr<Ctx extends PlanContext>(
  ctx: Ctx,
  expr: StandardSQLExpr,
  planExpr: (ctx: Ctx, expr: BoundExpr) => ToSQL,
): ToSQL {
  switch (expr.op) {
    case "literal":
      return sql`${expr.value}`;
    case "interval":
      // SQL databases do not like this to be a parameter
      return sql`INTERVAL '${snippet(`${expr.value}`)} ${snippet(
        expr.unit.toLocaleUpperCase(),
      )}'`;
    case "eq":
    case "is":
    case "ne":
    case "lt":
    case "le":
    case "gt":
    case "ge":
    case "add":
    case "sub":
    case "mul":
    case "div":
    case "mod":
    case "like":
    case "ilike":
      return sql`(${planExpr(ctx, expr.left)} ${opTostring(expr.op)} ${planExpr(
        ctx,
        expr.right,
      )})`;
    case "and":
    case "or":
      return sql`(${join(
        expr.children?.map((child) => planExpr(ctx, child)) ?? [],
        sql` ${opTostring(expr.op)} `,
      )})`;
    case "match":
      if (expr.right.op !== "literal" || expr.right.type !== "string") {
        fail(ctx, "Match op RHS must be a string literal", expr);
      }
      const right = {
        ...expr.right,
        value: `%${expr.right.value}%`,
      };
      return sql`(${planExpr(ctx, expr.left)} ILIKE ${planExpr(ctx, right)})`;
    case "neg":
      return sql`(-(${planExpr(ctx, expr.expr)}))`;

    case "not":
      return sql`(NOT (${planExpr(ctx, expr.expr)}))`;

    case "isnull":
      return sql`(${planExpr(ctx, expr.expr)} IS NULL)`;
    case "isnotnull":
      return sql`(${planExpr(ctx, expr.expr)} IS NOT NULL)`;

    case "if":
      const whens = expr.conds.map(
        (c) => sql`WHEN ${planExpr(ctx, c.cond)} THEN ${planExpr(ctx, c.then)}`,
      );
      return sql`CASE ${join(whens, " ")} ELSE ${planExpr(ctx, expr.else)} END`;

    case "in":
      // new operators in BTQL shouldn't support SQL BTQL as it is deprecated.
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return fail(ctx, `Unexpected op: ${(expr as any).op}`, expr);

    default:
      const _x: never = expr;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return fail(ctx, `Unexpected op: ${(expr as any).op}`, expr);
  }
}
