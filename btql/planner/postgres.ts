import { BoundExpr, Field, Function, Literal } from "#/binder";
import { stripCast } from "#/binder/bind";
import {
  coerceTypes,
  getExprScalarType,
  isObjectLike,
  literalValueToScalarType,
  weakestScalarType,
} from "#/binder/types";
import {
  isScalar,
  LogicalSchema,
  PostgresTableDefinition,
  PostgresType,
  ScalarType,
} from "#/schema";
import { ident, join, snippet, sql, ToSQL } from "./snippet";
import { planSQLExpr } from "./sql";
import { unpivotTableAlias } from "./unpivot";
import { fail, makeObjectFromPath, PlanContext } from "./util";

export type PostgresPlanCtx = PlanContext & {
  table: {
    alias: string | null;
    schema: PostgresTableDefinition;
  } | null;
};

export function planPostgresExpr(ctx: PostgresPlanCtx, expr: BoundExpr): ToSQL {
  switch (expr.op) {
    case "literal":
      const literalType = literalValueToScalarType(expr.value);
      if (literalType === null) {
        fail(ctx, `Unknown literal type: ${expr.value}`, expr);
      }
      // Array literals are cast to jsonb, so we stringify the array rather than
      // passing it in as an actual javascript array param.
      const exprSql =
        literalType === "array"
          ? sql`${JSON.stringify(expr.value)}`
          : sql`${expr.value}`;
      const ret = applyCast(exprSql, literalType, getExprScalarType(expr));
      return ret;
    case "field":
      if (!ctx.table) {
        fail(ctx, "Field reference outside of table", expr);
      }

      if (expr.name.length == 0) {
        fail(ctx, `Table reference`, expr);
      }

      const first = expr.name[0];

      const pieces: (string | number)[] = [];
      let colType: PostgresType;
      if (expr.source && "unpivot" in expr.source) {
        pieces.push(unpivotTableAlias(expr.source.unpivot));
        pieces.push(expr.source.type === "key" ? "key" : "value");
        colType =
          expr.source.type === "key" ? { type: "text" } : { type: "jsonb" };
      } else if (ctx.table.schema.columns[first]) {
        const rawExpr = ctx.table.schema.columns[first].expr;
        if (rawExpr) {
          return sql`${snippet(rawExpr)}`;
        }

        if (ctx.table.alias !== null) {
          pieces.push(ctx.table.alias);
        }
        pieces.push(...ctx.table.schema.columns[first].path);
        pieces.push(...expr.name.slice(1));
        colType = ctx.table.schema.columns[first].type;
      } else {
        fail(ctx, `Unknown field: ${first}`, expr);
      }

      const rootPieces = pieces.slice(0, 2);
      if (!rootPieces.every((p): p is string => typeof p === "string")) {
        fail(ctx, "Numeric root pieces not allowed", expr);
      }
      if (pieces.length <= 2) {
        let ret: ToSQL = ident(rootPieces);
        const [_, fieldCast] = coerceTypes(
          expr.type,
          postgresTypeToLogicalSchema(colType),
          true,
        );

        if (fieldCast !== null && fieldCast !== "string") {
          ret = sql`${ret}::${scalarTypeToPGTypeSQL(fieldCast)}`;
        } else if (
          !isScalar(expr.type) &&
          !(colType.type === "jsonb" || colType.type === "json")
        ) {
          ret = sql`to_jsonb(${ret})`;
        }
        return ret;
      }

      // We assume that if it's a path, the parent column is always JSONB
      const extractFunction = snippet(
        `jsonb_extract_path${isScalar(expr.type) ? "_text" : ""}`,
      );
      const rootIdent = ident(rootPieces);
      const jsonParts = pieces.slice(2).map((e) => sql`${e.toString()}`);

      let fieldAccess = sql`NULLIF(${extractFunction}(${rootIdent}, ${join(
        jsonParts,
        ", ",
      )}), 'null')`;

      const fieldType = weakestScalarType(expr.type);
      if (fieldType !== "string") {
        fieldAccess = sql`${fieldAccess}::${scalarTypeToPGTypeSQL(fieldType)}`;
      }
      return fieldAccess;
    case "function":
      return planPostgresFunction(ctx, expr);
    case "cast":
      const inner = planPostgresExpr(ctx, expr.expr);
      const exprType = getExprScalarType(expr.expr);
      const castType = expr.type;

      if (exprType !== castType) {
        return applyCast(inner, exprType, castType);
      } else {
        return inner;
      }
    case "includes":
      const haystackNoCast = stripCast(expr.haystack);
      const needleNoCast = stripCast(expr.needle);
      if (
        haystackNoCast.op === "field" &&
        needleNoCast.op === "literal" &&
        haystackNoCast.name.every((p) => typeof p === "string")
      ) {
        return planGinIndexLookup(ctx, haystackNoCast, needleNoCast);
      } else {
        return sql`${planPostgresExpr(ctx, expr.haystack)} @> ${applyCast(
          planPostgresExpr(ctx, expr.needle),
          getExprScalarType(expr.needle),
          "object",
        )}`;
      }
    case "eq":
      // Optimize gin index lookups
      let field: Field;
      let literal: Literal;
      if (
        expr.left.op === "field" &&
        expr.left.name.length > 1 &&
        expr.left.name.every((p) => typeof p === "string") &&
        expr.right.op === "literal" &&
        !isObjectLike(expr.right.type)
      ) {
        field = expr.left;
        literal = expr.right;
      } else if (
        expr.right.op === "field" &&
        expr.right.name.length > 1 &&
        expr.right.name.every((p) => typeof p === "string") &&
        expr.left.op === "literal" &&
        !isObjectLike(expr.left.type)
      ) {
        field = expr.right;
        literal = expr.left;
      } else {
        return planSQLExpr(ctx, expr, planPostgresExpr);
      }

      return planGinIndexLookup(ctx, field, literal);

    default:
      return planSQLExpr(ctx, expr, planPostgresExpr);
  }
}

function planGinIndexLookup(
  ctx: PostgresPlanCtx,
  field: Field,
  literal: Literal,
) {
  if (!ctx.table) {
    fail(ctx, "Field reference outside of table", field);
  }
  if (!field.name.every((p): p is string => typeof p === "string")) {
    fail(
      ctx,
      "Numeric field path pieces not allowed for GIN index lookup",
      field,
    );
  }
  const pieces = ctx.table.alias !== null ? [ctx.table.alias] : [];
  const first = field.name[0];
  if (!ctx.table?.schema.columns[first]) {
    fail(ctx, `Unknown field: ${first}`, field);
  }
  pieces.push(...ctx.table.schema.columns[first].path);
  pieces.push(...field.name.slice(1));

  if (pieces.length <= 2) {
    return sql`${ident(pieces)} = ${literal.value}`;
  }

  // If the LHS is an array, and the RHS literal is not, then we promote it
  // to an array
  if (getExprScalarType(field) === "array" && !Array.isArray(literal.value)) {
    literal = {
      op: "literal",
      value: [literal.value],
      type: "array",
    };
  }

  const fieldPath = pieces.slice(0, 2);
  const matchObject: Literal = {
    op: "literal",
    value: makeObjectFromPath(pieces.slice(2), literal.value),
    type: "object",
  };
  return sql`${ident(fieldPath)} @> ${planPostgresExpr(ctx, matchObject)}`;
}

function planPostgresFunction(ctx: PostgresPlanCtx, func: Function): ToSQL {
  const args = func.args.map((a) => planPostgresExpr(ctx, a));
  switch (func.name) {
    case "count":
    case "sum":
    case "avg":
    case "min":
    case "max":
      return sql`${ident(func.name)}(${join(args, ", ")})`;
    case "percentile":
      return sql`percentile_cont(${args[1]}) WITHIN GROUP (ORDER BY ${args[0]})`;
    case "second":
    case "minute":
    case "hour":
    case "day":
    case "week":
    case "month":
    case "year":
      if (ctx.tzOffset) {
        const tsExpr = sql`(${args[0]}::timestamptz - ${`${ctx.tzOffset} minutes`}::interval)`;
        return sql`date_trunc(${func.name}, ${tsExpr})::timestamptz + ${`${ctx.tzOffset} minutes`}::interval`;
      }
      return sql`date_trunc(${func.name}, ${args[0]}::timestamptz)`;
    case "current_date":
      return sql`current_date`;
    case "current_timestamp":
      return sql`current_timestamp`;
    case "sha256":
      return sql`sha256(${args[0]}::bytea)`;
    case "insert":
      const mainExpr = args[0];
      const buildArgs = args
        .slice(1)
        .map((a, i) =>
          i % 2 === 0
            ? a
            : applyCast(a, getExprScalarType(func.args[i + 1]), "object"),
        );
      return sql`${mainExpr} || jsonb_build_object(${join(buildArgs, ", ")})`;
    case "coalesce":
    case "greatest":
    case "least":
    case "concat":
      return sql`${snippet(func.name)}(${join(args, ", ")})`;
    case "nullif":
      return sql`nullif(${args[0]}, ${args[1]})`;
    case "len":
      return sql`jsonb_array_length(${args[0]})`;
    case "lower":
      return sql`lower(${args[0]})`;
    case "upper":
      return sql`upper(${args[0]})`;
    case "json_extract":
      // TODO: Turns out we need try...catch semantics to make this work, which we cannot do in postgres
      // without a UDF...

      // const hasCodeBlock = snippet("'[\\s\\n]*```.*```[\\s\\n]*'");
      // const replaceString = snippet(
      //   "'.*?```(?:json)?[\\s\\n]*([{[].*?[}\\]][\\s\\n]*?)```.*'",
      // );
      // return sql`CASE
      //   WHEN ${args[0]} ~ ${hasCodeBlock}
      //   THEN (
      //     SELECT jsonb_extract_path(
      //       NULLIF(regexp_replace(${args[0]}, ${replaceString}, '\\1', 'g'), '')::jsonb,
      //       ${join(args.slice(1), ", ")}
      //     )
      //     WHERE NULLIF(regexp_replace(${args[0]}, ${replaceString}, '\\1', 'g'), '') IS NOT NULL
      //       AND NULLIF(regexp_replace(${args[0]}, ${replaceString}, '\\1', 'g'), '') IS VALID JSON
      //   )
      //   ELSE (
      //     SELECT jsonb_extract_path(
      //       NULLIF(trim(${args[0]}), '')::jsonb,
      //       ${join(args.slice(1), ", ")}
      //     )
      //     WHERE NULLIF(trim(${args[0]}), '') IS NOT NULL
      //       AND NULLIF(trim(${args[0]}), '') IS VALID JSON
      //   )
      // END`;
      return sql`null`;
    case "to_string":
      return sql`CAST(${args[0]} AS TEXT)`;
    case "to_boolean":
      return sql`CAST(${args[0]} AS BOOLEAN)`;
    case "to_integer":
      return sql`CAST(${args[0]} AS INTEGER)`;
    case "to_number":
      return sql`CAST(${args[0]} AS FLOAT)`;
    case "to_date":
      return sql`CAST(${args[0]} AS DATE)`;
    case "to_datetime":
      return sql`CAST(${args[0]} AS TIMESTAMP)`;
    case "to_interval":
      return sql`CAST(${args[0]} AS INTERVAL)`;
    default:
      const _: never = func.name;
      throw new Error(`Unsupported function: ${_}`);
  }
}

function scalarTypeToPGType(type: ScalarType): PostgresType["type"] {
  switch (type) {
    case "string":
      return `text`;
    case "datetime":
      return `timestamptz`;
    case "date":
      return `date`;
    case "number":
      return `numeric`;
    case "integer":
      return `bigint`;
    case "boolean":
      return `boolean`;
    case "object":
    case "array":
    case "unknown":
      return `jsonb`;
    case "null":
      return `text`;
    case "interval":
      return `interval`;
  }
}

function scalarTypeToPGTypeSQL(type: ScalarType): ToSQL {
  return snippet(scalarTypeToPGType(type));
}

function postgresTypeToLogicalSchema(type: PostgresType): LogicalSchema {
  switch (type.type) {
    case "json":
    case "jsonb":
      return { type: "object" };
    case "text":
      return { type: "string" };
    case "integer":
    case "bigint":
    case "bigserial":
      return { type: "integer" };
    case "numeric":
      return { type: "number" };
    case "boolean":
      return { type: "boolean" };
    case "timestamp":
    case "timestamp with time zone":
      return { type: "string", format: "date-time" };
    case "date":
      return { type: "string", format: "date" };
    default:
      throw new Error(`Unsupported Postgres type: ${type.type}`);
  }
}

function applyCast(expr: ToSQL, exprType: ScalarType, targetType: ScalarType) {
  if (exprType === "boolean" && ["number", "integer"].includes(targetType)) {
    return sql`(CASE WHEN ${expr} THEN 1 ELSE 0 END)`;
  } else if (isObjectLike(exprType) && !isObjectLike(targetType)) {
    return sql`(${expr} #>> '{}')::${scalarTypeToPGTypeSQL(targetType)}`;
  } else if (isObjectLike(targetType) && !isObjectLike(exprType)) {
    return sql`to_jsonb((${expr})::${scalarTypeToPGTypeSQL(exprType)})`;
  } else {
    return sql`${expr}::${scalarTypeToPGTypeSQL(targetType)}`;
  }
}
