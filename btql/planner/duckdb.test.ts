import { test, expect } from "vitest";
import { DuckDBPlanCtx, planDuckDBExpr } from "./duckdb";
import { BoundExpr } from "#/binder/ast";
import { DuckDBTableDefinition } from "#/schema";

const DEFAULT_TABLE_DEFINITION: DuckDBTableDefinition = {
  columns: {
    metrics: {
      path: ["metrics"],
      type: {
        type: "struct_map",
        value: {
          type: "double",
        },
      },
    },
  },
};

const DEFAULT_CTX: DuckDBPlanCtx = {
  schema: {
    type: "duckdb",
    tables: {
      logs: DEFAULT_TABLE_DEFINITION,
    },
  },
  table: {
    alias: "logs2",
    schema: DEFAULT_TABLE_DEFINITION,
  },
};

test("plan struct_map field", () => {
  const cases: [BoundExpr, string][] = [
    [
      {
        op: "field",
        name: ["metrics", "foo"],
        type: { type: "number" },
      },
      `"logs2"."metrics"."foo"::numeric`,
    ],
  ];

  for (const [expr, expected] of cases) {
    expect(planDuckDBExpr(DEFAULT_CTX, expr).toPlainStringQuery()).toEqual(
      expected,
    );
  }
});
