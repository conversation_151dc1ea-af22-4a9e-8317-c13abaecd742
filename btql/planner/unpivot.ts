import {
  JSONSchemaObject,
  jsonSchemaObjectSchema,
  validateSchema,
} from "#/schema";
import { Row } from "./ast";
import {
  BoundAlias,
  isObjectLike,
  ResponseSchema,
  weakestScalarType,
} from "#/binder";
import { getExprScalarType } from "#/binder/types";
import { parseJSONFieldsMut } from ".";
import { PhysicalSchema } from "#/schema";
import { inferSchema } from "#/schema/inference";

export function unpivotTableAlias(unpivot: number): string {
  return `__unpivot_${unpivot}`;
}

function valueToKey(value: unknown): string {
  if (typeof value === "string") {
    return value;
  } else {
    return JSON.stringify(value);
  }
}

export async function* unpivotRows({
  rows,
  schema,
  pivotFields,
  measureFields,
  dialect,
  parseJSONFields,
  strictValidate,
  limit,
}: {
  rows: AsyncIterable<Row>;
  schema: ResponseSchema;
  pivotFields: BoundAlias[];
  measureFields: string[];
  dialect: PhysicalSchema["type"];
  parseJSONFields: boolean;
  strictValidate?: boolean;
  limit?: number;
}): AsyncIterable<Row> {
  const pivotNames = pivotFields.map((f) => f.alias);
  const rowSchema = schema.items;

  const jsonFields = new Set(
    Object.entries(rowSchema.properties)
      .filter(
        ([k, v]) =>
          isObjectLike(weakestScalarType(v)) && !pivotNames.includes(k),
      )
      .map(([k, _]) => k),
  );

  if (dialect === "duckdb") {
    // Postgres actually json deserializes jsonb values in the driver
    // Clickhouse only supports array joins on non-JSON types
    // DuckDB keeps them as JSON, and we can't distinguish them as strings in the driver,
    //  so we need to parse them here
    for (const field of pivotFields) {
      if (isObjectLike(getExprScalarType(field.expr))) {
        jsonFields.add(field.alias);
      }
    }
  }

  const intermediateRows: Record<string, unknown>[] = pivotNames.map(
    () => ({}),
  );
  const distinctValues = pivotNames.map(() => new Set<string>());
  let returned = 0;
  for await (const row of rows) {
    if (parseJSONFields) {
      parseJSONFieldsMut({ row, jsonFields });
    }
    if (limit !== undefined && returned >= limit) {
      break;
    }
    for (let i = pivotNames.length - 1; i >= 0; i--) {
      const field = pivotNames[i];
      const value = row[field];
      const valueKey = valueToKey(value);
      if (value !== undefined && value !== null) {
        distinctValues[i].add(valueKey);
        if (i === pivotNames.length - 1) {
          intermediateRows[i][`${valueKey}`] = Object.fromEntries(
            Object.entries(row).filter(([key, _]) =>
              measureFields.includes(key),
            ),
          );
        } else if (Object.keys(intermediateRows[i + 1]).length > 0) {
          intermediateRows[i][`${valueKey}`] = {
            [pivotNames[i + 1]]: {
              ...intermediateRows[i + 1],
            },
            ...Object.fromEntries(
              Object.entries(row).filter(([key, _]) =>
                measureFields.includes(key),
              ),
            ),
          };
          intermediateRows[i + 1] = {};
        }
        break;
      } else if (i === 0) {
        const finalRow = {
          ...Object.fromEntries(
            Object.entries(row).filter(([key, _]) => !pivotNames.includes(key)),
          ),
          [pivotNames[0]]: intermediateRows[0],
        };
        intermediateRows[0] = {};
        validateSchema({
          data: finalRow,
          schema: rowSchema,
          strict: strictValidate,
        });
        yield finalRow;
        returned += 1;
      }
    }
  }

  let curr: JSONSchemaObject = rowSchema;
  for (let i = 0; i < pivotNames.length; i++) {
    if (!curr.properties || !curr.properties[pivotNames[i]]) {
      throw new Error(
        `Invalid unpivot schema (expect "${pivotNames[i]}" property)`,
      );
    }
    const currSchema = jsonSchemaObjectSchema.parse(
      curr.properties[pivotNames[i]],
    );
    // We need to re-assign the updated currSchema to the original schema object
    // because this function is mutating the `rowSchema`.
    curr.properties[pivotNames[i]] = currSchema;
    currSchema.propertyNames = {
      enum: Array.from(distinctValues[i]),
    };
    if (!currSchema.additionalProperties) {
      throw new Error("Invalid unpivot schema (expect additionalProperties)");
    }
    if (typeof currSchema.additionalProperties === "boolean") {
      throw new Error(
        "Invalid unpivot schema (expect additionalProperties to be an object-type schema)",
      );
    }
    curr = currSchema.additionalProperties;
  }
}

export function makeUnpivotPostprocessor({
  schema,
  pivotFields,
  measureFields,
  dialect,
  parseJSONFields,
  strictValidate,
  limit,
  inferenceDepth,
}: {
  schema: ResponseSchema;
  pivotFields: BoundAlias[];
  measureFields: string[];
  dialect: PhysicalSchema["type"];
  parseJSONFields: boolean;
  strictValidate?: boolean;
  limit?: number;
  inferenceDepth?: number;
}) {
  return async function* (rows: AsyncIterable<Row>): AsyncIterable<Row> {
    for await (const row of unpivotRows({
      rows,
      schema,
      pivotFields,
      measureFields,
      dialect,
      parseJSONFields,
      strictValidate,
      limit,
    })) {
      if (inferenceDepth !== undefined) {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        schema.items = inferSchema({
          schema: schema.items,
          value: row,
          depth: inferenceDepth,
        }) as typeof schema.items;
      }
      yield row;
    }
  };
}
