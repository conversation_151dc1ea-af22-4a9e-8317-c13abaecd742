import { describe, expect, test } from "vitest";
import { doubleQuote, doubleQuote<PERSON>lackslash, jsonPath } from "./util";

describe("string escaping", () => {
  describe("doubleQuote", () => {
    for (const [input, expected] of [
      [``, `""`],
      [`""`, `""""""`],
      [`''`, `"''"`],
    ]) {
      test(input, () => {
        expect(doubleQuote(input)).toBe(expected);
      });
    }
  });
  describe("doubleQuoteBackslash", () => {
    for (const [input, expected] of [
      [``, `""`],
      [`""`, `"\\"\\""`],
      [`''`, `"''"`],
    ]) {
      test(input, () => {
        expect(doubleQuoteBlackslash(input)).toBe(expected);
      });
    }
  });
});

describe("jsonPath", () => {
  const testCases: [string[], string][] = [
    [[], `'$'`],
    [[``], `'$.""'`],
    [[`""`], `'$."\\"\\""'`],
    [[`''`], `'$."''''"'`],
    [[`""`, `2`, `\\"\\"`], `'$."\\"\\""."2"."\\\\"\\\\""'`],
  ];
  for (const [input, expected] of testCases) {
    test(JSON.stringify(input), () => {
      expect(jsonPath(input)).toBe(expected);
    });
  }
});
