import { test, expect } from "vitest";
import { snippet, ident, join, sql } from "./snippet";

test("json snippet", () => {
  const cases = [
    {
      root: ["project", "scores"],
      path: ["a"],
      expected: {
        query: `"project"."scores"->>$1`,
        params: ["a"],
      },
    },
    {
      root: ["project", "input"],
      path: ["a", "b"],
      expected: {
        query: `"project"."input"->$1->>$2`,
        params: ["a", "b"],
      },
    },
    {
      root: ["project", "input"],
      path: ["a", "b", "a"],
      expected: {
        query: `"project"."input"->$1->$2->>$1`,
        params: ["a", "b"],
      },
    },
  ];

  for (const { root, path, expected } of cases) {
    const rootIdent = ident(root);
    const joiner = "->>";
    const foo = sql`${join(
      [rootIdent, ...path.slice(0, -1).map((s) => sql`${s}`)],
      "->",
    )}${snippet(joiner)}${path[path.length - 1]}`;
    const { query, params } = foo.toNumericParamQuery();
    expect(query).toBe(expected.query);
    expect(params).toEqual(expected.params);
  }
});

test("array snippet", () => {
  // For some reason prettier modifies this array if it's inlined in the string.
  // Filed https://github.com/prettier/prettier/issues/16493.
  const case2Array = [
    [1, 2],
    [3, 4],
    [5, 6],
  ];
  const cases = [
    {
      queryObj: sql`select * from unnest(${[1, 2, 3]}::int[])`,
      expectedNumericParam: {
        query: `select * from unnest($1::int[])`,
        params: [[1, 2, 3]],
      },
      expectedPlainString: `select * from unnest(array [1, 2, 3]::int[])`,
    },
    {
      queryObj: sql`select ${["hello", "goodbye"]}::text[]`,
      expectedNumericParam: {
        query: `select $1::text[]`,
        params: [["hello", "goodbye"]],
      },
      expectedPlainString: `select array ['hello', 'goodbye']::text[]`,
    },
    {
      queryObj: sql`select ${case2Array}::int[][]`,
      expectedNumericParam: {
        query: `select $1::int[][]`,
        params: [
          [
            [1, 2],
            [3, 4],
            [5, 6],
          ],
        ],
      },
      expectedPlainString: `select array [array [1, 2], array [3, 4], array [5, 6]]::int[][]`,
    },
    {
      queryObj: sql`select ${[["hello"], ["goodbye"]]}::text[][]`,
      expectedNumericParam: {
        query: `select $1::text[][]`,
        params: [[["hello"], ["goodbye"]]],
      },
      expectedPlainString: `select array [array ['hello'], array ['goodbye']]::text[][]`,
    },
  ];

  for (const { queryObj, expectedNumericParam, expectedPlainString } of cases) {
    const { query, params } = queryObj.toNumericParamQuery();
    expect(query).toBe(expectedNumericParam.query);
    expect(params).toEqual(expectedNumericParam.params);
    const queryStr = queryObj.toPlainStringQuery();
    expect(queryStr).toEqual(expectedPlainString);
  }
});

test("undefined snippet", () => {
  const cases = [
    {
      queryObj: sql`select ${undefined}`,
      expectedNumericParam: {
        query: `select $1`,
        params: [undefined],
      },
      expectedPlainString: `select null`,
    },
  ];

  for (const { queryObj, expectedNumericParam, expectedPlainString } of cases) {
    const { query, params } = queryObj.toNumericParamQuery();
    expect(query).toBe(expectedNumericParam.query);
    expect(params).toEqual(expectedNumericParam.params);
    const queryStr = queryObj.toPlainStringQuery();
    expect(queryStr).toEqual(expectedPlainString);
  }
});
