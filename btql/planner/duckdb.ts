import { BoundExpr, Function } from "#/binder/ast";
import {
  coerceTypes,
  getExprScalarType,
  isObjectLike,
  weakestScalarType,
} from "#/binder/types";
import {
  DuckDBTableDefinition,
  DuckDBType,
  isScalar,
  LogicalSchema,
  ScalarType,
} from "#/schema";
import { ident, join, snippet, sql, ToSQL } from "./snippet";
import { planSQLExpr } from "./sql";
import { unpivotTableAlias } from "./unpivot";
import { doubleQuote, fail, PlanContext } from "./util";

export type DuckDBPlanCtx = PlanContext & {
  table: {
    alias: string | null;
    schema: DuckDBTableDefinition;
  } | null;
};

export function planDuckDBExpr(ctx: DuckDBPlanCtx, expr: BoundExpr): ToSQL {
  switch (expr.op) {
    case "literal":
      return sql`${expr.value}`;
    case "field":
      if (!ctx.table) {
        fail(ctx, "Field reference outside of table", expr);
      }

      if (expr.name.length == 0) {
        fail(ctx, `Table reference`, expr);
      }

      const first = expr.name[0];

      const pieces: (string | number)[] = [];
      let colType: DuckDBType;
      if (expr.source && "unpivot" in expr.source) {
        pieces.push(unpivotTableAlias(expr.source.unpivot));

        // In DuckDB, array unnest just uses the root alias
        if (expr.source.type === "key") {
          pieces.push("key");
        } else if (expr.source.type === "value") {
          pieces.push("value");
        }
        colType =
          expr.source.type === "key" ? { type: "varchar" } : { type: "json" };
      } else if (ctx.table.schema.columns[first]) {
        if (ctx.table.alias !== null) {
          pieces.push(ctx.table.alias);
        }
        pieces.push(...ctx.table.schema.columns[first].path);
        pieces.push(...expr.name.slice(1));
        colType = ctx.table.schema.columns[first].type;
      } else {
        fail(ctx, `Unknown field: ${first}`, expr);
      }

      const rootPieces = pieces.slice(0, 2);
      if (!rootPieces.every((p): p is string => typeof p === "string")) {
        fail(ctx, "Numeric root pieces not allowed", expr);
      }
      if (
        pieces.length <= 2 ||
        colType.type === "struct" ||
        colType.type === "struct_map" ||
        colType.type === "map"
      ) {
        let ret: ToSQL =
          colType.type === "map" && pieces.length === 3
            ? // Sadly, this case is not currently covered by unit tests. The best way to test this
              // logic is to filter scores in the Braintrust UI.
              sql`${ident(rootPieces)}[${pieces[2]}]`
            : ident(
                pieces.map((p) =>
                  typeof p === "string" ? p : wrapArrayIndex(p),
                ),
              );
        const [_, fieldCast] = coerceTypes(
          expr.type,
          duckdbTypeToLogicalSchema(colType),
          true,
        );

        if (fieldCast !== null && fieldCast !== "string") {
          ret = sql`${ret}::${jsonTypeToDuckDBType(fieldCast)}`;
        } else if (!isScalar(expr.type) && colType.type !== "json") {
          ret = sql`to_json(${ret})`;
        } else if (
          expr.name.length === 1 &&
          colType.type === "json" &&
          fieldCast !== null
        ) {
          ret = sql`json_extract_string(${ret}, '$')::${jsonTypeToDuckDBType(
            fieldCast,
          )}`;
        }
        return ret;
      }

      if (colType.type !== "json") {
        fail(
          ctx,
          `Unsupported field reference "${expr.name.join(".")}" on type ${
            colType.type
          }`,
          expr,
        );
      }

      const extractFunction = snippet(
        `json_extract${isScalar(expr.type) ? "_string" : ""}`,
      );
      const rootIdent = ident(rootPieces);
      const path =
        "$" +
        pieces
          .slice(2)
          .map((p) =>
            typeof p === "string" ? `.${doubleQuote(p)}` : wrapArrayIndex(p),
          )
          .join("");

      let fieldAccess = sql`${extractFunction}(${rootIdent}, ${path})`;

      const fieldType = weakestScalarType(expr.type);
      if (fieldType !== "string") {
        fieldAccess = sql`${fieldAccess}::${jsonTypeToDuckDBType(fieldType)}`;
      }
      return fieldAccess;
    case "function":
      return planDuckDBFunction(ctx, expr);
    case "cast":
      const inner = planDuckDBExpr(ctx, expr.expr);
      return applyCast(inner, getExprScalarType(expr.expr), expr.type);
    case "includes":
      return sql`json_contains(${planDuckDBExpr(
        ctx,
        expr.haystack,
      )}, ${planDuckDBExpr(ctx, expr.needle)})`;
    case "isnull": {
      const nullCheck = sql`${planDuckDBExpr(ctx, expr.expr)} IS NULL`;
      const nullStringCheck = sql`TRY_CAST(${planDuckDBExpr(ctx, expr.expr)} AS JSON) IS NOT DISTINCT FROM 'null'::JSON`;
      return sql`((${nullCheck}) OR (${nullStringCheck}))`;
    }
    case "isnotnull": {
      const nullCheck = sql`${planDuckDBExpr(ctx, expr.expr)} IS NOT NULL`;
      const nullStringCheck = sql`TRY_CAST(${planDuckDBExpr(ctx, expr.expr)} AS JSON) IS DISTINCT FROM 'null'::JSON`;
      return sql`((${nullCheck}) AND (${nullStringCheck}))`;
    }
    default:
      return planSQLExpr(ctx, expr, planDuckDBExpr);
  }
}

function planDuckDBFunction(ctx: DuckDBPlanCtx, func: Function): ToSQL {
  const args = func.args.map((a) => planDuckDBExpr(ctx, a));
  switch (func.name) {
    case "count":
    case "sum":
    case "avg":
    case "min":
    case "max":
      return sql`${ident(func.name)}(${join(args, ", ")})`;
    case "percentile":
      return sql`percentile_cont(${args[1]}) WITHIN GROUP (ORDER BY ${args[0]})`;
    case "second":
    case "minute":
    case "hour":
    case "day":
    case "week":
    case "month":
    case "year":
      let tsExpr = sql`${args[0]}`;
      if (ctx.tzOffset) {
        // Adjust the input timestamp by subtracting the offset
        tsExpr = sql`((${tsExpr}) - INTERVAL (${ctx.tzOffset}) MINUTE)`;

        const truncatedExpr = sql`date_trunc(${func.name}, ${tsExpr})`;

        // Adjust the result back to UTC by adding the offset
        tsExpr = sql`((${truncatedExpr}) + INTERVAL (${ctx.tzOffset}) MINUTE)`;
      } else {
        tsExpr = sql`date_trunc(${func.name}, ${tsExpr})`;
      }
      return sql`${tsExpr}::TIMESTAMP`;
    case "current_timestamp":
      return sql`get_current_timestamp()`;
    case "current_date":
      return sql`current_date()`;
    case "sha256":
      return sql`sha256(${args[0]})`;
    case "insert":
      return sql`json_merge_patch(${args[0]}, json_object(${join(
        args.slice(1),
        ", ",
      )}))`;
    case "concat":
    case "coalesce":
    case "greatest":
    case "least":
      return sql`${snippet(func.name)}(${join(args, ", ")})`;
    case "nullif":
      return sql`nullif(${args[0]}, ${args[1]})`;
    case "len":
      return sql`IF(${args[0]} = 'null'::json, NULL, json_array_length(${args[0]}))`;
    case "lower":
      return sql`lower(${args[0]})`;
    case "upper":
      return sql`upper(${args[0]})`;
    case "json_extract":
      const hasCodeBlock = snippet("'(?s).*```.*```.*'");
      const replaceString = snippet(
        "'(?s).*?```(?:json)?[\\s\\n]+([{[].*?[}\\]][\\s\\n]*?)```.*'",
      );
      return sql`CASE
        WHEN trim(${args[0]}) = ''
        THEN NULL
        WHEN regexp_matches(${args[0]}, ${hasCodeBlock})
        THEN json_extract(TRY_CAST(trim(regexp_replace(${args[0]}, ${replaceString}, '\\1', 'g')) AS JSON), ${join(args.slice(1), ", ")})
        ELSE json_extract(TRY_CAST(${args[0]} AS JSON), ${join(args.slice(1), ", ")})
      END`;
    case "to_string":
      return sql`CAST(${args[0]} AS VARCHAR)`;
    case "to_boolean":
      return sql`CAST(${args[0]} AS BOOLEAN)`;
    case "to_integer":
      return sql`CAST(${args[0]} AS INTEGER)`;
    case "to_number":
      return sql`CAST(${args[0]} AS FLOAT)`;
    case "to_date":
      return sql`CAST(${args[0]} AS DATE)`;
    case "to_datetime":
      return sql`CAST(${args[0]} AS TIMESTAMP)`;
    case "to_interval":
      return sql`CAST(${args[0]} AS INTERVAL)`;
    default:
      const _: never = func.name;
      throw new Error(`Unsupported function: ${_}`);
  }
}

function jsonTypeToDuckDBType(type: ScalarType): ToSQL {
  switch (type) {
    case "string":
      return sql`varchar`;
    case "datetime":
      return sql`timestamp`;
    case "date":
      return sql`date`;
    case "number":
      return sql`numeric`;
    case "integer":
      return sql`bigint`;
    case "boolean":
      return sql`boolean`;
    case "object":
    case "array":
    case "unknown":
      return sql`json`;
    case "null":
      return sql`varchar`;
    case "interval":
      return sql`interval`;
  }
}

function duckdbTypeToLogicalSchema(type: DuckDBType): LogicalSchema {
  switch (type.type) {
    case "varchar":
      return { type: "string" };
    case "json":
      return { type: "object" };
    case "double":
      return { type: "number" };
    case "integer":
    case "bigint":
    case "hugeint":
      return { type: "integer" };
    case "boolean":
      return { type: "boolean" };
    case "timestamp":
      return { type: "string", format: "date-time" };
    case "date":
      return { type: "string", format: "date" };
    case "struct":
      return {
        type: "object",
        properties: Object.fromEntries(
          Object.entries(type.fields).map(([k, v]) => [
            k,
            duckdbTypeToLogicalSchema(v),
          ]),
        ),
      };
    case "struct_map":
      return {
        type: "object",
        additionalProperties: duckdbTypeToLogicalSchema(type.value),
      };
    case "map":
      if (type.key.type !== "varchar") {
        throw new Error(`Unsupported map key type: ${type.key.type}`);
      }
      return {
        type: "object",
        additionalProperties: duckdbTypeToLogicalSchema(type.value),
      };
    default:
      throw new Error(`Unsupported DuckDB type: ${type.type}`);
  }
}

function applyCast(expr: ToSQL, exprType: ScalarType, targetType: ScalarType) {
  if (exprType === targetType) {
    return expr;
  } else if (exprType && isObjectLike(exprType) && !isObjectLike(targetType)) {
    return sql`json_extract_path_text(${expr}, '$')::${jsonTypeToDuckDBType(
      targetType,
    )}`;
  } else if (isObjectLike(targetType) && !isObjectLike(exprType)) {
    return sql`to_json((${expr})::${jsonTypeToDuckDBType(exprType)})`;
  } else {
    return sql`${expr}::${jsonTypeToDuckDBType(targetType)}`;
  }
}

function wrapArrayIndex(index: number) {
  return `[${index < 0 ? "#" : ""}${index}]`;
}
