import { BoundExpr } from "#/binder/ast";
import { PhysicalSchema } from "#/schema";
import { ToSQL } from "./snippet";

export type PlanContext = {
  schema: PhysicalSchema;

  // Time-zone offset in minutes.
  tzOffset?: number;

  // Set this to use the postgres GIN index for full text search.
  matchExprFn?: (match: string) => ToSQL;
};
export type PlanContextEngine<TableDefinition> = PlanContext & {
  table: {
    alias: string | null;
    schema: TableDefinition;
  } | null;
};

export function fail(
  ctx: PlanContext,
  prefix: string,
  expr?: BoundExpr,
): never {
  throw new PlanError(prefix, ctx.schema, expr);
}

export class PlanError extends Error {
  constructor(
    public prefix: string,
    public readonly schema: PhysicalSchema,
    public readonly expr?: BoundExpr,
  ) {
    super(`${prefix}`);
  }
}

export function singleQuote(s: string) {
  return `'${s.replace(/'/g, "''")}'`;
}

export function doubleQuote(s: string) {
  return `"${s.replace(/"/g, '""')}"`;
}

export function doubleQuoteBlackslash(s: string) {
  return `"${s.replace(/"/g, '\\"')}"`;
}

// NOTE: keep this aligned with app/utils/sql-utils.ts and its dependencies.
export function jsonPath(path: string[]) {
  // In DuckDB, double quotes for SQL are escaped by repeating the quote character:
  // https://duckdb.org/docs/sql/dialect/keywords_and_identifiers.html
  // Double quotes in json path strings for DuckDB need to be escaped with backslashes.
  return singleQuote(["$"].concat(path.map(doubleQuoteBlackslash)).join("."));
}

// NOTE: keep this aligned with app/utils/sql-utils.ts and its dependencies.
export function jsonGet(field: string, path: string[], typeName?: string) {
  return (
    `json_extract(${field}, ${jsonPath(path)})` +
    (typeName ? `::${typeName}` : "")
  );
}

export function makeObjectFromPath(path: string[], value: unknown): unknown {
  let ret: unknown = value;
  for (let i = path.length - 1; i >= 0; i--) {
    ret = { [path[i]]: ret };
  }
  return ret;
}
