import {
  getResultSchema,
  isObjectLike,
  ResponseSchema,
  traverseQuery,
  weakestScalarType,
} from "#/binder";
import {
  BoundExpr,
  BoundQuery,
  BoundSortItem,
  BoundUnpivotExpr,
} from "#/binder/ast";
import { applyCast } from "#/binder/bind";
import { coerceTypes, getExprSchema, getExprScalarType } from "#/binder/types";
import { PhysicalSchema, TableDefinition, validateSchema } from "#/schema";
import { inferSchema } from "#/schema/inference";
import { PlannedQuery, Row } from "./ast";
import {
  ClickhousePlanCtx,
  makeClickhouseArray,
  planClickhouseExpr,
} from "./clickhouse";
import { DuckDBPlanCtx, planDuckDBExpr } from "./duckdb";
import { planPostgresExpr, PostgresPlanCtx } from "./postgres";
import { ident, join, snippet, sql, ToSQL } from "./snippet";
import { makeUnpivotPostprocessor, unpivotTableAlias } from "./unpivot";
import { fail, PlanContext } from "./util";

export { ident, sql, join, snippet, ToSQL } from "./snippet";
export { singleQuote, doubleQuote } from "./util";
export { ClickhousePlanCtx, isCHMaterializedMapAvailable } from "./clickhouse";

export type PlanExprContext = PlanContext & {
  table: {
    alias: string | null;
    schema: TableDefinition;
  } | null;
};

export function makeExprContext(
  schema: PhysicalSchema,
  query: BoundQuery,
  opts?: PlanOpts,
): PlanExprContext {
  const from = query.from;
  const table = from ? schema.tables[from.name] : null;
  if (from && !table) {
    fail({ schema }, `Unknown table: ${from.name}`);
  }

  return {
    schema,
    table: from && table ? { alias: from.name, schema: table } : null,
    tzOffset: opts?.tzOffset,
    matchExprFn: opts?.matchExprFn,
  };
}

export interface PlanOpts {
  tableExprs?: Record<string, ToSQL>;
  // Time-zone offset in minutes.
  tzOffset?: number;
  // Set to true if you want the post-processor to deal with json parsing
  parseJSONFields?: boolean;
  // Set to true if you want the post-processor to throw an exception if
  // validation fails, rather than warn.
  strictValidate?: boolean;
  matchExprFn?: (match: string) => ToSQL;

  // Set to a number to limit the depth of schema inference. Defaults to undefined,
  // which means inference does not run.
  inferenceDepth?: number;
}

export function planQuery(
  schema: PhysicalSchema,
  query: BoundQuery,
  opts?: PlanOpts,
): PlannedQuery {
  if (query.summary) {
    throw new Error("Summary not supported in SQL planner");
  }

  const ctx = makeExprContext(schema, query, opts);
  query = addCoercions(query);

  let fromExpr = sql``;
  if (ctx.table?.alias) {
    const tableExpr = opts?.tableExprs?.[ctx.table.alias]
      ? opts.tableExprs[ctx.table.alias]
      : ident(ctx.table.alias);
    fromExpr = sql`${fromExpr}\nFROM ${tableExpr} AS ${ident(ctx.table.alias)}`;
  }
  if (query.unpivot.length > 0)
    if (ctx.schema.type !== "clickhouse") {
      fromExpr = sql`${fromExpr}\n${join(
        query.unpivot.map((expr, idx) => planUnpivot(ctx, idx, expr)),
        "\n",
      )}`;
    } else {
      fromExpr = sql`\nFROM (
  SELECT *
${join(
  query.unpivot.map((expr, idx) => sql`  ${planUnpivot(ctx, idx, expr)}`),
  "\n",
)}
${fromExpr}) AS ${
        ctx.table && ctx.table.alias ? ident(ctx.table.alias) : ident("sub")
      }`;
    }

  let selectExprs: [string, ToSQL][] = [];
  let numGroupBy = 0;
  let numRollup = 0;
  if ("select" in query) {
    selectExprs = query.select.map((a) => [a.alias, planExpr(ctx, a.expr)]);
  } else if ("infer" in query) {
    fail(ctx, "Infer not supported in SQL planner");
  } else {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    selectExprs = query.dimensions
      .concat(query.pivot)
      .map((a) => [a.alias, planExpr(ctx, a.expr)])
      .concat(query.measures.map((a) => [a.alias, planExpr(ctx, a.expr)])) as [
      string,
      ToSQL,
    ][];
    numGroupBy = query.dimensions.length;
    numRollup = query.pivot.length;
  }

  const filter = query.filter ? planExpr(ctx, query.filter) : null;

  const orderBy = (query.sort ?? [])
    .map((s) => planSortItem(ctx, s))
    .concat(
      numRollup > 0
        ? Array.from(
            { length: numGroupBy + numRollup },
            (_, i) => sql`${snippet(`${i + 1}`)}`,
          )
        : [],
    );

  if (query.cursor) {
    fail(ctx, "Cursor not supported in SQL planner");
  }

  let groupBy = null;
  if (numGroupBy + numRollup > 0) {
    const groupExprs = join(
      Array.from(
        { length: numGroupBy + numRollup },
        (_, i) => sql`${snippet(`${i + 1}`)}`,
      ),
      ", ",
    );
    groupBy = numRollup > 0 ? sql`ROLLUP(${groupExprs})` : groupExprs;
  }

  const having =
    numRollup > 0 && "dimensions" in query && query.dimensions.length > 0
      ? sql`GROUPING(${selectExprs[query.dimensions.length - 1][1]})=0`
      : null;

  const sqlQuery = sql`SELECT ${join(
    selectExprs.map(([alias, e]) => sql`${e} AS ${ident(alias)}`),
    "\n, ",
  )}${fromExpr}${filter ? sql`\nWHERE ${filter}` : sql``}${
    groupBy ? sql`\nGROUP BY ${groupBy}` : sql``
  }${having ? sql`\nHAVING ${having}` : sql``}${
    orderBy.length > 0 ? sql`\nORDER BY ${join(orderBy, ", ")}` : sql``
  }${
    query.limit && numRollup === 0
      ? sql`\nLIMIT ${snippet(`${query.limit}`)}`
      : sql``
  }${
    numRollup > 0 && ctx.schema.type === "clickhouse"
      ? sql`\nSETTINGS group_by_use_nulls=1`
      : sql``
  }`;

  // Deep copy the schema so that we can mutate it without affecting the source schema
  const resultSchema = JSON.parse(JSON.stringify(getResultSchema(query)));

  const parseJSONFields = !!opts?.parseJSONFields;
  return {
    sql: sqlQuery,
    postProcess:
      numRollup > 0
        ? makeUnpivotPostprocessor({
            schema: resultSchema,
            pivotFields: "pivot" in query ? query.pivot : [],
            measureFields: ("measures" in query ? query.measures : []).map(
              (expr) => expr.alias,
            ),
            dialect: ctx.schema.type,
            parseJSONFields,
            strictValidate: opts?.strictValidate,
            limit: query.limit ?? undefined,
            inferenceDepth: opts?.inferenceDepth,
          })
        : makeDefaultPostProcessor({
            schema: resultSchema,
            parseJSONFields,
            strictValidate: opts?.strictValidate,
            inferenceDepth: opts?.inferenceDepth,
          }),
    schema: resultSchema,
  };
}

export function planSortItem(ctx: PlanExprContext, expr: BoundSortItem): ToSQL {
  const exprPart =
    "alias" in expr ? ident(expr.alias) : planExpr(ctx, expr.expr);
  return sql`${exprPart} ${snippet(expr.dir)}`;
}

export function planExpr(ctx: PlanExprContext, expr: BoundExpr): ToSQL {
  switch (ctx.schema.type) {
    case "duckdb":
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return planDuckDBExpr(ctx as DuckDBPlanCtx, expr);
    case "postgres":
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return planPostgresExpr(ctx as PostgresPlanCtx, expr);
    case "clickhouse":
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return planClickhouseExpr(ctx as ClickhousePlanCtx, expr);
  }
}

export function planUnpivot(
  ctx: PlanExprContext,
  idx: number,
  expr: BoundUnpivotExpr,
) {
  const alias = unpivotTableAlias(idx);
  switch (ctx.schema.type) {
    case "duckdb":
      if (expr.type === "object") {
        // I have no idea why DuckDB has this crazy _(alias) syntax.
        // https://discord.com/channels/909674491309850675/1032659480539824208/1224594521426362398
        return sql`, unnest(map_entries(${planExpr(
          ctx,
          expr.expr,
        )}::json::map(string, json))) AS ${ident(alias)}(${ident(alias)})`;
      } else {
        return sql`, unnest(from_json(${planExpr(
          ctx,
          expr.expr,
        )}, '["json"]')) AS ${ident(alias)}(${ident(alias)})`;
      }
    case "postgres":
      if (expr.type === "object") {
        return sql`, jsonb_each(${planExpr(ctx, expr.expr)}) AS ${ident(
          alias,
        )}`;
      } else {
        return sql`, jsonb_array_elements(${planExpr(
          ctx,
          expr.expr,
        )}) AS ${ident(alias)}`;
      }
    case "clickhouse":
      let baseExpr: ToSQL;
      expr = {
        ...expr,
        expr: addUnpivotArgToField(expr.expr),
      };
      if (expr.type === "object") {
        baseExpr = planExpr(ctx, expr.expr);
      } else {
        baseExpr =
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          makeClickhouseArray(ctx as ClickhousePlanCtx, expr.expr, false) ||
          sql`JSONExtractArrayRaw(COALESCE(${planExpr(
            ctx,
            expr.expr,
          )}, 'null'))`;
      }

      return sql`, arrayJoin(${baseExpr}) AS ${ident(alias)}`;
  }
}

function makeDefaultPostProcessor({
  schema,
  parseJSONFields,
  strictValidate,
  inferenceDepth,
}: {
  schema: ResponseSchema;
  parseJSONFields: boolean;
  strictValidate?: boolean;
  inferenceDepth?: number;
}) {
  return async function* (rows: AsyncIterable<Row>): AsyncIterable<Row> {
    let rowSchema = schema.items;
    const jsonFields = new Set(
      Object.entries(rowSchema.properties)
        .filter(([_, v]) => isObjectLike(weakestScalarType(v)))
        .map(([k, _]) => k),
    );
    for await (const row of rows) {
      if (parseJSONFields) {
        parseJSONFieldsMut({ row, jsonFields });
      }
      validateSchema({
        data: row,
        schema: rowSchema,
        strict: strictValidate,
      });
      if (inferenceDepth !== undefined) {
        // Every row is guaranteed to be an object, so it's ok to assume that
        // the inferred schema will be compatible.
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        rowSchema = inferSchema({
          schema: rowSchema,
          value: row,
          depth: inferenceDepth,
        }) as typeof rowSchema;
      }
      yield row;
    }
    schema.items = rowSchema;
  };
}

export function parseJSONFieldsMut({
  row,
  jsonFields,
}: {
  row: Row;
  jsonFields: Set<string>;
}) {
  for (const [k, v] of Object.entries(row)) {
    if (jsonFields.has(k) && typeof v === "string") {
      row[k] = JSON.parse(v);
    }
  }
}

function addUnpivotArgToField(expr: BoundExpr): BoundExpr {
  if (expr.op === "field" && !expr.source) {
    return {
      ...expr,
      source: { kind: "unpivot-arg" },
    };
  } else {
    return expr;
  }
}

export function transformAddCoercions(expr: BoundExpr): boolean {
  if (expr.op === "and" || expr.op === "or") {
    expr.children?.forEach((child) => transformAddCoercions(child));
  } else if (
    expr.op === "eq" ||
    expr.op === "is" ||
    expr.op === "ne" ||
    expr.op === "lt" ||
    expr.op === "le" ||
    expr.op === "gt" ||
    expr.op === "ge" ||
    expr.op === "match"
  ) {
    const [left, right] = coerceTypes(
      getExprSchema(expr.left),
      getExprSchema(expr.right),
      true,
    );

    expr.left = left ? applyCast(expr.left, left, false) : expr.left;
    expr.right = right ? applyCast(expr.right, right, false) : expr.right;
  } else if (expr.op === "includes") {
    if (!isObjectLike(getExprScalarType(expr.haystack))) {
      expr.haystack = applyCast(expr.haystack, "object", false);
    }
    expr.needle = applyCast(expr.needle, "object", false);
  }

  return true;
}

export function addCoercions(query: BoundQuery): BoundQuery {
  const copiedQuery = JSON.parse(JSON.stringify(query));
  traverseQuery(copiedQuery, transformAddCoercions);

  return copiedQuery;
}
