# Terraform Guide

This directory contains Terraform configurations for managing Braintrust's infrastructure across different environments.

## Directory Structure

- `env/` - Contains environment-specific configurations
  - `sandbox/` - Sandbox terraform stacks
  - `prod/` - Production terraform stacks
  - `bootstrap/` - Only used for initial terraform setup

## Prerequisites

1. Install Terraform

   ```bash
   make install-terraform
   ```

2. Login to AWS SSO:

   ```bash
   aws sso login
   ```

   Note: you must have AdministratorAccess in IAM Identity Center to run terraform.

3. Setup a brainstore license key:
   ```bash
   echo "export TF_VAR_brainstore_license_key=<license-key>" >> ~/.zshrc
   exec $SHELL
   ```

## Working with Terraform

To update a specific terraform stack (e.g., the sandbox test data plane):

```bash
# Change into the sandbox test data plane stack
cd ../env/sandbox/test-aws-data-plane

# Initialize the terraform stack (downloads providers, modules, initializes state)
# This is only required once per stack
terraform init

# Change your code in main.tf or in the terraform-aws-braintrust-data-plane module

# Carefully review the plan of proposed changes.
# Watch out for anything that replaces a database, KMS key, or S3 bucket.
terraform plan

# Apply the changes (this will prompt you with a plan and ask for confirmation)
terraform apply
```
