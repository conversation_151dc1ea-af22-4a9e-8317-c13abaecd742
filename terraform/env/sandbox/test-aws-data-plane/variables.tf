variable "brainstore_license_key" {
  type        = string
  description = "The license key for the Brainstore instance"
  default     = null

  validation {
    condition     = var.brainstore_license_key != null || fileexists("${path.module}/.brainstore-license-key.txt")
    error_message = "The brainstore_license_key must be provided either as a variable or in a .brainstore-license-key.txt file."
  }
}
