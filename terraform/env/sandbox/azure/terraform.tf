terraform {
  required_version = ">= 1.11.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = ">= 4.23.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.20.0"
    }
  }

  backend "s3" {
    region       = "us-east-1"
    bucket       = "braintrust-terraform-state"
    use_lockfile = true
    key          = "env/sandbox/azure/terraform.tfstate"
  }
}
