#!/bin/bash
set -e

# Get Terraform outputs
cat << EOF > helm-terraform-values.yaml
terraform:
  orgName: "$(terraform output -raw braintrust_org_name)"
  storageAccountName: "$(terraform output -raw storage_account_name)"
  azureClientId: "$(terraform output -raw aks_identity_client_id)"
  azurePrincipalId: "$(terraform output -raw aks_identity_object_id)"
  azureTenantId: "$(terraform output -raw azure_tenant_id)"
  keyVaultName: "$(terraform output -raw key_vault_name)"
EOF
