#!/bin/bash

set -euo pipefail

# Change to the directory where the script is located
cd "$(dirname "${BASH_SOURCE[0]}")"

namespace="braintrust"

context=$(kubectl config current-context)
cluster_url=$(kubectl config view --minify -o jsonpath='{.clusters[0].cluster.server}')
echo "Current Kubernetes context: "
echo "$context ($cluster_url)"
echo
read -p "Do you want to proceed with deployment to this kubernetes cluster? (y/N) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "Deployment cancelled."
    exit 1
fi

echo "Creating namespace..."
kubectl create namespace "${namespace}" --dry-run=client -o yaml | kubectl apply -f -

echo "Generating Helm values from Terraform outputs..."
./generate-helm-terraform-values.sh

# Read the terraform values
ORG_NAME=$(terraform output -raw braintrust_org_name)
STORAGE_ACCOUNT_NAME=$(terraform output -raw storage_account_name)
AZURE_CLIENT_ID=$(terraform output -raw aks_identity_client_id)
AZURE_PRINCIPAL_ID=$(terraform output -raw aks_identity_object_id)
AZURE_TENANT_ID=$(terraform output -raw azure_tenant_id)
KEY_VAULT_NAME=$(terraform output -raw key_vault_name)

echo "Deploying Helm chart..."
set -x
helm upgrade --install braintrust \
    ../../../../../helm/braintrust \
    --namespace "${namespace}" \
    --values helm-override-values.yaml \
    --set terraform.orgName="${ORG_NAME}" \
    --set terraform.storageAccountName="${STORAGE_ACCOUNT_NAME}" \
    --set terraform.azureClientId="${AZURE_CLIENT_ID}" \
    --set terraform.azurePrincipalId="${AZURE_PRINCIPAL_ID}" \
    --set terraform.azureTenantId="${AZURE_TENANT_ID}" \
    --set terraform.keyVaultName="${KEY_VAULT_NAME}" \
    --force \
    --wait \
    --timeout 5m

echo "Deployment completed successfully!"
echo
echo "To see the status of the deployment, run:"
echo "kubectl get pods -n ${namespace}"
echo
echo "To see the logs of the deployment, run:"
echo "kubectl logs -n ${namespace} -l app=braintrust-api"
echo "kubectl logs -n ${namespace} -l app=brainstore"
echo
echo "To port forward the API, run:"
echo "./port-forward.sh"
