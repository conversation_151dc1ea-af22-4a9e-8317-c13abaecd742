provider "azurerm" {
  subscription_id = "36347dc6-dfe5-4575-a744-72f38ca320f3" # Main
  tenant_id       = "94d2932f-d842-404b-8384-b8d4b4691f1a" # Ankur Braintrustdata
  # This should be true on first run
  # skip_provider_registration = true
  storage_use_azuread = true
  features {
    postgresql_flexible_server {
      # This is the default but should probably be changed in prod
      restart_server_on_configuration_value_change = true
    }
  }
}
