#!/bin/bash
# This script will get the kube_config from terraform output and merge it into the global kubeconfig so you can use kubectl

set -e

echo "Getting kube_config from terraform output"
terraform output -raw kube_config > aks_kubeconfig.yaml.tmp

# Merge with existing kubeconfig
KUBECONFIG=~/.kube/config:aks_kubeconfig.yaml.tmp kubectl config view --flatten > ~/.kube/config.tmp
mv ~/.kube/config.tmp ~/.kube/config

rm -f aks_kubeconfig.yaml.tmp

echo "Kubeconfig has been merged into ~/.kube/config"
