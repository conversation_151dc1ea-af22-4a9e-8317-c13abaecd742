global:
  orgName: "mdeeks-azure"
  namespace: "braintrust"
  createNamespace: false

cloud: "azure"
objectStorage:
  azure:
    storageAccountName: "btstorageouhrsz"

api:
  replicas: 1
  image:
    tag: latest
    pullPolicy: Always
  serviceAccount:
    azureClientId: "37383d3f-f13f-4892-92ca-e872c27d7214"
  resources:
    requests:
      cpu: "500m"
      memory: "512Mi"
    limits:
      cpu: "500m"
      memory: "512Mi"
  extraEnvVars:
    - name: AZURE_LOCATION
      value: "eastus"
  # extraEnvVars:
  #   - name: AZURE_LOCATION
  #     value: "eastus"
  # Example:
  # - name: MY_ENV_VAR
  #   value: "my-value"
  # - name: ANOTHER_VAR
  #   value: "another-value"

brainstore:
  replicas: 1
  image:
    tag: latest
    pullPolicy: Always
  serviceAccount:
    # Azure Workload Identity client ID for accessing storage accounts
    azureClientId: "37383d3f-f13f-4892-92ca-e872c27d7214"
  resources:
    requests:
      cpu: "500m"
      memory: "512Mi"
    limits:
      cpu: "500m"
      memory: "512Mi"
  extraEnvVars:
    - name: BRAINSTORE_SYSTEM_MEMORY_IGNORE_SWAP
      value: "true"
    - name: AZURE_STORAGE_ACCOUNT_NAME
      value: "btstorageouhrsz"
    - name: AZURE_LOCATION
      value: "eastus"

azureKeyVaultCSI:
  enabled: true
  name: "bt-azure-kv"
  userAssignedIdentityID: "c08b8d2e-be8f-489b-8d44-ae61cac5718a"
  clientID: "37383d3f-f13f-4892-92ca-e872c27d7214"
  tenantId: "94d2932f-d842-404b-8384-b8d4b4691f1a"
