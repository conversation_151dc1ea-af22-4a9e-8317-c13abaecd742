locals {
  production_attachments_bucket_name = local.production_code_bundle_bucket_name
  production_code_bundle_bucket_name = "bt3subnets-3-codebundlebucket-lvt2jm5istw6"
}

data "aws_s3_bucket" "attachments_bucket" {
  bucket = local.production_attachments_bucket_name
}

# This is a temporary fix to enable attachment expiration for bill.com org
# This can go away after we build our own lifecycle management
resource "aws_s3_bucket_lifecycle_configuration" "attachments_bucket_lifecycle" {
  bucket = data.aws_s3_bucket.attachments_bucket.id

  rule {
    id     = "expire-attachments-7-days-billcom"
    status = "Enabled"

    # Filter to specific path for attachments
    filter {
      # Path for bill.com org
      prefix = "attachments/6326aded-3dcf-4bc2-9d90-cd500acbbf62/"
    }

    # Expire all objects after specified days
    expiration {
      days = "7"
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = "7"
    }
  }
}
