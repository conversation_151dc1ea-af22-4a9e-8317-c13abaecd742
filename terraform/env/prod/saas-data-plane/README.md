This terraform stack is used to modify our production SaaS data plane beyond the default configuration we provide to customers.

**Only put Braintrust SaaS specific changes here.**

If you want something available to customers, then add it to the Cloudformation template.

If you ONLY want something that changes our SaaS data plane, then add it here.

These resources should reference the production Cloudformation resources by hard-coded names or by data lookups. You can not modify resources that are managed by the Cloudformation stack. For example, you can not modify an S3 bucket directly, but you can modify the lifecycle configuration of an S3 bucket since that isn't managed by the Cloudformation stack and they are separate AWS resources.
