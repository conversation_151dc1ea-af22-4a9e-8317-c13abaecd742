terraform {
  backend "s3" {
    region       = "us-east-1"
    bucket       = "braintrust-terraform-state"
    use_lockfile = true
    key          = "prod/saas-data-plane/terraform.tfstate"
  }
  required_version = ">= 1.10.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 6.0"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  # Only allow running against prod
  allowed_account_ids = ["************"]
  assume_role {
    role_arn     = "arn:aws:iam::************:role/TerraformExecutionRole"
    session_name = "terraform"
  }
}
