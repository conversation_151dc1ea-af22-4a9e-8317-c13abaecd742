resource "aws_route53_zone" "braintrust_dot_dev" {
  name    = "braintrust.dev"
  comment = "Managed by Terraform"
}

resource "aws_route53_record" "braintrust_dot_dev_ns" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "braintrust.dev"
  type    = "NS"
  ttl     = "172800"
  records = [
    "${aws_route53_zone.braintrust_dot_dev.name_servers[0]}.",
    "${aws_route53_zone.braintrust_dot_dev.name_servers[1]}.",
    "${aws_route53_zone.braintrust_dot_dev.name_servers[2]}.",
    "${aws_route53_zone.braintrust_dot_dev.name_servers[3]}."
  ]
}

### Records ---

resource "aws_route53_record" "braintrust_dev_txt" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "braintrust.dev"
  type    = "TXT"
  ttl     = "300"
  records = [
    "google-site-verification=-nzCMbSqGLFHIKBcQYdRYzpeyfUTQ8tVCzmxkPp7NCc",
    "v=spf1 include:_spf.google.com ~all",
    "google-site-verification=4uTC74PzruutORDPpUjtAhbDMZTZu5l9yjYiRzZ1VPs",
  ]
}

# Notion friendly link
resource "aws_route53_record" "notion_braintrust_dev" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "notion.braintrust.dev"
  type    = "CNAME"
  ttl     = "300"
  records = ["external.notion.site"]
}

# Required to verify the domain with Notion
resource "aws_route53_record" "notion_braintrust_dev_txt" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "_notion-dcv.notion.braintrust.dev"
  type    = "TXT"
  ttl     = "300"
  records = ["1a6f7858-0289-81fd-be8d-0070242e0fac"]
}

# Pylon email verification
resource "aws_route53_record" "pylon_braintrust_dev_domainkey" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "20250327162811pm._domainkey"
  type    = "TXT"
  ttl     = "300"
  records = ["k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCV4zKFbR5FPauTi5giILDcui9JyH6MW1JZO5naEouUOclXWP6AzkXPOefyt6yXL4d8Ru9Hzr7RGV+kSCrTLv2bSdkoZkV//nhJ9+5L40piaJ5KjRydTj6nb6Ge3E07qXMgu18gdJepAVbMGIzYFB9WkH4C5Z0XciWYheo92O9AAwIDAQAB"]
}

resource "aws_route53_record" "pylon_braintrust_dev_pmbounces" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "pm-bounces"
  type    = "CNAME"
  ttl     = "300"
  records = ["pm.mtasv.net"]
}

resource "aws_route53_record" "_cf-custom-hostname_trust_braintrust_dev" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "_cf-custom-hostname.trust.braintrust.dev"
  type    = "TXT"
  ttl     = "300"
  records = ["49672943-01b6-4f6c-baf8-2e4fa0bcd7ad"]
}

resource "aws_route53_record" "trust_braintrust_dev" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "trust.braintrust.dev"
  type    = "CNAME"
  ttl     = "300"
  records = ["65a1d6f5b4c7501f89272256.cname.vantatrust.com"]
}

# Mailgun/Customer.io MX records
resource "aws_route53_record" "braintrust_dev_mx" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "cio129607"
  type    = "MX"
  ttl     = "300"
  records = [
    "10 mxa.mailgun.org",
    "10 mxb.mailgun.org"
  ]
}

# Mailgun/Customer.io SPF record
resource "aws_route53_record" "braintrust_dev_spf" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "cio129607"
  type    = "TXT"
  ttl     = "300"
  records = ["v=spf1 include:mailgun.org ~all"]
}

resource "aws_route53_record" "braintrust_dev_dkim" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "smtp._domainkey.cio129607"
  type    = "TXT"
  ttl     = "300"
  records = ["k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDUn4t/2elsSv8Kd32evw+3VB2IJa/IV24govGogPOM+X+xw7H3Jenj4deQFH77c6L/56Q6d8PqMyd081gqNMwfU3eg34BpkFr7VLkv7Us/joYOJ2rR5WegaX5w5glwjGPJwh7sHfL06qy5BNjDvGtn0XFlWL0pzNiPGqp+guScfwIDAQAB"]
}

resource "aws_route53_record" "braintrust_dev_dmarc" {
  zone_id = aws_route53_zone.braintrust_dot_dev.zone_id
  name    = "_dmarc.braintrust.dev"
  type    = "TXT"
  ttl     = "300"
  records = ["v=DMARC1; p=none;"]
}
