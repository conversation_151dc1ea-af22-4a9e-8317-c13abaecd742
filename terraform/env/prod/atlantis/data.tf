locals {
  environment       = "production"
  environment_short = "prod"
}

# This must be created manually in AWS Secrets Manager
data "aws_secretsmanager_secret" "atlantis_github_oauth" {
  name = "production/atlantis-github-oauth"
}

data "aws_secretsmanager_secret" "atlantis_github_app_key" {
  name = "production/atlantis-github-app-key"
}

data "aws_secretsmanager_secret" "atlantis_github_webhook_secret" {
  name = "production/atlantis-github-webhook-secret"
}

data "aws_secretsmanager_secret" "atlantis_redis_password" {
  name = "production/atlantis-redis-password"
}

data "aws_secretsmanager_secret_version" "atlantis_redis_password" {
  secret_id = data.aws_secretsmanager_secret.atlantis_redis_password.id
}

data "terraform_remote_state" "core" {
  backend = "s3"
  config = {
    region       = "us-east-1"
    bucket       = "braintrust-terraform-state"
    use_lockfile = true
    key          = "prod/core/terraform.tfstate"
  }
}

data "aws_caller_identity" "current" {}
