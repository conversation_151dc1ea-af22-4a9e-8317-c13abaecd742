module "oauth2_proxy_service" {
  source = "../../../modules/ecs-service"

  service_name     = "atlantis-oauth2-proxy"
  environment      = "production"
  ecs_cluster_name = data.terraform_remote_state.core.outputs.cluster_name

  container_image      = "quay.io/oauth2-proxy/oauth2-proxy:${var.oauth2_proxy_version}"
  container_port       = 4180
  force_new_deployment = var.oauth2_proxy_force_new_deployment

  is_public_service = true
  # This will be "atlantis.prod.braintrust.dev" since that is a friendlier name for users
  custom_dns_hostname = "atlantis"
  authorized_cidr_blocks = {
    internet = "0.0.0.0/0"
  }
  alb_health_check_path = "/ping"

  cpu            = 512
  memory         = 1024
  desired_count  = 2
  min_count      = 2
  max_count      = 4
  cpu_scaling    = true
  memory_scaling = false

  environment_variables = {
    OAUTH2_PROXY_HTTP_ADDRESS  = "0.0.0.0:4180"
    OAUTH2_PROXY_COOKIE_DOMAIN = ".prod.braintrust.dev"
    OAUTH2_PROXY_COOKIE_SECURE = "true"
    OAUTH2_PROXY_PROVIDER      = "github"
    OAUTH2_PROXY_CLIENT_ID     = "Ov23liWA7ucnXr9WzxMF"
    OAUTH2_PROXY_COOKIE_SECRET = random_password.oauth2_proxy_cookie_secret.result
    OAUTH2_PROXY_REDIRECT_URL  = "${module.oauth2_proxy_service.service_url}/oauth2/callback"
    OAUTH2_PROXY_UPSTREAMS     = module.atlantis_service.service_url
    # Github sends webhooks to /events, so we need to skip auth for that path
    # Atlantis validates the webhook signature against the secret
    OAUTH2_PROXY_SKIP_AUTH_ROUTES     = "POST=^/events$"
    OAUTH2_PROXY_EMAIL_DOMAINS        = "*" # This must be * for Github auth
    OAUTH2_PROXY_CUSTOM_SIGN_IN_LOGO  = "https://www.braintrust.dev/logo-letter.png"
    OAUTH2_PROXY_BANNER               = "<b>Sign-in to Atlantis</b>"
    OAUTH2_PROXY_FOOTER               = "-"
    OAUTH2_PROXY_SILENCE_PING_LOGGING = "true"
  }
  secrets = [
    {
      name      = "OAUTH2_PROXY_CLIENT_SECRET"
      valueFrom = data.aws_secretsmanager_secret.atlantis_github_oauth.arn
    }
  ]

  vpc_config = data.terraform_remote_state.core.outputs.vpc_config
}

resource "random_password" "oauth2_proxy_cookie_secret" {
  length           = 32
  override_special = "-_"
}
