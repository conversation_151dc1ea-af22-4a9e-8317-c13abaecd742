variable "atlantis_version" {
  description = "Docker image version for the atlantis service"
  type        = string
}

variable "oauth2_proxy_version" {
  description = "Docker image version for the oauth2-proxy service"
  type        = string
}

variable "atlantis_force_new_deployment" {
  description = "Force a new deployment of the atlantis service"
  type        = bool
  default     = false
}

variable "oauth2_proxy_force_new_deployment" {
  description = "Force a new deployment of the oauth2-proxy service"
  type        = bool
  default     = false
}
