locals {
  asset_buckets = {
    us-east-1      = "braintrust-assets-us-east-1"
    us-east-2      = "braintrust-assets-us-east-2"
    us-west-2      = "braintrust-assets-us-west-2"
    eu-west-1      = "braintrust-assets-eu-west-1"
    ap-southeast-2 = "braintrust-assets-ap-southeast-2"
    ca-central-1   = "braintrust-assets-ca-central-1"
  }
}

# These buckets are primarily used to store the Lambda Zips for terraform
# They must be publicly readable and must be one in reach region that customers
# want to deploy into. Lambdas can only pull from S3 in the same region.
resource "aws_s3_bucket" "braintrust_assets" {
  for_each = local.asset_buckets
  bucket   = each.value
  region   = each.key

  tags = {
    Environment = "production"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "braintrust_assets" {
  for_each = aws_s3_bucket.braintrust_assets
  bucket   = aws_s3_bucket.braintrust_assets[each.key].id
  region   = each.key

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "braintrust_assets" {
  for_each = aws_s3_bucket.braintrust_assets
  bucket   = aws_s3_bucket.braintrust_assets[each.key].id
  region   = each.key

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "braintrust_assets" {
  for_each = aws_s3_bucket.braintrust_assets
  bucket   = aws_s3_bucket.braintrust_assets[each.key].id
  region   = each.key

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "PublicReadGetObject"
        Effect = "Allow"
        Principal = {
          AWS = "*"
        }
        Action   = "s3:GetObject"
        Resource = "${aws_s3_bucket.braintrust_assets[each.key].arn}/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket_public_access_block.braintrust_assets]
}
