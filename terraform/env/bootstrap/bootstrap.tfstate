{"version": 4, "terraform_version": "1.10.5", "serial": 38, "lineage": "302e9731-da58-65bb-be65-69dfc3da1f9d", "outputs": {"atlantis_task_role_prod_arn": {"value": "arn:aws:iam::872608195481:role/AtlantisTaskRole", "type": "string"}, "atlantis_task_role_sandbox_arn": {"value": "arn:aws:iam::982534393296:role/AtlantisTaskRole", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_iam_role", "name": "atlantis_task_role_prod", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::872608195481:role/AtlantisTaskRole", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-24T17:50:13Z", "description": "", "force_detach_policies": false, "id": "AtlantisTaskRole", "inline_policy": [{"name": "AtlantisAssumeTerraformRole", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:iam::872608195481:role/TerraformExecutionRole\",\"arn:aws:iam::982534393296:role/TerraformExecutionRole\"]}]}"}, {"name": "prod-atlantis-ecs-exec-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"ssmmessages:CreateControlChannel\",\"ssmmessages:CreateDataChannel\",\"ssmmessages:OpenControlChannel\",\"ssmmessages:OpenDataChannel\"],\"Effect\":\"Allow\",\"Resource\":\"*\"},{\"Action\":[\"ecs:ExecuteCommand\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": ["arn:aws:iam::aws:policy/AdministratorAccess"], "max_session_duration": 3600, "name": "AtlantisTaskRole", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {"tf-source": "bootstrap"}, "unique_id": "AROA4WK4AMOM2XFYCPNK7"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "atlantis_task_role_sandbox", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].sandbox", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::982534393296:role/AtlantisTaskRole", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-24T17:50:13Z", "description": "", "force_detach_policies": false, "id": "AtlantisTaskRole", "inline_policy": [{"name": "AtlantisAssumeTerraformRole", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:iam::982534393296:role/TerraformExecutionRole\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "AtlantisTaskRole", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {"tf-source": "bootstrap"}, "unique_id": "AROA6JQ45BXII2AZFEI2C"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "terraform_execution_prod", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::872608195481:role/TerraformExecutionRole", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"}},{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::872608195481:role/AtlantisTaskRole\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-04-11T21:55:54Z", "description": "", "force_detach_policies": false, "id": "TerraformExecutionRole", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AdministratorAccess"], "max_session_duration": 3600, "name": "TerraformExecutionRole", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {"tf-source": "bootstrap"}, "unique_id": "AROA4WK4AMOMTSKHAKLYV"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.atlantis_task_role_prod"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "terraform_execution_sandbox", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].sandbox", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::982534393296:role/TerraformExecutionRole", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"}},{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::982534393296:role/AtlantisTaskRole\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-04-11T21:55:54Z", "description": "", "force_detach_policies": false, "id": "TerraformExecutionRole", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AdministratorAccess"], "max_session_duration": 3600, "name": "TerraformExecutionRole", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {"tf-source": "bootstrap"}, "unique_id": "AROA6JQ45BXIJZSZREYUS"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.atlantis_task_role_sandbox"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "atlantis_task_role_prod_admin", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"id": "AtlantisTaskRole-20250724204822639300000001", "policy_arn": "arn:aws:iam::aws:policy/AdministratorAccess", "role": "AtlantisTaskRole"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.atlantis_task_role_prod"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "terraform_execution_prod_admin", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"id": "TerraformExecutionRole-20250411215554995000000001", "policy_arn": "arn:aws:iam::aws:policy/AdministratorAccess", "role": "TerraformExecutionRole"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.atlantis_task_role_prod", "aws_iam_role.terraform_execution_prod"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "terraform_execution_sandbox_admin", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].sandbox", "instances": [{"schema_version": 0, "attributes": {"id": "TerraformExecutionRole-20250411215555119600000001", "policy_arn": "arn:aws:iam::aws:policy/AdministratorAccess", "role": "TerraformExecutionRole"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.atlantis_task_role_sandbox", "aws_iam_role.terraform_execution_sandbox"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::braintrust-terraform-state", "bucket": "braintrust-terraform-state", "bucket_domain_name": "braintrust-terraform-state.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "braintrust-terraform-state.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "4039a3af4e1cd0e79e68e971b1e5526cbd1b18d5f33f38ef8ba5c9576bdbab95", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "braintrust-terraform-state", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"arn:aws:iam::982534393296:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_6907e31c110f66dd\",\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"]},\"Resource\":\"arn:aws:s3:::braintrust-terraform-state/*\"},{\"Action\":\"s3:ListBucket\",\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"arn:aws:iam::982534393296:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_6907e31c110f66dd\",\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"]},\"Resource\":\"arn:aws:s3:::braintrust-terraform-state\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {"tf-source": "bootstrap"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"bucket": "braintrust-terraform-state", "id": "braintrust-terraform-state", "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"arn:aws:iam::982534393296:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_6907e31c110f66dd\",\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"]},\"Resource\":\"arn:aws:s3:::braintrust-terraform-state/*\"},{\"Action\":[\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"arn:aws:iam::982534393296:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_6907e31c110f66dd\",\"arn:aws:iam::872608195481:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26\"]},\"Resource\":\"arn:aws:s3:::braintrust-terraform-state\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "braintrust-terraform-state", "id": "braintrust-terraform-state", "ignore_public_acls": true, "restrict_public_buckets": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"bucket": "braintrust-terraform-state", "expected_bucket_owner": "", "id": "braintrust-terraform-state", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].prod", "instances": [{"schema_version": 0, "attributes": {"bucket": "braintrust-terraform-state", "expected_bucket_owner": "", "id": "braintrust-terraform-state", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}], "check_results": null}