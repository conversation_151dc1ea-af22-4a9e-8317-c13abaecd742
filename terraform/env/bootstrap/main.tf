# One terraform state bucket for all accounts
locals {
  aws_account_sandbox = "************"
  aws_account_prod    = "************"
  roles_with_terraform_state_access = [
    local.prod_admin_role_arn,
    local.sandbox_admin_role_arn
  ]
  prod_admin_role_arn    = "arn:aws:iam::${local.aws_account_prod}:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_601e556fd24fac26"
  sandbox_admin_role_arn = "arn:aws:iam::${local.aws_account_sandbox}:role/aws-reserved/sso.amazonaws.com/us-east-2/AWSReservedSSO_AdministratorAccess_6907e31c110f66dd"
}

resource "aws_s3_bucket" "terraform_state" {
  provider = aws.prod
  bucket   = "braintrust-terraform-state"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "terraform_state" {
  provider = aws.prod
  bucket   = aws_s3_bucket.terraform_state.id

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "terraform_state" {
  provider = aws.prod
  bucket   = aws_s3_bucket.terraform_state.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "terraform_state" {
  provider                = aws.prod
  bucket                  = aws_s3_bucket.terraform_state.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = false # This enables cross-account access
}

resource "aws_s3_bucket_policy" "terraform_state" {
  provider = aws.prod
  bucket   = aws_s3_bucket.terraform_state.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = local.roles_with_terraform_state_access
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "arn:aws:s3:::${aws_s3_bucket.terraform_state.id}/*"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = local.roles_with_terraform_state_access
        }
        Action = [
          "s3:ListBucket"
        ]
        Resource = "arn:aws:s3:::${aws_s3_bucket.terraform_state.id}"
      }
    ]
  })
}

# Create TerraformExecutionRole in sandbox account
resource "aws_iam_role" "terraform_execution_sandbox" {
  provider = aws.sandbox
  name     = "TerraformExecutionRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = local.prod_admin_role_arn
        }
        Action = "sts:AssumeRole"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.atlantis_task_role_sandbox.arn
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Attach AdministratorAccess policy to sandbox role
resource "aws_iam_role_policy_attachment" "terraform_execution_sandbox_admin" {
  provider   = aws.sandbox
  role       = aws_iam_role.terraform_execution_sandbox.name
  policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
}

# Create Atlantis task role in sandbox account
resource "aws_iam_role" "atlantis_task_role_sandbox" {
  provider = aws.sandbox
  name     = "AtlantisTaskRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Create TerraformExecutionRole in prod account
resource "aws_iam_role" "terraform_execution_prod" {
  provider = aws.prod
  name     = "TerraformExecutionRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = local.prod_admin_role_arn
        }
        Action = "sts:AssumeRole"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.atlantis_task_role_prod.arn
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Attach AdministratorAccess policy to prod role
resource "aws_iam_role_policy_attachment" "terraform_execution_prod_admin" {
  provider   = aws.prod
  role       = aws_iam_role.terraform_execution_prod.name
  policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
}

# Create Atlantis task role in prod account
resource "aws_iam_role" "atlantis_task_role_prod" {
  provider = aws.prod
  name     = "AtlantisTaskRole"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "atlantis_task_role_prod_admin" {
  provider   = aws.prod
  role       = aws_iam_role.atlantis_task_role_prod.name
  policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
}

output "atlantis_task_role_prod_arn" {
  description = "ARN of the Atlantis task role in the prod account"
  value       = aws_iam_role.atlantis_task_role_prod.arn
}

output "atlantis_task_role_sandbox_arn" {
  description = "ARN of the Atlantis task role in the sandbox account"
  value       = aws_iam_role.atlantis_task_role_sandbox.arn
}
