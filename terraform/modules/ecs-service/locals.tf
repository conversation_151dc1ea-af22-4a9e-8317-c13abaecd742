locals {
  # Environment name shortening for resource names
  env_short = var.environment == "production" ? "prod" : var.environment == "staging" ? "stg" : var.environment

  # Full service name with environment prefix
  service_name = "${local.env_short}-${var.service_name}"

  # S3 bucket name for ALB access logs
  alb_logs_bucket = "bt-alb-logs-${local.env_short}"

  # Common tags including environment
  common_tags = merge(var.tags, {
    env              = var.environment
    ecs-service-name = local.service_name
  })

  # Domain names - always created
  dns_hostname    = var.custom_dns_hostname != null ? var.custom_dns_hostname : var.service_name
  internal_domain = "${local.dns_hostname}-int.${local.env_short}.braintrust.dev"
  public_domain   = "${local.dns_hostname}.${local.env_short}.braintrust.dev"
}
