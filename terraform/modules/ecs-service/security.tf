locals {
  ingress_port = var.is_public_service ? 443 : 80
}

resource "aws_security_group" "alb" {
  name        = "${local.service_name}-alb"
  description = "Security group for service ALB"
  vpc_id      = var.vpc_config.vpc_id

  tags = merge(local.common_tags, {
    Name = "${local.service_name}-alb"
  })
}

# Allow traffic from authorized security groups
resource "aws_security_group_rule" "alb_from_authorized_sg" {
  for_each = var.authorized_security_groups

  type                     = "ingress"
  from_port                = local.ingress_port
  to_port                  = local.ingress_port
  protocol                 = "tcp"
  source_security_group_id = each.value
  security_group_id        = aws_security_group.alb.id
  description              = "From authorized security group: ${each.key}"
}

# Allow traffic from authorized CIDR blocks
resource "aws_security_group_rule" "alb_from_authorized_cidr" {
  for_each = var.authorized_cidr_blocks

  type              = "ingress"
  from_port         = local.ingress_port
  to_port           = local.ingress_port
  protocol          = "tcp"
  cidr_blocks       = [each.value]
  security_group_id = aws_security_group.alb.id
  description       = "From authorized CIDR block: ${each.key}"
}

resource "aws_security_group_rule" "alb_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.alb.id
  description       = "Allow all outbound traffic"
}

resource "aws_security_group" "service" {
  name        = "${local.service_name}-service"
  description = "Security group for service ECS tasks"
  vpc_id      = var.vpc_config.vpc_id

  tags = merge(local.common_tags, {
    Name = "${local.service_name}-service"
  })
}

resource "aws_security_group_rule" "service_from_alb" {
  type                     = "ingress"
  from_port                = var.container_port
  to_port                  = var.container_port
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.alb.id
  security_group_id        = aws_security_group.service.id
  description              = "From ALB"
}

resource "aws_security_group_rule" "service_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.service.id
  description       = "Allow all outbound traffic"
}
