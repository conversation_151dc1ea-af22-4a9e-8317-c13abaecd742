resource "aws_cloudwatch_log_group" "service" {
  name              = "/ecs/${local.service_name}"
  retention_in_days = var.log_retention_days
  kms_key_id        = var.kms_key_arn

  tags = local.common_tags
}

locals {
  healthcheck = var.healthcheck_command != null ? {
    command     = ["CMD-SHELL", var.healthcheck_command]
    interval    = 5
    timeout     = 3
    retries     = 3
    startPeriod = 5
  } : null
}

resource "aws_ecs_task_definition" "service" {
  family                   = local.service_name
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.cpu
  memory                   = var.memory
  execution_role_arn       = aws_iam_role.ecs_execution_role.arn
  task_role_arn            = var.task_role_arn != null ? var.task_role_arn : aws_iam_role.ecs_task_role[0].arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = var.cpu_architecture
  }

  ephemeral_storage {
    size_in_gib = var.ephemeral_storage
  }

  dynamic "volume" {
    for_each = var.enable_efs ? [1] : []
    content {
      name = "efs-volume"
      efs_volume_configuration {
        file_system_id     = aws_efs_file_system.service[0].id
        transit_encryption = "ENABLED"
        authorization_config {
          access_point_id = aws_efs_access_point.service[0].id
        }
      }
    }
  }

  container_definitions = jsonencode(concat([
    {
      name  = local.service_name
      image = var.container_image

      command = var.command

      portMappings = [
        {
          containerPort = var.container_port
          protocol      = "tcp"
        }
      ]

      environment = [
        for key, value in var.environment_variables : {
          name  = key
          value = value
        }
      ]

      secrets = var.secrets

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.service.name
          awslogs-region        = data.aws_region.current.region
          awslogs-stream-prefix = "ecs/${local.service_name}"
        }
      }

      healthCheck = local.healthcheck

      mountPoints = var.enable_efs ? [
        {
          sourceVolume  = "efs-volume"
          containerPath = var.efs_mount_point
          readOnly      = false
        }
      ] : []

      user = var.container_user

      essential = true
    }
    ], [
    for sidecar in var.sidecar_containers : merge(sidecar, {
      logConfiguration = can(sidecar.logConfiguration) ? sidecar.logConfiguration : {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.service.name
          awslogs-region        = data.aws_region.current.region
          awslogs-stream-prefix = "ecs/${local.service_name}"
        }
      }

    })
  ]))

  tags = local.common_tags
}

resource "aws_ecs_service" "service" {
  name                   = local.service_name
  cluster                = var.ecs_cluster_name
  task_definition        = aws_ecs_task_definition.service.arn
  desired_count          = var.desired_count
  launch_type            = "FARGATE"
  force_new_deployment   = var.force_new_deployment
  enable_execute_command = true
  network_configuration {
    subnets          = var.vpc_config.private_subnets
    security_groups  = [aws_security_group.service.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.service.arn
    container_name   = local.service_name
    container_port   = var.container_port
  }
  wait_for_steady_state              = true
  propagate_tags                     = "TASK_DEFINITION"
  deployment_maximum_percent         = var.deployment_maximum_percent
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent

  depends_on = [
    aws_iam_role_policy_attachment.ecs_execution_role_policy,
    aws_iam_role_policy.ecs_execution_secrets_policy,
  ]

  tags = local.common_tags
}
