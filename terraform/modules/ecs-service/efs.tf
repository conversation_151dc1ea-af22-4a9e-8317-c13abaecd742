resource "aws_efs_file_system" "service" {
  count = var.enable_efs ? 1 : 0

  creation_token   = "${local.service_name}-efs"
  encrypted        = true
  kms_key_id       = var.kms_key_arn
  performance_mode = "generalPurpose"
  throughput_mode  = "elastic"

  tags = merge(local.common_tags, {
    Name = "${local.service_name}-efs"
  })
}

resource "aws_efs_mount_target" "service" {
  count = var.enable_efs ? length(var.vpc_config.private_subnets) : 0

  file_system_id  = aws_efs_file_system.service[0].id
  subnet_id       = var.vpc_config.private_subnets[count.index]
  security_groups = [aws_security_group.efs[0].id]
}

resource "aws_security_group" "efs" {
  count = var.enable_efs ? 1 : 0

  name        = "${local.service_name}-efs"
  description = "Security group for EFS access from ECS tasks"
  vpc_id      = var.vpc_config.vpc_id

  ingress {
    description     = "EFS/NFS from ECS tasks"
    from_port       = 2049
    to_port         = 2049
    protocol        = "tcp"
    security_groups = [aws_security_group.service.id]
  }

  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(local.common_tags, {
    Name = "${local.service_name}-efs"
  })
}

resource "aws_efs_access_point" "service" {
  count = var.enable_efs ? 1 : 0

  file_system_id = aws_efs_file_system.service[0].id
  posix_user {
    uid = var.efs_mount_uid
    gid = var.efs_mount_gid
  }
  root_directory {
    creation_info {
      owner_uid   = var.efs_mount_uid
      owner_gid   = var.efs_mount_gid
      permissions = "755"
    }
    path = var.efs_mount_point
  }
}
