resource "aws_ecs_cluster" "main" {
  name = local.cluster_name

  setting {
    name  = "containerInsights"
    value = var.container_insights_setting
  }

  dynamic "configuration" {
    for_each = var.kms_key_arn != null ? [1] : []
    content {
      managed_storage_configuration {
        fargate_ephemeral_storage_kms_key_id = var.kms_key_arn
        kms_key_id                           = var.kms_key_arn
      }
    }
  }

  tags = local.common_tags
}

resource "aws_cloudwatch_log_group" "containerinsights_performance" {
  name              = "/aws/ecs/containerinsights/${aws_ecs_cluster.main.name}/performance"
  retention_in_days = 365
  kms_key_id        = var.kms_key_arn

  tags = local.common_tags
}

resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name = aws_ecs_cluster.main.name

  capacity_providers = ["FARGATE"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}
