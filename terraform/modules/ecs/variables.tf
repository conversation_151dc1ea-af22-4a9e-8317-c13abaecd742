variable "environment" {
  description = "Environment name (e.g., production, staging, development)"
  type        = string

  validation {
    condition     = !contains(["prod", "stage"], var.environment)
    error_message = "Environment must be the full name (e.g., 'production', 'staging'), not abbreviated."
  }
}

variable "name" {
  description = "Cluster name (without environment prefix)"
  type        = string
  default     = "main"
}

variable "container_insights_setting" {
  description = "Container Insights setting (enabled, enhanced, disabled)"
  type        = string
  default     = "enhanced"

  validation {
    condition     = contains(["enabled", "enhanced", "disabled"], var.container_insights_setting)
    error_message = "Container insights setting must be one of: enabled, enhanced, disabled."
  }
}

variable "kms_key_arn" {
  description = "ARN of the Customer Managed KMS key for storage encryption. <PERSON>fault uses the AWS-managed key."
  type        = string
  default     = null
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
