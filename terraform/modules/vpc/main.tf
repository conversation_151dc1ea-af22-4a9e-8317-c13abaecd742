locals {
  vpc_name_prefix = "${var.environment}-${var.name}"
  # Calculate subnet size dynamically based on VPC size
  # We need 6 subnets, so we need at least 3 bits (2^3 = 8 subnets available)
  vpc_prefix_length    = tonumber(split("/", var.vpc_cidr_block)[1])
  subnet_prefix_length = local.vpc_prefix_length + 3
  bits_to_add          = 3

  # Calculate subnet CIDR blocks automatically
  public_subnet_cidrs = [
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 0),
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 1),
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 2)
  ]

  private_subnet_cidrs = [
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 3),
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 4),
    cidrsubnet(var.vpc_cidr_block, local.bits_to_add, 5)
  ]

  # Get first 3 available AZs
  availability_zones = slice(data.aws_availability_zones.available.names, 0, 3)

  # Common tags
  common_tags = merge(var.tags, {
    Name        = var.name
    Environment = var.environment
    Module      = "vpc"
  })
}

data "aws_availability_zones" "available" {
  state = "available"
  # This zone is old and doesn't work with cross-region Privatelink. Better to just not use it.
  exclude_zone_ids = ["use1-az3"]
}

resource "aws_vpc" "main" {
  cidr_block                           = var.vpc_cidr_block
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = true

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-vpc"
  })
}

resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-igw"
  })
}

resource "aws_subnet" "public" {
  count = 3

  vpc_id                  = aws_vpc.main.id
  cidr_block              = local.public_subnet_cidrs[count.index]
  availability_zone       = local.availability_zones[count.index]
  map_public_ip_on_launch = true

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-public-${local.availability_zones[count.index]}"
    Type = "public"
  })
}

resource "aws_subnet" "private" {
  count = 3

  vpc_id            = aws_vpc.main.id
  cidr_block        = local.private_subnet_cidrs[count.index]
  availability_zone = local.availability_zones[count.index]

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-private-${local.availability_zones[count.index]}"
    Type = "private"
  })
}

resource "aws_eip" "nat" {
  count = 3

  domain = "vpc"

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-nat-eip-${count.index + 1}"
  })

  depends_on = [aws_internet_gateway.main]
}

resource "aws_nat_gateway" "main" {
  count = 3

  allocation_id = aws_eip.nat[count.index].id
  subnet_id     = aws_subnet.public[count.index].id

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-nat-${count.index + 1}"
  })

  depends_on = [aws_internet_gateway.main]
}

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-public-rt"
  })
}

resource "aws_route_table" "private" {
  count = 3

  vpc_id = aws_vpc.main.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main[count.index].id
  }

  tags = merge(local.common_tags, {
    Name = "${local.vpc_name_prefix}-private-rt-${count.index + 1}"
  })
}

resource "aws_route_table_association" "public" {
  count = 3

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "private" {
  count = 3

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}
