import * as path from "path";
import * as yaml from "js-yaml";
import { Authors, Notebook, RegistryEntry } from "./notebook-schema";

export function notebookBasename(filename: string) {
  return path.basename(path.dirname(filename));
}

export function createGuide(
  notebook: Notebook,
  assetsRelPath: string,
  entry: RegistryEntry,
  authors: Authors,
) {
  const notebookAuthors = entry.authors.map((a) => authors[a]);

  // Remove suffix
  const language = notebook.metadata.language_info.name;
  const frontMatter = {
    title: entry.title,
    language,
    authors: notebookAuthors,
    date: entry.date,
    tags: entry.tags,
    logo: entry.logo,
    ...(entry.banner
      ? {
          image: entry.banner,
          twimage: entry.banner,
        }
      : {}),
  };

  const lines = [];
  lines.push("---");
  lines.push("# Generated file -- do not modify.");
  lines.push(yaml.dump(frontMatter).trimEnd());
  lines.push("---\n");
  lines.push(`import { Subheader } from "#/ui/docs/cookbook"\n`);

  lines.push("# " + entry.title + "\n");
  lines.push("");
  lines.push(`<Subheader
className="mt-2"
authors={${JSON.stringify(notebookAuthors)}}
githubUrl={${JSON.stringify(
    "https://github.com/braintrustdata/braintrust-cookbook/blob/main/" +
      entry.path,
  )}}
date={${entry.date ? JSON.stringify(entry.date) : "undefined"}}/>\n`);

  if (entry.banner) {
    lines.push(`![Banner](${entry.banner})\n`);
  }

  const assets: Record<string, Uint8Array> = {};

  for (const cell of notebook.cells) {
    if (cell.cell_type === "markdown") {
      lines.push(
        cell.source
          .join("")
          .replace(/(\/\.)?assets\//g, `../${assetsRelPath}\/`)
          .replace(new RegExp("<a.*?\n.*?Colab.*?\n.*?a>", "m"), "")
          .replace(new RegExp(/^#\s+.*/), ""),
      );
    } else if (cell.cell_type === "code") {
      lines.push("```" + language);
      lines.push(cell.source.join("").trimEnd());
      lines.push("```\n");

      if (cell.outputs.length > 0) {
        for (const output of cell.outputs) {
          if (output.output_type === "stream") {
            pushText(lines, output.text.join(""));
          } else if (
            output.output_type === "display_data" ||
            output.output_type === "execute_result"
          ) {
            const dataTypes = Object.keys(output.data).filter(
              (n) => !n.includes("application/vnd.jupyter.widget"),
            );
            console.assert(
              dataTypes.length === 1,
              `Multiple data types ${dataTypes}`,
            );
            const mimeType = dataTypes[0];
            const extension = mimeType.split("/")[1];
            const dataObj = output.data[mimeType];
            const data = Array.isArray(dataObj)
              ? dataObj.join("")
              : typeof dataObj === "string"
                ? dataObj
                : JSON.stringify(dataObj);

            if (mimeType.includes("text/")) {
              pushText(lines, data);
            } else {
              const assetName =
                `_generated_${cell.execution_count}` + "." + extension;
              assets[assetName] = Buffer.from(data, "base64");
              lines.push(
                `![Cell ${cell.execution_count}](../${assetsRelPath}/${assetName})\n<br />\n`,
              );
            }
          } else {
            console.assert(false, "Unhandled output", output);
          }
        }
      }
    }
  }
  return {
    text: lines.join("\n"),
    assets,
    language,
  };
}

function pushText(lines: string[], textInput: string) {
  lines.push("```");
  const text = textInput.trimEnd();

  // Remove ANSI escape codes
  const ansiRegex = /\u001b\[.*?m/g;
  const cleanText = text.replace(ansiRegex, "").replace(/\`/g, "\\`");

  lines.push(cleanText);
  lines.push("```\n");
}
