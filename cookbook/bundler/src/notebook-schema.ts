import { z } from "zod";
import registrySchema from "./registry-schema";
import authorsSchema from "./authors-schema";

export const markdownCellSchema = z.object({
  cell_type: z.literal("markdown"),
  metadata: z.record(z.unknown()),
  source: z.array(z.string()),
});

export const stdoutOutputSchema = z.object({
  name: z.string(),
  output_type: z.literal("stream"),
  text: z.array(z.string()),
});

export const displayDataSchema = z.object({
  data: z.record(
    z.string(),
    z.union([
      z.record(z.string(), z.unknown()),
      z.array(z.string()),
      z.string(),
    ]),
  ),
  metadata: z.record(z.unknown()),
  output_type: z.enum(["display_data", "execute_result"]),
});

export const outputSchema = z.union([stdoutOutputSchema, displayDataSchema]);

export const codeCellSchema = z.object({
  cell_type: z.literal("code"),
  execution_count: z.number().nullish(),
  metadata: z.record(z.unknown()),
  outputs: z.array(outputSchema),
  source: z.array(z.string()),
});

export const notebookSchema = z.object({
  cells: z.array(z.union([markdownCellSchema, codeCellSchema])),
  metadata: z.object({
    kernelspec: z
      .object({
        display_name: z.string(),
        language: z.string(),
        name: z.string(),
      })
      .optional(),
    language_info: z.object({
      codemirror_mode: z.object({
        mode: z.string().optional(),
        name: z.string(),
        typescript: z.boolean().optional(),
      }),
      file_extension: z.string(),
      mimetype: z.string(),
      name: z.string(),
      version: z.string(),
    }),
  }),
  nbformat: z.number(),
  nbformat_minor: z.number(),
});

export type Notebook = z.infer<typeof notebookSchema>;
export type RegistryEntry = z.infer<typeof registrySchema>[number];
export type Authors = z.infer<typeof authorsSchema>;
