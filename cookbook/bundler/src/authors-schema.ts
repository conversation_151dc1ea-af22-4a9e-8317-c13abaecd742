import { z } from "zod";

export default z
  .record(
    z.union([
      z
        .object({
          name: z.string(),
          website: z.string().url(),
          avatar: z.string(),
        })
        .strict(),
      z.never(),
    ]),
  )
  .superRefine((value, ctx) => {
    for (const key in value) {
      let evaluated = false;
      if (key.match(new RegExp("^.*$"))) {
        evaluated = true;
        const result = z
          .object({
            name: z.string(),
            website: z.string().url(),
            avatar: z.string(),
          })
          .strict()
          .safeParse(value[key]);
        if (!result.success) {
          ctx.addIssue({
            path: [...ctx.path, key],
            code: "custom",
            message: `Invalid input: Key matching regex /${key}/ must match schema`,
            params: {
              issues: result.error.issues,
            },
          });
        }
      }
      if (!evaluated) {
        const result = z.never().safeParse(value[key]);
        if (!result.success) {
          ctx.addIssue({
            path: [...ctx.path, key],
            code: "custom",
            message: `Invalid input: must match catchall schema`,
            params: {
              issues: result.error.issues,
            },
          });
        }
      }
    }
  });
