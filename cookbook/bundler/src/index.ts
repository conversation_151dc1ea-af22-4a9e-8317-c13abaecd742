#!/usr/bin/env node

import * as fs from "fs";
import * as yaml from "js-yaml";
import * as path from "path";
import * as chokidar from "chokidar";
import { execSync } from "child_process";
import { ArgumentParser } from "argparse";
import {
  Authors,
  Notebook,
  RegistryEntry,
  notebookSchema,
} from "./notebook-schema";
import { createGuide, notebookBasename } from "./notebook";
import registrySchema from "./registry-schema";
import authorsSchema from "./authors-schema";

export interface NotebookInfo {
  urlPath: string;
  language: string;
}

function processNotebook(
  notebookPath: string,
  entry: RegistryEntry,
  authors: Authors,
  outputDir: string,
): NotebookInfo {
  let notebook: Notebook;
  if (notebookPath.endsWith(".ipynb")) {
    const notebookData = JSON.parse(fs.readFileSync(notebookPath, "utf-8"));
    notebook = notebookSchema.parse(notebookData);
  } else {
    notebook = {
      cells: [
        {
          cell_type: "markdown",
          source: [fs.readFileSync(notebookPath, "utf-8")],
          metadata: {},
        },
      ],
      metadata: {
        language_info: {
          name: "typescript",
          codemirror_mode: {
            name: "typescript",
          },
          file_extension: ".ts",
          mimetype: "text/typescript",
          version: "1.0.0",
        },
      },
      nbformat: 4,
      nbformat_minor: 2,
    };
  }
  const assetsRelPath = path.join("assets", notebookBasename(notebookPath));
  const { text, assets, language } = createGuide(
    notebook,
    assetsRelPath,
    entry,
    authors,
  );

  const urlPath = notebookBasename(notebookPath);
  const guidePath = path.join(outputDir, "recipes", urlPath + ".mdx");

  // Copy the assets directory from the notebook directory to the output directory
  const notebookDirectory = path.dirname(notebookPath);
  const assetsDirectory = path.join(notebookDirectory, "assets");

  const outputAssetsDirectory = path.join(outputDir, assetsRelPath);
  execSync(`mkdir -p ${path.join(outputDir, "assets")}`);

  // cp -r assets outputDir
  if (fs.existsSync(outputAssetsDirectory)) {
    execSync(`rm -r ${outputAssetsDirectory}`);
  }
  if (fs.existsSync(assetsDirectory)) {
    execSync(`cp -r ${assetsDirectory} ${outputAssetsDirectory}`);
  }

  for (const [assetName, assetData] of Object.entries(assets)) {
    const assetPath = path.join(outputAssetsDirectory, assetName);
    fs.writeFileSync(assetPath, assetData);
  }

  fs.writeFileSync(guidePath, text);
  execSync(
    `/bin/bash -c 'cd ../.. && source env.sh && pre-commit run end-of-file-fixer --color never --files ${guidePath} || true > /dev/null'`,
  );
  execSync(
    `/bin/bash -c 'cd ../.. && source env.sh && pre-commit run prettier --color never --files ${guidePath} || true > /dev/null'`,
  );

  console.log(`-- Created ${guidePath}`);
  return {
    urlPath: notebookBasename(notebookPath),
    language,
  };
}

function createMetaJSON(
  entries: RegistryEntry[],
  authors: Authors,
  languages: Record<string, NotebookInfo>,
  outputDir: string,
) {
  const meta: unknown[] = [];

  for (const entry of entries) {
    meta.push({
      ...entry,
      ...languages[entry.path],
      authors: entry.authors.map((a) => authors[a]),
    });
  }

  const metaPath = path.join(outputDir, "recipes.json");
  fs.writeFileSync(metaPath, JSON.stringify(meta, null, 2) + "\n");
}

function reloadRegistry(registryPath: string) {
  const registry = registrySchema.parse(
    yaml.load(fs.readFileSync(registryPath, "utf-8"), {
      schema: yaml.CORE_SCHEMA, // This prevents datetimes from being parsed
    }),
  );

  const registryDirectory = path.dirname(registryPath);
  return Object.fromEntries(
    registry.map((entry) => [path.join(registryDirectory, entry.path), entry]),
  );
}

async function main() {
  const parser = new ArgumentParser({
    description: "Argparse example",
  });
  parser.add_argument("-r", "--registry", {
    help: "Cookbook registry",
  });
  parser.add_argument("-o", "--output", {
    help: "Output dir",
  });
  parser.add_argument("-y", "--watch", {
    action: "store_true",
    help: "Watch files for changes",
  });

  const scriptDir = path.dirname(fs.realpathSync(__filename));
  const contentDir = path.join(scriptDir, "..", "..", "content");

  const args = parser.parse_args();

  const registryPath = path.resolve(
    args.registry ?? path.join(contentDir, "registry.yaml"),
  );
  const registryDirectory = path.dirname(registryPath);
  const authorsPath = path.join(registryDirectory, "authors.yaml");

  const outputDir = path.resolve(
    args.output ??
      path.join(
        scriptDir,
        "..",
        "..",
        "..",
        "app",
        "content",
        "docs",
        "cookbook",
      ),
  );

  let authors = authorsSchema.parse(
    yaml.load(fs.readFileSync(authorsPath, "utf-8"), {
      schema: yaml.CORE_SCHEMA, // This prevents datetimes from being parsed
    }),
  );

  let allNotebooks = reloadRegistry(registryPath);

  if (args.watch) {
    const watcher = chokidar.watch(Object.keys(allNotebooks), {
      persistent: true,
    });

    watcher.on("add", (path) => {
      console.log(`-- File added: ${path}`);
      const notebookInfo = processNotebook(
        path,
        allNotebooks[path],
        authors,
        outputDir,
      );
      createMetaJSON(
        Object.values(allNotebooks),
        authors,
        { [allNotebooks[path].path]: notebookInfo },
        outputDir,
      );
    });

    watcher.on("change", (path) => {
      console.log(`-- File changed: ${path}`);
      const notebookInfo = processNotebook(
        path,
        allNotebooks[path],
        authors,
        outputDir,
      );
      createMetaJSON(
        Object.values(allNotebooks),
        authors,
        { [allNotebooks[path].path]: notebookInfo },
        outputDir,
      );
    });

    const registryWatcher = chokidar.watch([registryPath, authorsPath], {
      persistent: true,
    });

    registryWatcher.on("change", (path) => {
      console.log(`-- File changed: ${path}`);
      allNotebooks = reloadRegistry(registryPath);
      authors = authorsSchema.parse(
        yaml.load(fs.readFileSync(authorsPath, "utf-8"), {
          schema: yaml.CORE_SCHEMA, // This prevents datetimes from being parsed
        }),
      );
      const allInfo: Record<string, NotebookInfo> = {};
      for (const [file, entry] of Object.entries(allNotebooks)) {
        const info = processNotebook(file, entry, authors, outputDir);
        allInfo[entry.path] = info;
      }
      createMetaJSON(Object.values(allNotebooks), authors, allInfo, outputDir);
    });
  } else {
    const allInfo: Record<string, NotebookInfo> = {};
    for (const [file, entry] of Object.entries(allNotebooks)) {
      const info = processNotebook(file, entry, authors, outputDir);
      allInfo[entry.path] = info;
    }
    createMetaJSON(Object.values(allNotebooks), authors, allInfo, outputDir);
  }
}

main();
