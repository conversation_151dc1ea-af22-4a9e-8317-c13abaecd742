{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "path": {"type": "string"}, "redirects": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}, "authors": {"type": "array", "items": {"type": "string"}}, "date": {"type": "string", "format": "date"}, "logo": {"type": "string", "format": "uri"}, "banner": {"type": "string"}}, "required": ["title", "path", "tags", "authors"], "additionalProperties": false}}