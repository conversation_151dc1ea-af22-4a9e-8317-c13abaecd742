repos:
  - repo: "https://github.com/pre-commit/pre-commit-hooks"
    rev: v4.4.0
    hooks:
      - id: end-of-file-fixer
        exclude: queryscript/tests/.*\.(expected|test)$
      - id: trailing-whitespace
        exclude: queryscript/tests/.*\.(expected|test)$
  - repo: "https://github.com/psf/black"
    rev: 22.6.0
    hooks:
      - id: black
        files: ./
        args: [--config=./pyproject.toml]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.1.7
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        exclude: ^(extension/|.*\.json$|pnpm-lock.yaml)
