{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:tailwindcss/recommended", "prettier"], "plugins": ["@typescript-eslint"], "rules": {"@next/next/no-img-element": "off", "@typescript-eslint/no-unused-vars": ["error", {"vars": "all", "args": "none", "ignoreRestSiblings": false, "argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "prefer-const": "error", "tailwindcss/no-custom-classname": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-var-requires": "off"}}