---
title: "Guides"
---

import { Card, Cards } from "fumadocs-ui/components/card";

# Guides

Guides are step-by-step walkthroughs to help you accomplish a specific goal in
Braintrust.

## Core functionality

<Cards>
  <Card
    title="Evals"
    description="Test AI features and changes before shipping"
    href="/docs/guides/evals"
  />
  <Card
    title="Logs"
    description="Instrument your code to monitor its performance"
    href="/docs/guides/logging"
  />
</Cards>

## Features

<Cards>
  <Card
    title="Datasets"
    description="Manage test cases and use Log data for Evals"
    href="/docs/guides/datasets"
  />
  <Card
    title="Prompts"
    description="Manage and version your prompts and sync them between your live code, Playgrounds, and Evals"
    href="/docs/guides/prompts"
  />
  <Card
    title="Playground"
    description="An IDE for AI: Make changes and see results quickly"
    href="/docs/guides/playground"
  />
  <Card
    title="Human Review"
    description="Allow humans to grade AI outputs inside Braintrust"
    href="/docs/guides/human-review"
  />
  <Card
    title="Proxy"
    description="Make it easier to work with API calls to AI"
    href="/docs/guides/proxy"
  />
</Cards>

## Advanced usecases

<Cards>
  <Card
    title="Tracing"
    description="Control exactly what gets logged in Logs and Evals"
    href="/docs/guides/tracing"
  />
  <Card
    title="Self-Hosting"
    description="Run Braintrust on-prem"
    href="/docs/guides/self-hosting"
  />
</Cards>
