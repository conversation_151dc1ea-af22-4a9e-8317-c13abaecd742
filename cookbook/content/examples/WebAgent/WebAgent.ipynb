{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating a multimodal web navigation agent\n", "\n", "Web navigation can be tricky for AI agents because they need to understand webpage layouts, visual elements, and remember previous steps to take the right actions. This cookbook focuses on how models decide what to do next, like clicking buttons, entering text, or choosing dropdown options.\n", "\n", "We'll use the [Multimodal-Mind2Web dataset](https://osu-nlp-group.github.io/Mind2Web/), which combines screenshots and HTML, to help models make better decisions. We'll also discuss how to apply these lessons beyond just this dataset. By the end, you'll have a clear framework for testing how well your AI navigates websites and finding ways to improve it."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started\n", "\n", "To follow along, start by installing the required packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install lxml openai datasets pillow braintrust autoevals"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, make sure you have a [Braintrust](https://www.braintrust.dev/signup) account, along with an [OpenAI API key](https://platform.openai.com/). To authenticate with Braintrust, export your `BRAINTRUST_API_KEY` as an environment variable:\n", "\n", "```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>\n", "\n", "We'll import our modules and initialize the OpenAI client using the Braintrust proxy:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import base64\n", "import re\n", "import time\n", "from typing import Dict, Any, List, Optional, Tuple\n", "\n", "from lxml import etree\n", "import openai\n", "from datasets import load_dataset\n", "from PIL import Image\n", "from io import BytesIO\n", "\n", "from braintrust import (\n", "    <PERSON><PERSON>,\n", "    Attachment,\n", "    start_span,\n", "    wrap_openai,\n", ")\n", "\n", "# Constants\n", "MAX_SAMPLES = 50\n", "HTML_MAX_ELEMENTS = 50\n", "MAX_PREVIOUS_ACTIONS = 3\n", "\n", "# Uncomment the following line to hardcode your API key\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_API_KEY_HERE\"\n", "\n", "client = wrap_openai(\n", "    openai.OpenAI(\n", "        api_key=os.environ.get(\"BRAINTRUST_API_KEY\"),\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approaches to web navigation\n", "\n", "There are a few ways AI models can navigate websites:\n", "* HTML-only: Uses page structure but misses visual details.\n", "* Screenshot-only: Captures visuals but misses interaction details.\n", "* Multimodal: Combines HTML structure and screenshots for better decisions.\n", "\n", "In this cookbook, we'll use the multimodal approach, combining HTML DOM structure and screenshots."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Processing screenshots\n", "\n", "First, let's write a function that converts screenshots of a given webpage into a format that we can use to pass to our model and [attach to our eval](/docs/guides/evals/write#attachments)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def process_screenshot(screenshot_input: Any) -> Optional[Attachment]:\n", "    with start_span(name=\"process_screenshot\") as span:\n", "        try:\n", "            # Handle PIL Image\n", "            if isinstance(screenshot_input, Image.Image):\n", "                img_byte_arr = BytesIO()\n", "                screenshot_input.save(img_byte_arr, format=\"PNG\")\n", "                image_data = img_byte_arr.getvalue()\n", "\n", "            # Handle file path\n", "            elif isinstance(screenshot_input, str) and os.path.exists(screenshot_input):\n", "                with open(screenshot_input, \"rb\") as f:\n", "                    image_data = f.read()\n", "\n", "            # Handle bytes\n", "            elif isinstance(screenshot_input, bytes):\n", "                image_data = screenshot_input\n", "\n", "            # Handle dictionary with base64 data\n", "            elif isinstance(screenshot_input, dict) and \"data\" in screenshot_input:\n", "                data = screenshot_input[\"data\"]\n", "                if not isinstance(data, str):\n", "                    return None\n", "\n", "                # Process base64 data\n", "                if data.startswith(\"data:image\"):\n", "                    base64_data = data.split(\",\", 1)[1]\n", "                elif data.startswith(\"/9j/\") or data.startswith(\"iVBOR\"):\n", "                    base64_data = data\n", "                else:\n", "                    return None\n", "\n", "                image_data = base64.b64decode(base64_data)\n", "            else:\n", "                return None\n", "\n", "            # Create attachment\n", "            result = Attachment(\n", "                data=image_data,\n", "                filename=\"screenshot.png\",\n", "                content_type=\"image/png\",\n", "            )\n", "\n", "            return result\n", "\n", "        except Exception:\n", "            return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we'll identify and summarize important HTML elements on the webpage, making it easier for the model to quickly understand page structure:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def get_enhanced_tree_summary(\n", "    html_content: str, max_items: int = HTML_MAX_ELEMENTS\n", ") -> str:\n", "    with start_span(name=\"html_parsing\") as span:\n", "        if not html_content:\n", "            return \"No HTML content provided\"\n", "\n", "        try:\n", "            # Parse HTML\n", "            parser = etree.HTMLParser()\n", "            dom_tree = etree.fromstring(html_content, parser)\n", "\n", "            # XPath for interactive elements, sorted by relevance\n", "            xpath_queries = [\n", "                \"//button | //input[@type='submit'] | //input[@type='button']\",\n", "                \"//a[@href] | //*[@role='button'] | //*[@onclick]\",\n", "                \"//input[not(@type='hidden')] | //select | //textarea\",\n", "                \"//label | //form\",\n", "                \"//h1 | //h2 | //h3 | //nav | //*[@role='navigation']\",\n", "            ]\n", "\n", "            # Collect elements by priority until max_items is reached\n", "            important_elements = []\n", "            for query in xpath_queries:\n", "                if len(important_elements) >= max_items:\n", "                    break\n", "                elements = dom_tree.xpath(query)\n", "                remaining_slots = max_items - len(important_elements)\n", "                important_elements.extend(elements[:remaining_slots])\n", "\n", "            # Create a concise representation\n", "            summary = []\n", "            for elem in important_elements:\n", "                tag = elem.tag\n", "\n", "                # Get text content, limited to 30 chars\n", "                text = elem.text.strip() if elem.text else \"\"\n", "                if not text:\n", "                    for child in elem.xpath(\".//text()\"):\n", "                        if child.strip():\n", "                            text += \" \" + child.strip()\n", "                text = text.strip()[:30]\n", "\n", "                # Get key attributes\n", "                key_attrs = [\n", "                    \"id\",\n", "                    \"type\",\n", "                    \"placeholder\",\n", "                    \"href\",\n", "                    \"role\",\n", "                    \"aria-label\",\n", "                    \"value\",\n", "                    \"name\",\n", "                ]\n", "                attrs = []\n", "                for k in key_attrs:\n", "                    if k in elem.attrib:\n", "                        attrs.append(f'{k}=\"{elem.attrib[k]}\"')\n", "\n", "                # Format element representation\n", "                elem_repr = f\"<{tag} {' '.join(attrs)}>{text}</{tag}>\"\n", "                summary.append(elem_repr)\n", "\n", "            return \"\\n\".join(summary)\n", "\n", "        except Exception as e:\n", "            return f\"Error parsing HTML: {str(e)}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Keeping track of actions\n", "\n", "Models perform better if they have context from previous steps. Without historical context, an agent might repeat actions or select incorrect next steps. \n", "\n", "This function takes the latest few actions (up to `MAX_PREVIOUS_ACTIONS`) and neatly formats them for easy reference:"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def format_previous_actions(\n", "    actions: List[str], max_actions: int = MAX_PREVIOUS_ACTIONS\n", ") -> str:\n", "    if not actions:\n", "        return \"None\"\n", "\n", "    # Only take the most recent actions\n", "    recent_actions = actions[-max_actions:]\n", "\n", "    # Format with numbering\n", "    formatted = \"\\n\".join(\n", "        [f\"{i+1}. {action}\" for i, action in enumerate(recent_actions)]\n", "    )\n", "\n", "    # Indicate if there were more actions before these\n", "    if len(actions) > max_actions:\n", "        formatted = (\n", "            f\"Showing {max_actions} most recent of {len(actions)} total actions\\n\"\n", "            + formatted\n", "        )\n", "\n", "    return formatted"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We also need a reliable way to convert raw action descriptions from our dataset into structured data our program can use. This function parses a provided action description and figures out the action type (`CLICK`, `TYPE`, or `SELECT`), and any associated values (like text typed):\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def parse_operation_string(operation_str: str) -> Dict[str, str]:\n", "    with start_span(name=\"parse_operation\") as span:\n", "        # Default values\n", "        operation = {\"op\": \"CLICK\", \"value\": \"\"}\n", "\n", "        if not operation_str:\n", "            return operation\n", "\n", "        try:\n", "            # Try parsing as <PERSON><PERSON><PERSON> first\n", "            if operation_str.strip().startswith(\"{\"):\n", "                parsed = json.loads(operation_str)\n", "                if isinstance(parsed, dict):\n", "                    operation[\"op\"] = parsed.get(\"op\", \"CLICK\")\n", "                    operation[\"value\"] = parsed.get(\"value\", \"\")\n", "            else:\n", "                # Fallback to regex parsing\n", "                import re\n", "\n", "                match_op = re.search(r\"(CLICK|TYPE|SELECT)\", operation_str)\n", "                if match_op:\n", "                    operation[\"op\"] = match_op.group(1)\n", "                    match_value = re.search(\n", "                        r'value\\s*[:=]\\s*[\"\\']?([^\"\\']+)[\"\\']?', operation_str\n", "                    )\n", "                    if match_value:\n", "                        operation[\"value\"] = match_value.group(1)\n", "        except Exception:\n", "            pass\n", "\n", "        return operation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading and preparing the dataset\n", "\n", "Now that we've set up our helper functions, we can we load and process samples from the [Multimodal-Mind2Web dataset](https://huggingface.co/datasets/osunlp/Multimodal-Mind2Web):"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def load_mind2web_samples(\n", "    max_samples: int = MAX_SAMPLES, use_smaller_subset: bool = True\n", ") -> List[Dict[str, Any]]:\n", "\n", "    # Load the dataset with streaming to conserve memory\n", "    split = \"test_domain\" if use_smaller_subset else \"train\"\n", "    dataset = load_dataset(\"osunlp/Multimodal-Mind2Web\", split=split, streaming=True)\n", "\n", "    processed_samples = []\n", "    successful_samples = 0\n", "\n", "    # Process samples\n", "    for item in dataset:\n", "        if successful_samples >= max_samples:\n", "            break\n", "\n", "        try:\n", "            with start_span(name=\"process_sample\") as sample_span:\n", "                # Extract basic fields\n", "                annotation_id = item.get(\n", "                    \"annotation_id\", f\"sample_{successful_samples}\"\n", "                )\n", "                website = item.get(\"website\", \"unknown\")\n", "                confirmed_task = item.get(\"confirmed_task\", \"Navigate the website\")\n", "                cleaned_html = item.get(\"cleaned_html\", \"<html></html>\")\n", "                operation_str = item.get(\"operation\", '{\"op\": \"CLICK\", \"value\": \"\"}')\n", "\n", "                # Process operation\n", "                operation = parse_operation_string(operation_str)\n", "\n", "                # Process screenshot\n", "                screenshot_attachment = None\n", "                screenshot_dict = item.get(\"screenshot\")\n", "                if screenshot_dict:\n", "                    screenshot_attachment = process_screenshot(screenshot_dict)\n", "\n", "                # Process HTML summary\n", "                html_summary = get_enhanced_tree_summary(\n", "                    cleaned_html, max_items=HTML_MAX_ELEMENTS\n", "                )\n", "\n", "                # Process previous actions\n", "                action_reprs = item.get(\"action_reprs\", [])\n", "                previous_actions_str = format_previous_actions(\n", "                    action_reprs, max_actions=MAX_PREVIOUS_ACTIONS\n", "                )\n", "\n", "                # Map operation type to the correct option letter\n", "                expected_option = \"A\"  # Default to CLICK\n", "                if operation[\"op\"] == \"TYPE\":\n", "                    expected_option = \"B\"\n", "                elif operation[\"op\"] == \"SELECT\":\n", "                    expected_option = \"C\"\n", "\n", "                # Create a focused prompt\n", "                formatted_prompt = f\"\"\"\n", "                    Task: {confirmed_task}\n", "\n", "                    Key webpage elements:\n", "                    {html_summary}\n", "\n", "                    Previous actions:\n", "                    {previous_actions_str}\n", "\n", "                    What should be the next action? Select from:\n", "                    <PERSON><PERSON> <PERSON>lick the appropriate element based on the task\n", "                    B. Type text into an input field\n", "                    C. Select an option from a dropdown\n", "                    \"\"\"\n", "\n", "                # Build complete sample\n", "                sample = {\n", "                    \"annotation_id\": annotation_id,\n", "                    \"website\": website,\n", "                    \"confirmed_task\": confirmed_task,\n", "                    \"html_summary\": html_summary,\n", "                    \"operation\": operation,\n", "                    \"previous_actions_str\": previous_actions_str,\n", "                    \"formatted_prompt\": formatted_prompt,\n", "                    \"expected_option\": expected_option,\n", "                    \"expected_action\": operation[\"op\"],\n", "                    \"expected_value\": operation[\"value\"],\n", "                    \"screenshot_attachment\": screenshot_attachment,\n", "                }\n", "\n", "                processed_samples.append(sample)\n", "                successful_samples += 1\n", "\n", "        except Exception:\n", "            continue\n", "\n", "    return processed_samples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll transform these samples to a format that your model can easily use during evaluation. This function creates structured samples clearly separating inputs (task, screenshot) from expected actions for comparison during evaluation:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def create_braintrust_dataset(samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:\n", "\n", "    dataset_samples = []\n", "\n", "    for sample in samples:\n", "        if not isinstance(sample, dict):\n", "            continue\n", "\n", "        # Extract operation details\n", "        operation = sample.get(\"operation\", {})\n", "        operation_type = (\n", "            operation.get(\"op\", \"CLICK\") if isinstance(operation, dict) else \"CLICK\"\n", "        )\n", "        operation_value = (\n", "            operation.get(\"value\", \"\") if isinstance(operation, dict) else \"\"\n", "        )\n", "\n", "        # Create dataset entry\n", "        dataset_entry = {\n", "            \"input\": {\n", "                \"prompt\": sample.get(\"formatted_prompt\", \"\"),\n", "                \"task\": sample.get(\"confirmed_task\", \"\"),\n", "                \"website\": sample.get(\"website\", \"\"),\n", "                \"previous_actions\": sample.get(\"previous_actions_str\", \"None\"),\n", "            },\n", "            \"expected\": {\n", "                \"option\": sample.get(\"expected_option\", \"\"),\n", "                \"action\": operation_type,\n", "                \"value\": operation_value,\n", "            },\n", "            \"metadata\": {\n", "                \"annotation_id\": sample.get(\"annotation_id\", \"\"),\n", "                \"website\": sample.get(\"website\", \"\"),\n", "                \"operation_type\": operation_type,\n", "            },\n", "        }\n", "\n", "        # Add screenshot attachment if available\n", "        if sample.get(\"screenshot_attachment\"):\n", "            dataset_entry[\"input\"][\"screenshot\"] = sample[\"screenshot_attachment\"]\n", "\n", "        dataset_samples.append(dataset_entry)\n", "\n", "    return dataset_samples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building the prediction function\n", "\n", "Next, we'll build the prediction function that will send each formatted input to the model (`gpt-4o`) and retrieve the predicted action:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def predict_with_gpt4o(input_data: Dict[str, Any]) -> Dict[str, Any]:\n", "    with start_span(name=\"model_prediction\") as predict_span:\n", "        try:\n", "            # Extract input components\n", "            prompt = input_data.get(\"prompt\", \"\")\n", "            screenshot_attachment = input_data.get(\"screenshot\")\n", "\n", "            # Create system message requesting JSON output\n", "            system_message = \"\"\"You are a web navigation assistant that helps users complete tasks online.\n", "                Analyze the webpage and determine the best action to take next based on the task.\n", "\n", "                You MUST respond with a valid JSON object with the following structure:\n", "                {\n", "                \"option\": \"A, B, or C\",\n", "                \"op\": \"CLICK, TYPE, or SELECT\",\n", "                \"value\": \"Only provide value for TYPE/SELECT actions\"\n", "                }\n", "\n", "                Option A corresponds to CLICK, B to TYPE, and C to SELECT.\n", "                For CLICK operations, include an empty value field.\n", "\n", "                Example for clicking:\n", "                {\"option\": \"A\", \"op\": \"CLICK\", \"value\": \"\"}\n", "\n", "                Example for typing:\n", "                {\"option\": \"B\", \"op\": \"TYPE\", \"value\": \"search query text\"}\n", "\n", "                Example for selecting:\n", "                {\"option\": \"C\", \"op\": \"SELECT\", \"value\": \"dropdown option\"}\n", "                \"\"\"\n", "\n", "            # Create messages array\n", "            messages = [{\"role\": \"system\", \"content\": system_message}]\n", "\n", "            # Add screenshot if available\n", "            if screenshot_attachment and hasattr(screenshot_attachment, \"data\"):\n", "                try:\n", "                    image_data = screenshot_attachment.data\n", "                    base64_image = base64.b64encode(image_data).decode(\"utf-8\")\n", "\n", "                    messages.append(\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": [\n", "                                {\n", "                                    \"type\": \"image_url\",\n", "                                    \"image_url\": {\n", "                                        \"url\": f\"data:image/png;base64,{base64_image}\"\n", "                                    },\n", "                                },\n", "                                {\"type\": \"text\", \"text\": prompt},\n", "                            ],\n", "                        }\n", "                    )\n", "                except Exception:\n", "                    messages.append({\"role\": \"user\", \"content\": prompt})\n", "            else:\n", "                messages.append({\"role\": \"user\", \"content\": prompt})\n", "\n", "            # Request JSON output format\n", "            response = client.chat.completions.create(\n", "                model=\"gpt-4o\",\n", "                messages=messages,\n", "                max_tokens=150,\n", "                temperature=0.2,\n", "                response_format={\"type\": \"json_object\"},  # This is critical!\n", "            )\n", "\n", "            result = response.choices[0].message.content\n", "\n", "            # Parse JSON response\n", "            try:\n", "                structured_response = json.loads(result)\n", "\n", "                # Ensure the required fields exist\n", "                if \"option\" not in structured_response:\n", "                    structured_response[\"option\"] = \"\"\n", "                if \"op\" not in structured_response:\n", "                    structured_response[\"op\"] = \"\"\n", "                if \"value\" not in structured_response:\n", "                    structured_response[\"value\"] = \"\"\n", "\n", "                return structured_response\n", "\n", "            except json.JSONDecodeError as e:\n", "                # If JSON parsing fails, try to extract data from text\n", "                option_match = re.search(r\"Answer:\\s*([ABC])\", result, re.IGNORECASE)\n", "                action_match = re.search(\n", "                    r\"Action:\\s*(CLICK|TYPE|SELECT)\", result, re.IGNORECASE\n", "                )\n", "                value_match = re.search(r\"Value:\\s*(.+?)(?:\\n|$)\", result)\n", "\n", "                option = option_match.group(1).upper() if option_match else \"\"\n", "                action = action_match.group(1).upper() if action_match else \"\"\n", "                value = value_match.group(1).strip() if value_match else \"\"\n", "\n", "                # Convert to structured format\n", "                return {\n", "                    \"option\": option,\n", "                    \"op\": action,\n", "                    \"value\": value,\n", "                    \"error\": f\"JSON parsing failed: {str(e)}\",\n", "                }\n", "\n", "        except Exception as e:\n", "            # Return error information in JSON format\n", "            return {\"option\": \"\", \"op\": \"ERROR\", \"value\": str(e), \"error\": str(e)}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining our scorers\n", "\n", "To evaluate how accurate the predictions are against the ground truth, we'll use two different scoring metrics. For web navigation tasks, we need metrics that can pinpoint specific strengths and weaknesses in our agent. We'll create two simple code-based scorers. \n", "\n", "The first scorer checks if the predicted action matches the expected action type:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def option_selection_scorer(output: Dict[str, str], expected: Dict[str, Any]) -> int:\n", "    return int(output[\"op\"] == expected[\"action\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The second evaluates whether the details of the action were correct:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def action_correctness_scorer(output: Dict[str, str], expected: Dict[str, Any]) -> int:\n", "    # First, check if both action types match (note output uses \"op\" key)\n", "    action_matches = output[\"op\"] == expected[\"action\"]\n", "\n", "    # If the actions don't match, return 0 immediately\n", "    if not action_matches:\n", "        return 0\n", "\n", "    # If we're dealing with a CLICK action, we've already confirmed they match\n", "    if expected[\"action\"] == \"CLICK\":\n", "        return 1\n", "\n", "    # For TYPE or SELECT, check if values match too\n", "    return int(output[\"value\"] == expected[\"value\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Using two different scorers will help us identify whether errors come from misunderstanding the task context or from incorrectly formulating the action details."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the evaluation\n", "\n", "Now that we've set up the task, dataset, and evaluation criteria, we're ready to run our evaluation. This function will load and process each dataset sample, generate predictions, and assess how accurately the model identifies the correct action type and associated details. All results will be captured in Braintrust, allowing us to analyze performance and pinpoint areas for improvement."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_mind2web_evaluation(sample_size: int = MAX_SAMPLES) -> None:\n", "    try:\n", "        # Load samples\n", "        samples = load_mind2web_samples(max_samples=sample_size)\n", "\n", "        if not samples:\n", "            return\n", "\n", "        # Create Braintrust dataset\n", "        dataset = create_braintrust_dataset(samples)\n", "\n", "        # Run the evaluation\n", "        experiment_name = f\"mind2web-{int(time.time())}\"\n", "        Eval(\n", "            \"multimodal-mind2web-eval\",  # Project name\n", "            data=dataset,\n", "            task=predict_with_gpt4o,\n", "            scores=[option_selection_scorer, action_correctness_scorer],\n", "            experiment_name=experiment_name,\n", "            metadata={\n", "                \"model\": \"gpt-4o\",\n", "            },\n", "        )\n", "\n", "    except Exception as e:\n", "        print(f\"Evaluation failed: {e}\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # Run evaluation with a smaller sample size for testing. Adjust this number to run on more or less samples.\n", "    run_mind2web_evaluation(sample_size=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing the results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Web agents have many configuration options that can impact their performance. In Braintrust, you can dig deeper into each trace to see each step the agent takes, including attachments and intermediate processing steps. This makes it easier to identify issues, debug quickly, and iterate.\n", "\n", "![attachment](./assets/trace.png)\n", "\n", "Performance can also vary depending on context. For example, your agent might perform well on some websites but struggle with others, or handle certain action types better. In Braintrust, you can group and filter evaluation results by metadata, helping you quickly pinpoint patterns and identify areas for improvement.\n", "\n", "![grouping](./assets/grouping.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Learning from the data\n", "\n", "Taking the time to analyze your results in Braintrust will help you discover clear opportunities to improve your agent. For example, you might find that certain HTML preprocessing techniques perform better on form-intensive websites, or that providing more detailed historical context improves accuracy on complex tasks. By tracing each action, filtering results, and comparing different approaches systematically, you can make targeted improvements instead of relying on guesswork."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that you've explored how to evaluate the decision making ability of a web agent, you can:\n", "\n", "- Learn more about [how to evaluate agents](/blog/evaluating-agents)\n", "- Check out the [guide to what you should do after running an eval](/blog/after-evals)\n", "- Try out another [agent cookbook](/docs/cookbook/recipes/PromptChaining)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}