{"cells": [{"cell_type": "markdown", "metadata": {"id": "quNDJhiCJ-AI"}, "source": ["# LLM Eval For Text2SQL\n", "\n", "In this cookbook, we're going to work through a Text2SQL use case where we are starting from scratch without a nice and clean\n", "dataset of questions, SQL queries, or expected responses. Although eval datasets are popular in academic settings, they are often\n", "not practically available in the real world. In this case, we'll build up a dataset using some simple handwritten questions and\n", "an LLM to generate samples based on the SQL dataset.\n", "\n", "Along the way, we'll cover the following components of the eval process:\n", "\n", "![eval framework](./assets/eval_framework.jpeg)\n", "\n", "Before starting, please make sure that you have a Braintrust account. If you do not, please [sign up](https://braintrust.dev).\n"]}, {"cell_type": "markdown", "metadata": {"id": "2tMYg0jyKelb"}, "source": ["## Setting up the environment\n", "\n", "The next few commands will install some libraries and include some helper code for the text2sql application. Feel free to copy/paste/tweak/reuse this code in your own tools.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pGuPB2SqUWdZ"}, "outputs": [], "source": ["%pip install -U autoevals braintrust duckdb datasets openai pyarrow pydantic --quiet"]}, {"cell_type": "markdown", "metadata": {"id": "fajc2XybK_gt"}, "source": ["### Downloading the data\n", "\n", "We're going to use an NBA dataset that includes information about games from 2014-2018. Let's start by downloading it and poking around.\n", "\n", "We'll use [DuckDB](https://duckdb.org/) as the database, since it's easy to embed directly in the notebook.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Unnamed: 0': 1,\n", " 'Team': 'ATL',\n", " 'Game': 1,\n", " 'Date': '10/29/14',\n", " 'Home': 'Away',\n", " 'Opponent': 'TOR',\n", " 'WINorLOSS': 'L',\n", " 'TeamPoints': 102,\n", " 'OpponentPoints': 109,\n", " 'FieldGoals': 40,\n", " 'FieldGoalsAttempted': 80,\n", " 'FieldGoals.': 0.5,\n", " 'X3PointShots': 13,\n", " 'X3PointShotsAttempted': 22,\n", " 'X3PointShots.': 0.591,\n", " 'FreeThrows': 9,\n", " 'FreeThrowsAttempted': 17,\n", " 'FreeThrows.': 0.529,\n", " 'OffRebounds': 10,\n", " 'TotalRebounds': 42,\n", " 'Assists': 26,\n", " 'Steals': 6,\n", " 'Blocks': 8,\n", " 'Turnovers': 17,\n", " 'TotalFouls': 24,\n", " 'Opp.FieldGoals': 37,\n", " 'Opp.FieldGoalsAttempted': 90,\n", " 'Opp.FieldGoals.': 0.411,\n", " 'Opp.3PointShots': 8,\n", " 'Opp.3PointShotsAttempted': 26,\n", " 'Opp.3PointShots.': 0.308,\n", " 'Opp.FreeThrows': 27,\n", " 'Opp.FreeThrowsAttempted': 33,\n", " 'Opp.FreeThrows.': 0.818,\n", " 'Opp.OffRebounds': 16,\n", " 'Opp.TotalRebounds': 48,\n", " 'Op<PERSON><PERSON>Assists': 26,\n", " '<PERSON><PERSON><PERSON>': 13,\n", " 'Opp.Blocks': 9,\n", " 'Opp.Turnovers': 9,\n", " 'Opp.TotalFouls': 22}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import duckdb\n", "from datasets import load_dataset\n", "\n", "data = load_dataset(\"suzyanil/nba-data\")[\"train\"]\n", "\n", "conn = duckdb.connect(database=\":memory:\", read_only=False)\n", "conn.register(\"nba\", data.to_pandas())\n", "\n", "conn.query(\"SELECT * FROM nba LIMIT 5\").to_df().to_dict(orient=\"records\")[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prototyping text2sql\n", "\n", "Now that we have the basic data in place, let's implement the text2sql logic. Don't overcomplicate it at the start. We can always improve its implementation later!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT Team, COUNT(*) AS Wins\n", "FROM nba\n", "WHERE WINorLOSS = 'W'\n", "GROUP BY Team\n", "ORDER BY Wins DESC\n", "LIMIT 1;\n"]}], "source": ["import os\n", "from textwrap import dedent\n", "\n", "import braintrust\n", "import openai\n", "\n", "client = braintrust.wrap_openai(\n", "    openai.AsyncClient(\n", "        api_key=os.environ[\"OPENAI_API_KEY\"],\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",  # This is optional and allows us to cache responses\n", "    )\n", ")\n", "\n", "columns = conn.query(\"DESCRIBE nba\").to_df().to_dict(orient=\"records\")\n", "\n", "TASK_MODEL = \"gpt-4o\"\n", "\n", "\n", "@braintrust.traced\n", "async def generate_query(input):\n", "    response = await client.chat.completions.create(\n", "        model=TASK_MODEL,\n", "        temperature=0,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": dedent(\n", "                    f\"\"\"\\\n", "        You are a SQL expert, and you are given a single table named nba with the following columns:\n", "        {\", \".join(column[\"column_name\"] + \": \" + column[\"column_type\"] for column in columns)}\n", "\n", "        Write a SQL query corresponding to the user's request. Return just the query text, with no\n", "        formatting (backticks, markdown, etc.).\n", "\"\"\"\n", "                ),\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": input,\n", "            },\n", "        ],\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "\n", "query = await generate_query(\"Who won the most games?\")\n", "print(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome, let's try running the query!\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Team': 'GSW', 'Wins': 265}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["def execute_query(query):\n", "    return conn.query(query).fetchdf().to_dict(orient=\"records\")\n", "\n", "\n", "execute_query(query)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initial evals\n", "\n", "An `Eval()` consists of three parts — data, task, and scores. We'll start with **data**.\n", "\n", "### Creating an initial dataset\n", "\n", "Let's handwrite a few examples to bootstrap the dataset. It'll be a real pain, and probably brittle, to try and handwrite both questions and SQL queries/outputs. Instead,\n", "we'll just write some questions, and try to evaluate the outputs _without an expected output_.\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["questions = [\n", "    \"Which team won the most games?\",\n", "    \"Which team won the most games in 2015?\",\n", "    \"Who led the league in 3 point shots?\",\n", "    \"Which team had the biggest difference in records across two consecutive years?\",\n", "    \"What is the average number of free throws per year?\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task function\n", "\n", "Now let's write a task function. The function should take input (the question) and return output (the SQL query and results).\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["@braintrust.traced\n", "async def text2sql(question):\n", "    query = await generate_query(question)\n", "    results = None\n", "    error = None\n", "    try:\n", "        results = execute_query(query)\n", "    except duckdb.E<PERSON>r as e:\n", "        error = str(e)\n", "\n", "    return {\n", "        \"query\": query,\n", "        \"results\": results,\n", "        \"error\": error,\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scores\n", "\n", "At this point, there's not a lot we can score, but we can at least check if the SQL query is valid. If we generate an invalid query, the `error` field will be non-empty.\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["async def no_error(output):\n", "    return output[\"error\"] is None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Eval\n", "\n", "And that's it! Now let's plug these things together and run an eval.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Initial dataset is running at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Initial%20dataset\n", "LLM Eval for Text2SQL [experiment_name=Initial dataset] (data): 5it [00:00, 33078.11it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e4a25917a77401dbba65bffaa1161c1", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM Eval for Text2SQL [experiment_name=Initial dataset] (tasks):   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "60.00% 'no_error' score\n", "\n", "See results for Initial dataset at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Initial%20dataset\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(...)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from braintrust import EvalAsync\n", "\n", "PROJECT_NAME = \"LLM Eval for Text2SQL\"\n", "\n", "await <PERSON><PERSON><PERSON>(\n", "    PROJECT_NAME,\n", "    experiment_name=\"Initial dataset\",\n", "    data=[{\"input\": q} for q in questions],\n", "    task=text2sql,\n", "    scores=[no_error],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ok! It looks like 3/5 of our queries are valid. Let's take a closer look in the Braintrust UI.\n", "\n", "![eval results](./assets/initial_results.png)\n", "\n", "### Interpreting results\n", "\n", "Now that we ran the initial eval, it looks like two of the results are valid, two produce SQL errors, and one is incorrect.\n", "\n", "To best utilize these results:\n", "\n", "1. Let's capture the good data into a dataset. Since our eval pipeline did the hard work of generating a reference query and results, we can\n", "   now save these, and make sure that future changes we make do not _regress_ the results.\n", "\n", "![add to dataset](./assets/add-to-dataset.gif)\n", "\n", "- The incorrect query didn't seem to get the date format correct. That would probably be improved by showing a sample of the data to the model.\n", "\n", "![invalid query](./assets/incorrect-query.png)\n", "\n", "- There are two binder errors, which may also have to do with not understanding the data format.\n", "\n", "![binder errors](./assets/binder_error.png)\n", "\n", "### Updating the eval\n", "\n", "Let's start by reworking our `data` function to pull the golden data we're storing in Braintrust and extend it with the handwritten questions. Since\n", "there may be some overlap, we automatically exclude any questions that are already in the dataset.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': '614006b1-a8b1-40c2-b700-3634c4fb14f5',\n", " '_xact_id': '1000193117554478505',\n", " 'created': '2024-05-29 16:23:59.989+00',\n", " 'project_id': 'b8d44d19-7999-49b0-911b-1f0aaafc5bac',\n", " 'dataset_id': 'a6c337e3-f7f7-4a96-8529-05cb172f847e',\n", " 'input': 'Which team won the most games?',\n", " 'expected': {'error': None,\n", "  'query': \"SELECT Team, COUNT(*) AS Wins\\nFROM nba\\nWHERE WINorLOSS = 'W'\\nGROUP BY Team\\nORDER BY Wins DESC\\nLIMIT 1;\",\n", "  'results': [{'Team': 'GSW', 'Wins': 265}]},\n", " 'metadata': {},\n", " 'tags': [],\n", " 'span_id': '614006b1-a8b1-40c2-b700-3634c4fb14f5',\n", " 'root_span_id': '614006b1-a8b1-40c2-b700-3634c4fb14f5'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["from braintrust import init_dataset\n", "\n", "\n", "def load_data():\n", "    golden_data = init_dataset(PROJECT_NAME, \"Golden data\")\n", "    golden_questions = set(d[\"input\"] for d in golden_data)\n", "    return list(golden_data) + [\n", "        {\"input\": q} for q in questions if q not in golden_questions\n", "    ]\n", "\n", "\n", "load_data()[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's tweak the prompt to include a sample of each row.\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT Team, COUNT(*) AS Wins\n", "FROM nba\n", "WHERE WINorLOSS = 'W' AND Date LIKE '%/15'\n", "GROUP BY Team\n", "ORDER BY Wins DESC\n", "LIMIT 1;\n"]}], "source": ["samples = conn.query(\"SELECT * FROM nba LIMIT 1\").to_df().to_dict(orient=\"records\")[0]\n", "\n", "\n", "@braintrust.traced\n", "async def generate_query(input):\n", "    response = await client.chat.completions.create(\n", "        model=TASK_MODEL,\n", "        temperature=0,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": dedent(f\"\"\"\\\n", "        You are a SQL expert, and you are given a single table named nba with the following columns:\n", "\n", "        Column | Type | Example\n", "        -------|------|--------\n", "        {\"\\n\".join(f\"{column['column_name']} | {column['column_type']} | {samples[column['column_name']]}\" for column in columns)}\n", "\n", "        Write a DuckDB SQL query corresponding to the user's request. Return just the query text, with no\n", "        formatting (backticks, markdown, etc.).\n", "\"\"\"),\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": input,\n", "            },\n", "        ],\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "\n", "print(await generate_query(\"Which team won the most games in 2015?\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking much better! Finally, let's add a scoring function that compares the results, if they exist, with the expected results.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autoevals import JSONDiff, Sql\n", "\n", "\n", "def extract_values(results):\n", "    return [list(result.values()) for result in results]\n", "\n", "\n", "def correct_result(output, expected):\n", "    if (\n", "        expected is None\n", "        or expected.get(\"results\") is None\n", "        or output.get(\"results\") is None\n", "    ):\n", "        return None\n", "    return J<PERSON><PERSON><PERSON>()(\n", "        output=extract_values(output[\"results\"]),\n", "        expected=extract_values(expected[\"results\"]),\n", "    ).score\n", "\n", "\n", "def correct_sql(input, output, expected):\n", "    if expected is None or expected.get(\"query\") is None or output.get(\"query\") is None:\n", "        return None\n", "    return Sql()(input=input, output=output[\"query\"], expected=expected[\"query\"]).score"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Great. Let's plug these pieces together and run an eval!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment With samples is running at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/With%20samples\n", "LLM Eval for Text2SQL [experiment_name=With samples] (data): 5it [00:00, 17848.10it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "411ec1a94e0946cd8f598b68db8994b8", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM Eval for Text2SQL [experiment_name=With samples] (tasks):   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "With samples compared to Initial dataset:\n", "80.00% (+20.00%) 'no_error'       score\t(1 improvements, 0 regressions)\n", "100.00% 'correct_result' score\n", "100.00% 'correct_sql'    score\n", "\n", "5.78s duration\n", "\n", "See results for With samples at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/With%20samples\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(...)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["await <PERSON><PERSON><PERSON>(\n", "    PROJECT_NAME,\n", "    experiment_name=\"With samples\",\n", "    data=load_data,\n", "    task=text2sql,\n", "    scores=[no_error, correct_result, correct_sql],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Amazing. It looks like we removed one of the errors, and got a result for the incorrect query.\n", "\n", "![updated eval](./assets/eval-2.png)\n", "\n", "Let's add the \"Which team won the most games in 2015?\" row to our dataset, since its answer now looks correct.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating more data\n", "\n", "Now that we have a basic flow in place, let's generate some data. We're going to use the dataset itself to generate expected queries, and have a model describe the queries.\n", "This is a slightly more robust method than having it generate queries, because we'd expect a model to describe a query more accurately than generate one from scratch.\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'sql': \"SELECT Team, COUNT(*) as TotalGames, SUM(CASE WHEN WINorLOSS = 'W' THEN 1 ELSE 0 END) as Wins, SUM(CASE WHEN WINorLOSS = 'L' THEN 1 ELSE 0 END) as Losses FROM nba GROUP BY Team;\",\n", " 'question': 'What is the total number of games played, wins, and losses for each team?'}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "class Question(BaseModel):\n", "    sql: str\n", "    question: str\n", "\n", "\n", "class Questions(BaseModel):\n", "    questions: list[Question]\n", "\n", "\n", "logger = braintrust.init_logger(\"question generator\")\n", "\n", "response = await client.chat.completions.create(\n", "    model=\"gpt-4o\",\n", "    temperature=0,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": dedent(f\"\"\"\\\n", "        You are a SQL expert, and you are given a single table named nba with the following columns:\n", "\n", "        Column | Type | Example\n", "        -------|------|--------\n", "        {\"\\n\".join(f\"{column['column_name']} | {column['column_type']} | {samples[column['column_name']]}\" for column in columns)}\n", "\n", "        Generate SQL queries that would be interesting to ask about this table. Return the SQL query as a string, as well as the\n", "        question that the query answers.\"\"\"),\n", "        }\n", "    ],\n", "    tools=[\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"generate_questions\",\n", "                \"description\": \"Generate SQL queries that would be interesting to ask about this table.\",\n", "                \"parameters\": Questions.model_json_schema(),\n", "            },\n", "        }\n", "    ],\n", "    tool_choice={\"type\": \"function\", \"function\": {\"name\": \"generate_questions\"}},\n", ")\n", "\n", "generated_questions = json.loads(response.choices[0].message.tool_calls[0].function.arguments)[\"questions\"]\n", "generated_questions[0]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query failed: SELECT Team, AVG(FieldGoals.) as AvgFieldGoalPercentage, AVG(X3PointShots.) as Avg3PointPercentage, AVG(FreeThrows.) as AvgFreeThrowPercentage FROM nba GROUP BY Team; Parser Error: syntax error at or near \")\"\n", "Skipping...\n", "Query failed: SELECT Team, AVG(Opp.FieldGoals.) as AvgOppFieldGoalPercentage, AVG(Opp.3PointShots.) as AvgOpp3PointPercentage, AVG(Opp.FreeThrows.) as AvgOppFreeThrowPercentage FROM nba GROUP BY Team; Parser Error: syntax error at or near \")\"\n", "Skipping...\n"]}, {"data": {"text/plain": ["{'input': 'What is the total number of games played, wins, and losses for each team?',\n", " 'expected': {'results': [{'Team': 'ATL',\n", "    'TotalGames': 328,\n", "    'Wins': 175.0,\n", "    'Losses': 153.0},\n", "   {'Team': 'CHI', 'TotalGames': 328, 'Wins': 160.0, 'Losses': 168.0},\n", "   {'Team': 'NYK', 'TotalGames': 328, 'Wins': 109.0, 'Losses': 219.0},\n", "   {'Team': 'POR', 'TotalGames': 328, 'Wins': 185.0, 'Losses': 143.0},\n", "   {'Team': 'DEN', 'TotalGames': 328, 'Wins': 149.0, 'Losses': 179.0},\n", "   {'Team': 'UTA', 'TotalGames': 328, 'Wins': 177.0, 'Losses': 151.0},\n", "   {'Team': '<PERSON><PERSON>', 'TotalGames': 328, 'Wins': 107.0, 'Losses': 221.0},\n", "   {'Team': 'CHO', 'TotalGames': 328, 'Wins': 153.0, 'Losses': 175.0},\n", "   {'Team': 'DAL', 'TotalGames': 328, 'Wins': 149.0, 'Losses': 179.0},\n", "   {'Team': 'LAC', 'TotalGames': 328, 'Wins': 202.0, 'Losses': 126.0},\n", "   {'Team': 'DET', 'TotalGames': 328, 'Wins': 152.0, 'Losses': 176.0},\n", "   {'Team': 'GS<PERSON>', 'TotalGames': 328, 'Wins': 265.0, 'Losses': 63.0},\n", "   {'Team': 'IND', 'TotalGames': 328, 'Wins': 173.0, 'Losses': 155.0},\n", "   {'Team': 'MI<PERSON>', 'TotalGames': 328, 'Wins': 170.0, 'Losses': 158.0},\n", "   {'Team': 'MI<PERSON>', 'TotalGames': 328, 'Wins': 160.0, 'Losses': 168.0},\n", "   {'Team': 'SAC', 'TotalGames': 328, 'Wins': 121.0, 'Losses': 207.0},\n", "   {'Team': 'OKC', 'TotalGames': 328, 'Wins': 195.0, 'Losses': 133.0},\n", "   {'Team': 'PHI', 'TotalGames': 328, 'Wins': 108.0, 'Losses': 220.0},\n", "   {'Team': 'PHO', 'TotalGames': 328, 'Wins': 107.0, 'Losses': 221.0},\n", "   {'Team': 'SAS', 'TotalGames': 328, 'Wins': 230.0, 'Losses': 98.0},\n", "   {'Team': 'B<PERSON>', 'TotalGames': 328, 'Wins': 196.0, 'Losses': 132.0},\n", "   {'Team': 'HOU', 'TotalGames': 328, 'Wins': 217.0, 'Losses': 111.0},\n", "   {'Team': 'LAL', 'TotalGames': 328, 'Wins': 99.0, 'Losses': 229.0},\n", "   {'Team': 'MIN', 'TotalGames': 328, 'Wins': 123.0, 'Losses': 205.0},\n", "   {'Team': 'TOR', 'TotalGames': 328, 'Wins': 215.0, 'Losses': 113.0},\n", "   {'Team': '<PERSON><PERSON>', 'TotalGames': 328, 'Wins': 211.0, 'Losses': 117.0},\n", "   {'Team': 'MEM', 'TotalGames': 328, 'Wins': 162.0, 'Losses': 166.0},\n", "   {'Team': 'NOP', 'TotalGames': 328, 'Wins': 157.0, 'Losses': 171.0},\n", "   {'Team': 'OR<PERSON>', 'TotalGames': 328, 'Wins': 114.0, 'Losses': 214.0},\n", "   {'Team': 'WAS', 'TotalGames': 328, 'Wins': 179.0, 'Losses': 149.0}],\n", "  'error': None,\n", "  'query': \"SELECT Team, COUNT(*) as TotalGames, SUM(CASE WHEN WINorLOSS = 'W' THEN 1 ELSE 0 END) as Wins, SUM(CASE WHEN WINorLOSS = 'L' THEN 1 ELSE 0 END) as Losses FROM nba GROUP BY Team;\"},\n", " 'metadata': {'category': 'Generated'}}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["generated_dataset = []\n", "for q in generated_questions:\n", "    try:\n", "        result = execute_query(q[\"sql\"])\n", "        generated_dataset.append(\n", "            {\n", "                \"input\": q[\"question\"],\n", "                \"expected\": {\n", "                    \"results\": result,\n", "                    \"error\": None,\n", "                    \"query\": q[\"sql\"],\n", "                },\n", "                \"metadata\": {\n", "                    \"category\": \"Generated\",\n", "                },\n", "            }\n", "        )\n", "    except duckdb.E<PERSON>r as e:\n", "        print(f\"Query failed: {q['sql']}\", e)\n", "        print(\"Skipping...\")\n", "\n", "generated_dataset[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome, let's update our dataset with the new data.\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def load_data():\n", "    golden_data = init_dataset(PROJECT_NAME, \"Golden data\")\n", "    golden_questions = set(d[\"input\"] for d in golden_data)\n", "    return (\n", "        [{**x, \"metadata\": {\"category\": \"Golden data\"}} for x in golden_data]\n", "        + [\n", "            {\"input\": q, \"metadata\": {\"category\": \"Handwritten question\"}}\n", "            for q in questions\n", "            if q not in golden_questions\n", "        ]\n", "        + [x for x in generated_dataset if x[\"input\"] not in golden_questions]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Generated data is running at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Generated%20data\n", "LLM Eval for Text2SQL [experiment_name=Generated data] (data): 13it [00:00, 36916.69it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "878bb9811d0d43d29c4011e9526cb75a", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM Eval for Text2SQL [experiment_name=Generated data] (tasks):   0%|          | 0/13 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Generated data compared to With samples:\n", "84.62% (-) 'no_error'       score\t(0 improvements, 0 regressions)\n", "69.72% (-) 'correct_result' score\t(0 improvements, 0 regressions)\n", "63.64% (-) 'correct_sql'    score\t(0 improvements, 0 regressions)\n", "\n", "4.23s (-155.93%) 'duration'\t(0 improvements, 0 regressions)\n", "\n", "See results for Generated data at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Generated%20data\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(...)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["await <PERSON><PERSON><PERSON>(\n", "    PROJECT_NAME,\n", "    experiment_name=\"Generated data\",\n", "    data=load_data,\n", "    task=text2sql,\n", "    scores=[no_error, correct_result, correct_sql],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![eval 3](./assets/eval-3.png)\n", "\n", "Amazing! Now we have a rich dataset to work with and some failures to debug. From here, you could try to investigate whether some of the generated data needs improvement, or try tweaking the prompt to improve accuracy,\n", "or maybe even something more adventurous, like feed the errors back to the model and have it iterate on a better query. Most importantly, we have a good workflow in place to iterate on both the application and dataset.\n", "\n", "## Trying GPT-4\n", "\n", "Just for fun, let's wrap things up by trying out GPT-4. All we need to do is switch the model name, and run our `Eval()` function again.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Try gpt-4 is running at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Try%20gpt-4\n", "LLM Eval for Text2SQL [experiment_name=Try gpt-4] (data): 13it [00:00, 25491.33it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9c7e00d40b2a4af29a72902e778c8b7b", "version_major": 2, "version_minor": 0}, "text/plain": ["LLM Eval for Text2SQL [experiment_name=Try gpt-4] (tasks):   0%|          | 0/13 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Try gpt-4 compared to Generated data:\n", "46.14% (-23.58%) 'correct_result' score\t(1 improvements, 5 regressions)\n", "84.62% (-) 'no_error'       score\t(1 improvements, 1 regressions)\n", "54.55% (-09.09%) 'correct_sql'    score\t(1 improvements, 2 regressions)\n", "\n", "6.77s (+254.27%) 'duration'\t(0 improvements, 1 regressions)\n", "\n", "See results for Try gpt-4 at https://www.braintrust.dev/app/braintrustdata.com/p/LLM%20Eval%20for%20Text2SQL/experiments/Try%20gpt-4\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(...)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["TASK_MODEL = \"gpt-4\"\n", "\n", "await <PERSON><PERSON><PERSON>(\n", "    PROJECT_NAME,\n", "    experiment_name=\"Try gpt-4\",\n", "    data=load_data,\n", "    task=text2sql,\n", "    scores=[no_error, correct_result, correct_sql],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Interesting. It seems like that was not a slam dunk. There were a few regressions on each of the scores:\n", "\n", "![gpt-4-eval](./assets/eval-gpt-4.png)\n", "\n", "Braintrust makes it easy to filter down to the regressions, and view a side-by-side diff:\n", "\n", "![diff](./assets/analyze-regressions.gif)\n", "\n", "## Conclusion\n", "\n", "In this cookbook, we walked through the process of building a dataset for a text2sql application. We started with a few handwritten examples, and iterated on the dataset by using an LLM to generate more examples. We used the eval framework to track our progress, and iterated on the model and dataset to improve the results. Finally, we tried out a more powerful model to see if it could improve the results.\n", "\n", "Happy evaling!\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}