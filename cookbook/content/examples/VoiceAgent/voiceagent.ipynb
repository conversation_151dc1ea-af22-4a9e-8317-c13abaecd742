{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating a voice agent for classifying support messages\n", "\n", "In this cookbook, we'll walk through how to evaluate an AI voice agent that classifies short customer support messages by language. In a production application, this might be one component of a customer support agent. Our approach uses an LLM and text-to-speech (TTS) to generate synthetic customer calls, and OpenAI's GPT-4o audio model to classify the calls. Finally, we'll use Braintrust to evaluate the performance of the classifier using `ExactMatch` from our [autoevals library](https://github.com/braintrustdata/autoevals).\n", "\n", "## Getting started\n", "\n", "You’ll need a [Braintrust](https://www.braintrust.dev/signup) account, along with an [OpenAI API key](https://platform.openai.com/). Export your `BRAINTRUST_API_KEY` and `OPENAI_API_KEY` to your environment:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_BRAINTRUST_API_KEY\"\n", "export OPENAI_API_KEY=\"YOUR_OPENAI_API_KEY\"\n", "```\n", "\n", "Next, install the required packages:\n", "```bash\n", "pip install braintrust openai autoevals librosa soundfile\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We’ll import our modules, then wrap the OpenAI client for Braintrust features.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<Callout type=\"info\">\n", "Best practice is to export your API key as an environment variable. However, to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import os\n", "import base64\n", "import tempfile\n", "import random\n", "import soundfile as sf\n", "import librosa\n", "import openai\n", "import string\n", "import nest_asyncio\n", "import numpy as np\n", "\n", "from braintrust import EvalAsync, Attachment, current_span, wrap_openai\n", "from autoevals import ExactMatch\n", "\n", "# Uncomment to hardcode your API keys\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_BRAINTRUST_API_KEY\"\n", "# os.environ[\"OPENAI_API_KEY\"] = \"YOUR_OPENAI_API_KEY\"\n", "\n", "openai.api_key = os.environ[\"OPENAI_API_KEY\"]\n", "\n", "# OpenAI client instance, wrapped for Braintrust.\n", "openai_client = wrap_openai(openai.OpenAI(api_key=openai.api_key))\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating synthetic support calls\n", "\n", "We'll create a function `generate_customer_issue` that asks the LLM to produce one-sentence customer service inquiries in multiple languages, along with a fallback if LLM calls fail. Then, we'll call a TTS endpoint to produce audio from each sentence. We store everything in an array for easy iteration.\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def generate_customer_issue(language):\n", "    \"\"\"\n", "    Generate a realistic one-sentence customer service inquiry in the specified language.\n", "    If the API call fails, return a fallback string.\n", "    \"\"\"\n", "    prompt = (\n", "        f\"Generate a realistic one-sentence customer service inquiry in {language}. \"\n", "        \"The sentence should reflect a common customer issue and be in natural language.\"\n", "    )\n", "    try:\n", "        response = openai_client.chat.completions.create(\n", "            model=\"gpt-4o\",\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            temperature=0,\n", "            max_tokens=100,\n", "        )\n", "        return response.choices[0].message.content.strip()\n", "    except Exception:\n", "        fallback_texts = {\n", "            \"english\": \"I can't access my account.\",\n", "            \"spanish\": \"No puedo acceder a mi cuenta.\",\n", "            \"french\": \"Je n'arrive pas à accéder à mon compte.\",\n", "            \"german\": \"Ich kann nicht auf mein Konto zugreifen.\",\n", "            \"italian\": \"Non riesco ad accedere al mio account.\",\n", "        }\n", "        return fallback_texts.get(language, \"I need help.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating evaluation data\n", "\n", "We'll generate multiple snippets for each language, each produced by TTS. If TTS fails, we use a dummy silence clip as a fallback.\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def load_eval_data(limit=20):\n", "    \"\"\"\n", "    Generate synthetic evaluation data simulating customer service calls.\n", "    For each of five languages, generate a customer issue and create TTS audio.\n", "    If the TTS API call fails, print a debug message and use dummy audio data.\n", "    \"\"\"\n", "    languages = [\"english\", \"spanish\", \"french\", \"german\", \"italian\"]\n", "    voices = [\n", "        \"alloy\",\n", "        \"ash\",\n", "        \"coral\",\n", "        \"echo\",\n", "        \"fable\",\n", "        \"onyx\",\n", "        \"nova\",\n", "        \"sage\",\n", "        \"shimmer\",\n", "    ]\n", "    eval_data = []\n", "\n", "    examples_per_language = limit // len(languages)\n", "    extra_examples = limit % len(languages)\n", "\n", "    for i, lang in enumerate(languages):\n", "        # Distribute any extra examples across the first few languages.\n", "        num_examples = examples_per_language + (1 if i < extra_examples else 0)\n", "        for _ in range(num_examples):\n", "            # Generate the raw text for the TTS call.\n", "            customer_text = generate_customer_issue(lang)\n", "            selected_voice = random.choice(voices)\n", "            tts_file_path = None\n", "            try:\n", "                with tempfile.NamedTemporaryFile(\n", "                    suffix=\".mp3\", delete=False\n", "                ) as tmp_file:\n", "                    tts_file_path = tmp_file.name\n", "\n", "                tts_response = openai.audio.speech.create(\n", "                    model=\"tts-1\",\n", "                    voice=selected_voice,\n", "                    input=customer_text,\n", "                )\n", "                # Use the original streaming call that worked before the asyncio changes.\n", "                tts_response.stream_to_file(tts_file_path)\n", "                audio_array, sampling_rate = librosa.load(tts_file_path, sr=None)\n", "            except Exception as e:\n", "                print(\n", "                    f\"TTS generation failed for language '{lang}' with voice '{selected_voice}': {e}\"\n", "                )\n", "                print(\"Using dummy audio data instead.\")\n", "                # Create 1 second of silence at 22050 Hz as dummy audio.\n", "                audio_array = np.zeros(22050)\n", "                sampling_rate = 22050\n", "            finally:\n", "                if tts_file_path and os.path.exists(tts_file_path):\n", "                    try:\n", "                        os.remove(tts_file_path)\n", "                    except Exception as cleanup_e:\n", "                        print(f\"Error cleaning up temporary file: {cleanup_e}\")\n", "\n", "            # Append the evaluation case with metadata.\n", "            eval_data.append(\n", "                {\n", "                    \"input\": {\n", "                        \"audio\": {\"array\": audio_array, \"sampling_rate\": sampling_rate}\n", "                    },\n", "                    \"expected\": lang,\n", "                    \"metadata\": {\n", "                        \"voice_model\": selected_voice,\n", "                        \"expected_language\": lang,\n", "                        \"raw_text\": customer_text,\n", "                    },\n", "                }\n", "            )\n", "\n", "    return eval_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task definition and audio attachment\n", "\n", "Below is our core task function, `task_func`, which receives an audio snippet, [attaches the raw audio to Braintrust for logging](/docs/guides/evals/write#attachments), and prompts an LLM to classify the language. Notice how we create an `Attachment` object and call `current_span().log(input={\"audio_attachment\": attachment})`. This adds the attachment to your log's trace details, which is helpful if you want to replay or debug your audio data.\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def task_func(example):\n", "    input_data = example.get(\"input\", example)\n", "    audio_info = input_data.get(\"audio\")\n", "    if not audio_info:\n", "        return \"ERROR: Missing audio input\"\n", "\n", "    # Determine the audio source: use an existing file or create one from the array.\n", "    audio_path = audio_info.get(\"path\")\n", "    temp_file_created = False\n", "    if not (audio_path and os.path.exists(audio_path)):\n", "        audio_array = audio_info.get(\"array\")\n", "        sampling_rate = audio_info.get(\"sampling_rate\")\n", "        if audio_array is None or sampling_rate is None:\n", "            return \"ERROR: Missing audio data\"\n", "        try:\n", "            with tempfile.NamedTemporaryFile(suffix=\".wav\", delete=False) as tmp_file:\n", "                audio_path = tmp_file.name\n", "            sf.write(audio_path, audio_array, sampling_rate)\n", "            temp_file_created = True\n", "        except Exception:\n", "            return \"ERROR: Failed to write temporary file\"\n", "\n", "    # Read and encode the audio file.\n", "    try:\n", "        with open(audio_path, \"rb\") as af:\n", "            audio_bytes = af.read()\n", "        encoded_audio = base64.b64encode(audio_bytes).decode(\"utf-8\")\n", "    except Exception:\n", "        return \"ERROR: Failed to read audio file\"\n", "\n", "    # Log the audio attachment to Braintrust.\n", "    try:\n", "        attachment = Attachment(\n", "            data=audio_bytes,\n", "            filename=\"raw_audio.wav\",\n", "            content_type=\"audio/wav\",\n", "        )\n", "        current_span().log(input={\"audio_attachment\": attachment})\n", "    except Exception:\n", "        pass\n", "\n", "    # Prepare the payload for language classification.\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": (\n", "                        \"Please listen to the following audio clip and determine the language being spoken. \"\n", "                        \"Return only the language as a single word (e.g., 'english', 'spanish'). \"\n", "                        \"Do not include any additional text or characters. If you cannot identify the language, return 'unknown'.\"\n", "                    ),\n", "                },\n", "                {\n", "                    \"type\": \"input_audio\",\n", "                    \"input_audio\": {\"data\": encoded_audio, \"format\": \"wav\"},\n", "                },\n", "            ],\n", "        }\n", "    ]\n", "\n", "    try:\n", "        response = openai_client.chat.completions.create(\n", "            model=\"gpt-4o-audio-preview\",\n", "            messages=messages,\n", "        )\n", "        raw_text = response.choices[0].message.content.strip().lower()\n", "        if not raw_text:\n", "            raise ValueError(\"Empty response from GPT-4o\")\n", "        output = raw_text.rstrip(string.punctuation)\n", "    except Exception:\n", "        output = \"error\"\n", "    finally:\n", "        if temp_file_created:\n", "            try:\n", "                os.remove(audio_path)\n", "            except Exception:\n", "                pass\n", "\n", "    # Log additional metadata (expected language and raw text used for TTS) to the current span.\n", "    try:\n", "        current_span().log(\n", "            metadata={\n", "                \"expected_language\": example.get(\"expected\"),\n", "                \"raw_text\": example.get(\"metadata\", {}).get(\"raw_text\"),\n", "            }\n", "        )\n", "    except Exception:\n", "        pass\n", "\n", "    return output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![attachment](assets/attachment.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the evaluation\n", "\n", "To evaluate our voice agent, we run `EvalAsync` with the `ExactMatch` scoring function. This will compare the agent's predicted language to the expected language, returning 1 if they match and 0 otherwise. After you run the code, you'll be able to analyze the results in the Braintrust UI.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await <PERSON><PERSON><PERSON>(\n", "    \"Multilingual Language Classification Eval\",\n", "    data=load_eval_data,\n", "    task=task_func,\n", "    scores=[ExactMatch],\n", "    metadata={\"model\": \"gpt-4o-audio-preview\"},\n", "    experiment_name=\"multilingual-language-classification-eval\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing results    \n", "\n", "In the Braintrust UI, you'll have each audio attachment in its corresponding trace, along with your classification logs and the score. You can refine your prompt or switch to a more advanced model if you notice any incorrect classifications.\n", "\n", "In our example, we attached metadata to each eval, giving you more granular insights into the classifier's performance. For example, you can group by `expected_language` and see if a particular language fails more often. These sorts of insights allow you to improve your prompting and overall pipeline.\n", "\n", "![group-by-language](assets/group.png)\n", "\n", "## Next steps\n", "\n", "As you continue iterating on this voice agent or build more complex AI products, you'll want to customize Braintrust even more for your use case.\n", "\n", "You might consider:\n", "\n", "- Reading our [blog on evaluating agents](/blog/evaluating-agents)\n", "\n", "- Learning to [evaluate prompt chaining agents](/docs/cookbook/recipes/PromptChaining)\n", "\n", "- Diving deeper into [LLM classifiers](/docs/cookbook/recipes/PrecisionRecall)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}