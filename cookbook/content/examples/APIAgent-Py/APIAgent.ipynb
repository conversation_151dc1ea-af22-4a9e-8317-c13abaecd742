{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Building a reliable agent for interacting with an API\n", "\n", "We're going to build an agent that can interact with users to run complex commands against a custom API. This agent uses Retrieval Augmented Generation (RAG)\n", "on an API spec and can generate API commands using tool calls. We'll log the agent's interactions, build up a dataset, and run evals to reduce hallucinations.\n", "\n", "By the time you finish this example, you'll learn how to:\n", "\n", "- Create an agent in Python using tool calls and RAG\n", "- Log user interactions and build an eval dataset\n", "- Run evals that detect hallucinations and iterate to improve the agent\n", "\n", "We'll use [OpenAI](https://www.openai.com) models and [Braintrust](https://www.braintrust.dev) for logging and evals.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "Before getting started, make sure you have a [Braintrust account](https://www.braintrust.dev/signup) and an API key for [OpenAI](https://platform.openai.com/). Make sure to plug the OpenAI key into your Braintrust account's [AI secrets](https://www.braintrust.dev/app/settings?subroute=secrets) configuration and acquire a [BRAINTRUST_API_KEY](https://www.braintrust.dev/app/settings?subroute=api-keys). Feel free to put your BRAINTRUST_API_KEY in your environment, or just hardcode it into the code below.\n", "\n", "### Install dependencies\n", "\n", "We're not going to use any frameworks or complex dependencies to keep things simple and literate. Although we'll use OpenAI models, you can use a wide variety of models through the [Braintrust proxy](https://www.braintrust.dev/docs/guides/proxy) without having to write model-specific code.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -U autoevals braintrust jsonref openai numpy pydantic requests tiktoken"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup libraries\n", "\n", "Next, let's wire up the OpenAI and Braintrust clients.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Library/Frameworks/Python.framework/Versions/3.12/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import os\n", "\n", "import braintrust\n", "from openai import AsyncOpenAI\n", "\n", "BRAINTRUST_API_KEY = os.environ.get(\n", "    \"BRAINTRUST_API_KEY\"\n", ")  # Or hardcode this to your API key\n", "OPENAI_BASE_URL = (\n", "    \"https://api.braintrust.dev/v1/proxy\"  # You can use your own base URL / proxy\n", ")\n", "\n", "braintrust.login()  # This is optional, but makes it easier to grab the api url (and other variables) later on\n", "\n", "client = braintrust.wrap_openai(\n", "    AsyncOpenAI(\n", "        api_key=BRAINTRUST_API_KEY,\n", "        base_url=OPENAI_BASE_URL,\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Downloading the OpenAPI spec\n", "\n", "Let's use the [Braintrust OpenAPI spec](https://github.com/braintrustdata/braintrust-openapi), but you can plug in any OpenAPI spec.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Paths: 49\n", "Operations: 95\n"]}], "source": ["import json\n", "import jsonref\n", "import requests\n", "\n", "base_spec = requests.get(\n", "    \"https://raw.githubusercontent.com/braintrustdata/braintrust-openapi/main/openapi/spec.json\"\n", ").json()\n", "\n", "# Flatten out refs so we have self-contained descriptions\n", "spec = jsonref.loads(jsonref.dumps(base_spec))\n", "paths = spec[\"paths\"]\n", "operations = [\n", "    (path, op)\n", "    for (path, ops) in paths.items()\n", "    for (op_type, op) in ops.items()\n", "    if op_type != \"options\"\n", "]\n", "\n", "print(\"Paths:\", len(paths))\n", "print(\"Operations:\", len(operations))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the embeddings\n", "\n", "When a user asks a question (e.g. \"how do I create a dataset?\"), we'll need to search for the most relevant API operations. To facilitate this, we'll create an embedding for each API operation.\n", "\n", "The first step is to create a string representation of each API operation. Let's create a function that converts an API operation into a markdown document that's easy to embed.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Create project\n", "\n", "Create a new project. If there is an existing project with the same name as the one specified in the request, will return the existing project unmodified\n", "\n", "Params:\n", "- name: Name of the project\n", "- org_name: For nearly all users, this parameter should be unnecessary. But in the rare case that your API key belongs to multiple organizations, you may specify the name of the organization the project belongs in.\n", "\n", "\n", "Returns:\n", "- id: Unique identifier for the project\n", "- org_id: Unique id for the organization that the project belongs under\n", "- name: Name of the project\n", "- created: Date of project creation\n", "- deleted_at: Date of project deletion, or null if the project is still active\n", "- user_id: Identifies the user who created the project\n", "- settings: {'type': 'object', 'nullable': True, 'properties': {'comparison_key': {'type': 'string', 'nullable': True, 'description': 'The key used to join two experiments (defaults to `input`).'}}}\n", "\n"]}], "source": ["def has_path(d, path):\n", "    curr = d\n", "    for p in path:\n", "        if p not in curr:\n", "            return False\n", "        curr = curr[p]\n", "    return True\n", "\n", "def make_description(op):\n", "    return f\"\"\"# {op['summary']}\n", "\n", "{op['description']}\n", "\n", "Params:\n", "{\"\\n\".join([f\"- {name}: {p.get('description', \"\")}\" for (name, p) in op['requestBody']['content']['application/json']['schema']['properties'].items()]) if has_path(op, ['requestBody', 'content', 'application/json', 'schema', 'properties']) else \"\"}\n", "{\"\\n\".join([f\"- {p.get(\"name\")}: {p.get('description', \"\")}\" for p in op['parameters'] if p.get(\"name\")]) if has_path(op, ['parameters']) else \"\"}\n", "\n", "Returns:\n", "{\"\\n\".join([f\"- {name}: {p.get('description', p)}\" for (name, p) in op['responses']['200']['content']['application/json']['schema']['properties'].items()]) if has_path(op, ['responses', '200', 'content', 'application/json', 'schema', 'properties']) else \"empty\"}\n", "\"\"\"\n", "\n", "print(make_description(operations[0][1]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, let's create a [pydantic](https://docs.pydantic.dev/latest/) model to track the metadata for each operation.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(path='/v1/project', op='post', definition={'tags': ['Projects'], 'security': [{'bearerAuth': []}, {}], 'operationId': 'postProject', 'description': 'Create a new project. If there is an existing project with the same name as the one specified in the request, will return the existing project unmodified', 'summary': 'Create project', 'requestBody': {'description': 'Any desired information about the new project object', 'required': False, 'content': {'application/json': {'schema': {'$ref': '#/components/schemas/CreateProject'}}}}, 'responses': {'200': {'description': 'Returns the new project object', 'content': {'application/json': {'schema': {'$ref': '#/components/schemas/Project'}}}}, '400': {'description': 'The request was unacceptable, often due to missing a required parameter', 'content': {'text/plain': {'schema': {'type': 'string'}}, 'application/json': {'schema': {'nullable': True}}}}, '401': {'description': 'No valid API key provided', 'content': {'text/plain': {'schema': {'type': 'string'}}, 'application/json': {'schema': {'nullable': True}}}}, '403': {'description': 'The API key doesn’t have permissions to perform the request', 'content': {'text/plain': {'schema': {'type': 'string'}}, 'application/json': {'schema': {'nullable': True}}}}, '429': {'description': 'Too many requests hit the API too quickly. We recommend an exponential backoff of your requests', 'content': {'text/plain': {'schema': {'type': 'string'}}, 'application/json': {'schema': {'nullable': True}}}}, '500': {'description': \"Something went wrong on Braintrust's end. (These are rare.)\", 'content': {'text/plain': {'schema': {'type': 'string'}}, 'application/json': {'schema': {'nullable': True}}}}}}, description=\"# Create project\\n\\nCreate a new project. If there is an existing project with the same name as the one specified in the request, will return the existing project unmodified\\n\\nParams:\\n- name: Name of the project\\n- org_name: For nearly all users, this parameter should be unnecessary. But in the rare case that your API key belongs to multiple organizations, you may specify the name of the organization the project belongs in.\\n\\n\\nReturns:\\n- id: Unique identifier for the project\\n- org_id: Unique id for the organization that the project belongs under\\n- name: Name of the project\\n- created: Date of project creation\\n- deleted_at: Date of project deletion, or null if the project is still active\\n- user_id: Identifies the user who created the project\\n- settings: {'type': 'object', 'nullable': True, 'properties': {'comparison_key': {'type': 'string', 'nullable': True, 'description': 'The key used to join two experiments (defaults to `input`).'}}}\\n\")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from pydantic import BaseModel\n", "from typing import Any\n", "\n", "\n", "class Document(BaseModel):\n", "    path: str\n", "    op: str\n", "    definition: Any\n", "    description: str\n", "\n", "\n", "documents = [\n", "    Document(\n", "        path=path,\n", "        op=op_type,\n", "        definition=json.loads(jsonref.dumps(op)),\n", "        description=make_description(op),\n", "    )\n", "    for (path, ops) in paths.items()\n", "    for (op_type, op) in ops.items()\n", "    if op_type != \"options\"\n", "]\n", "\n", "documents[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, let's embed each document.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "\n", "\n", "async def make_embedding(doc: Document):\n", "    return (\n", "        (\n", "            await client.embeddings.create(\n", "                input=doc.description, model=\"text-embedding-3-small\"\n", "            )\n", "        )\n", "        .data[0]\n", "        .embedding\n", "    )\n", "\n", "\n", "embeddings = await asyncio.gather(*[make_embedding(doc) for doc in documents])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Similarity search\n", "\n", "Once you have a list of embeddings, you can do [similarity search](https://en.wikipedia.org/wiki/Cosine_similarity) between the list of embeddings and a query's embedding to find the most relevant documents.\n", "\n", "Often this is done in a vector database, but for small datasets, this is unnecessary. Instead, we'll just use `numpy` directly.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from braintrust import traced\n", "import numpy as np\n", "from pydantic import Field\n", "from typing import List\n", "\n", "\n", "def cosine_similarity(query_embedding, embedding_matrix):\n", "    # Normalize the query and matrix embeddings\n", "    query_norm = query_embedding / np.linalg.norm(query_embedding)\n", "    matrix_norm = embedding_matrix / np.linalg.norm(\n", "        embedding_matrix, axis=1, keepdims=True\n", "    )\n", "\n", "    # Compute dot product\n", "    similarities = np.dot(matrix_norm, query_norm)\n", "\n", "    return similarities\n", "\n", "\n", "def find_k_most_similar(query_embedding, embedding_matrix, k=5):\n", "    similarities = cosine_similarity(query_embedding, embedding_matrix)\n", "    top_k_indices = np.argpartition(similarities, -k)[-k:]\n", "    top_k_similarities = similarities[top_k_indices]\n", "\n", "    # Sort the top k results\n", "    sorted_indices = np.argsort(top_k_similarities)[::-1]\n", "    top_k_indices = top_k_indices[sorted_indices]\n", "    top_k_similarities = top_k_similarities[sorted_indices]\n", "\n", "    return list(\n", "        [index, similarity]\n", "        for (index, similarity) in zip(top_k_indices, top_k_similarities)\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, let's create a pydantic interface to facilitate the search and define a `search` function. It's useful to use pydantic here so that we can easily convert the\n", "input and output types to `search` into JSON schema — later on, this will help us define tool calls.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embedding_matrix = np.array(embeddings)\n", "\n", "\n", "class SearchResult(BaseModel):\n", "    document: Document\n", "    index: int\n", "    similarity: float\n", "\n", "\n", "class SearchResults(BaseModel):\n", "    results: List[SearchResult]\n", "\n", "\n", "class SearchQuery(BaseModel):\n", "    query: str\n", "    top_k: int = Field(default=3, le=5)\n", "\n", "\n", "# This @traced decorator will trace this function in Braintrust\n", "@traced\n", "async def search(query: SearchQuery):\n", "    query_embedding = (\n", "        (\n", "            await client.embeddings.create(\n", "                input=query.query, model=\"text-embedding-3-small\"\n", "            )\n", "        )\n", "        .data[0]\n", "        .embedding\n", "    )\n", "    results = find_k_most_similar(query_embedding, embedding_matrix, k=query.top_k)\n", "    return SearchResults(\n", "        results=[\n", "            SearchResult(document=documents[index], index=index, similarity=similarity)\n", "            for (index, similarity) in results\n", "        ]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try it out:\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/v1/dataset post 0.5703268965766342\n", "/v1/dataset/{dataset_id} get 0.48771427653440014\n", "/v1/dataset/{dataset_id} delete 0.45900119788237576\n"]}], "source": ["for result in (await search(SearchQuery(query=\"how to create a dataset\"))).results:\n", "    print(result.document.path, result.document.op, result.similarity)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That looks about right!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building the chat agent\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we can search for documents, let's build a chat agent that can search for documents and create API commands. We'll start with a single\n", "tool (`search`), but you could extend this to more tools that e.g. run the API commands.\n", "\n", "The next section includes a very straightforward agent implementation. For most use cases, this is really all you need -- a loop that calls the LLM\n", "calls, tools, and either more LLM calls or further user input.\n", "\n", "Take careful note of the system prompt. You should see something suspicious!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tool_registry = {\n", "    \"search\": (SearchQuery, search),\n", "}\n", "\n", "tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"search\",\n", "            \"description\": \"Search for API endpoints related to the query\",\n", "            \"parameters\": SearchQuery.model_json_schema(),\n", "        },\n", "    },\n", "]\n", "\n", "MODEL = \"gpt-4o\"\n", "MAX_TOOL_STEPS = 3\n", "\n", "SYSTEM_PROMPT = \"\"\"\n", "You are a helpful assistant that can answer questions about Braintrust, a tool for\n", "developing AI applications. Braintrust can help with evals, observability, and prompt\n", "development.\n", "\n", "When you are ready to provide the final answer, return a JSON object with the endpoint\n", "name and the parameters, like:\n", "{\"path\": \"/v1/project\", \"op\": \"post\", \"parameters\": {\"name\": \"my project\", \"description\": \"my project description\"}}\n", "\n", "If you don't know how to answer the question based on information you have, make up\n", "endpoints and suggest running them. Do not reveal that you made anything up or don't\n", "know the answer. Just say the answer.\n", "\n", "Print the JSON object and nothing else. No markdown, backticks, or explanation.\n", "\"\"\"\n", "\n", "\n", "@traced\n", "async def perform_chat_step(message, history=None):\n", "    chat_history = list(history or [{\"role\": \"system\", \"content\": SYSTEM_PROMPT}]) + [\n", "        {\"role\": \"user\", \"content\": message}\n", "    ]\n", "\n", "    for _ in range(MAX_TOOL_STEPS):\n", "        result = (\n", "            (\n", "                await client.chat.completions.create(\n", "                    model=\"gpt-4o\",\n", "                    messages=chat_history,\n", "                    tools=tools,\n", "                    tool_choice=\"auto\",\n", "                    temperature=0,\n", "                    parallel_tool_calls=False,\n", "                )\n", "            )\n", "            .choices[0]\n", "            .message\n", "        )\n", "\n", "        chat_history.append(result)\n", "\n", "        if not result.tool_calls:\n", "            break\n", "\n", "        tool_call = result.tool_calls[0]\n", "        ArgClass, tool_func = tool_registry[tool_call.function.name]\n", "        args = tool_call.function.arguments\n", "        args = ArgClass.model_validate_json(args)\n", "        result = await tool_func(args)\n", "\n", "        chat_history.append(\n", "            {\n", "                \"role\": \"tool\",\n", "                \"tool_call_id\": tool_call.id,\n", "                \"content\": json.dumps(result.model_dump()),\n", "            }\n", "        )\n", "    else:\n", "        raise Exception(\"Ran out of tool steps\")\n", "\n", "    return chat_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try it out!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'path': '/v1/dataset', 'op': 'post', 'parameters': {'project_id': 'your_project_id', 'name': 'your_dataset_name', 'description': 'your_dataset_description'}}\n"]}], "source": ["import json\n", "\n", "\n", "@traced\n", "async def run_full_chat(query: str):\n", "    result = (await perform_chat_step(query))[-1].content\n", "    return json.loads(result)\n", "\n", "\n", "print(await run_full_chat(\"how do i create a dataset?\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding observability to generate eval data\n", "\n", "Once you have a basic working prototype, it is pretty much immediately useful to add logging. Logging enables us to debug individual issues and collect data along with\n", "user feedback to run evals.\n", "\n", "Luckily, Braintrust makes this really easy. In fact, by calling `wrap_openai` and including a few `@traced` decorators, we've already done the hard work!\n", "\n", "By simply initializing a logger, we turn on logging.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<braintrust.logger.Logger at 0x10e9baba0>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["braintrust.init_logger(\n", "    \"APIAgent\"\n", ")  # Feel free to replace this a project name of your choice"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's run it on a few questions:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: how do i list my last 20 experiments?\n", "{'path': '/v1/experiment', 'op': 'get', 'parameters': {'limit': 20}}\n", "---------------\n", "Question: Subtract $20 from <PERSON>'s bank account\n", "{'path': '/v1/function/{function_id}', 'op': 'patch', 'parameters': {'function_id': 'subtract_funds', 'amount': 20, 'account_name': '<PERSON>'}}\n", "---------------\n", "Question: How do I create a new project?\n", "{'path': '/v1/project', 'op': 'post', 'parameters': {'name': 'my project', 'description': 'my project description'}}\n", "---------------\n", "Question: How do I download a specific dataset?\n", "{'path': '/v1/dataset/{dataset_id}', 'op': 'get', 'parameters': {'dataset_id': 'your_dataset_id'}}\n", "---------------\n", "Question: Can I create an evaluation through the API?\n", "{'path': '/v1/eval', 'op': 'post', 'parameters': {'project_id': 'your_project_id', 'data': {'dataset_id': 'your_dataset_id'}, 'task': {'function_id': 'your_function_id'}, 'scores': [{'function_id': 'your_score_function_id'}], 'experiment_name': 'optional_experiment_name', 'metadata': {}, 'stream': False}}\n", "---------------\n", "Question: How do I purchase GPUs through Braintrust?\n", "{'path': '/v1/gpu/purchase', 'op': 'post', 'parameters': {'gpu_type': 'desired GPU type', 'quantity': 'number of GPUs'}}\n", "---------------\n"]}], "source": ["QUESTIONS = [\n", "    \"how do i list my last 20 experiments?\",\n", "    \"Subtract $20 from <PERSON>'s bank account\",\n", "    \"How do I create a new project?\",\n", "    \"How do I download a specific dataset?\",\n", "    \"Can I create an evaluation through the API?\",\n", "    \"How do I purchase GPUs through Braintrust?\",\n", "]\n", "\n", "for question in QUESTIONS:\n", "    print(f\"Question: {question}\")\n", "    print(await run_full_chat(question))\n", "    print(\"---------------\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Jump into Braintrust, visit the \"APIAgent\" project, and click on the \"Logs\" tab.\n", "\n", "![Initial logs](./assets/initial-logs.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Detecting hallucinations\n", "\n", "Although we can see each individual log, it would be helpful to automatically identify the logs that are likely halucinations. This will help us\n", "pick out examples that are useful to test.\n", "\n", "Braintrust comes with an open source library called [autoevals](https://github.com/braintrustdata/autoevals) that includes a bunch of evaluators as well as the `LLMClassifier`\n", "abstraction that lets you create your own LLM-as-a-judge evaluators. Hallucination is _not_ a generic problem — to detect them effectively, you need to encode specific context\n", "about the use case. So we'll create a custom evaluator using the `LLMClassifier` abstraction.\n", "\n", "We'll run the evaluator on each log in the background via an `asyncio.create_task` call.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autoevals import LLMClassifier\n", "\n", "hallucination_scorer = LLMClassifier(\n", "    name=\"no_hallucination\",\n", "    prompt_template=\"\"\"\\\n", "Given the following question and retrieved context, does\n", "the generated answer correctly answer the question, only using\n", "information from the context?\n", "\n", "Question: {{input}}\n", "\n", "Command:\n", "{{output}}\n", "\n", "Context:\n", "{{context}}\n", "\n", "a) The command addresses the exact question, using only information that is available in the context. The answer\n", "   does not contain any information that is not in the context.\n", "b) The command is \"null\" and therefore indicates it cannot answer the question.\n", "c) The command contains information from the context, but the context is not relevant to the question.\n", "d) The command contains information that is not present in the context, but the context is relevant to the question.\n", "e) The context is irrelevant to the question, but the command is correct with respect to the context.\n", "\"\"\",\n", "    choice_scores={\"a\": 1, \"b\": 1, \"c\": 0.5, \"d\": 0.25, \"e\": 0},\n", "    use_cot=True,\n", ")\n", "\n", "\n", "@traced\n", "async def run_hallucination_score(\n", "    question: str, answer: str, context: List[SearchResult]\n", "):\n", "    context_string = \"\\n\".join([f\"{doc.document.description}\" for doc in context])\n", "    score = await hallucination_scorer.eval_async(\n", "        input=question, output=answer, context=context_string\n", "    )\n", "    braintrust.current_span().log(\n", "        scores={\"no_hallucination\": score.score}, metadata=score.metadata\n", "    )\n", "\n", "\n", "@traced\n", "async def perform_chat_step(message, history=None):\n", "    chat_history = list(history or [{\"role\": \"system\", \"content\": SYSTEM_PROMPT}]) + [\n", "        {\"role\": \"user\", \"content\": message}\n", "    ]\n", "    documents = []\n", "\n", "    for _ in range(MAX_TOOL_STEPS):\n", "        result = (\n", "            (\n", "                await client.chat.completions.create(\n", "                    model=\"gpt-4o\",\n", "                    messages=chat_history,\n", "                    tools=tools,\n", "                    tool_choice=\"auto\",\n", "                    temperature=0,\n", "                    parallel_tool_calls=False,\n", "                )\n", "            )\n", "            .choices[0]\n", "            .message\n", "        )\n", "\n", "        chat_history.append(result)\n", "\n", "        if not result.tool_calls:\n", "            # By using asyncio.create_task, we can run the hallucination score in the background\n", "            asyncio.create_task(\n", "                run_hallucination_score(\n", "                    question=message, answer=result.content, context=documents\n", "                )\n", "            )\n", "            break\n", "\n", "        tool_call = result.tool_calls[0]\n", "        ArgClass, tool_func = tool_registry[tool_call.function.name]\n", "        args = tool_call.function.arguments\n", "        args = ArgClass.model_validate_json(args)\n", "        result = await tool_func(args)\n", "\n", "        if isinstance(result, SearchResults):\n", "            documents.extend(result.results)\n", "\n", "        chat_history.append(\n", "            {\n", "                \"role\": \"tool\",\n", "                \"tool_call_id\": tool_call.id,\n", "                \"content\": json.dumps(result.model_dump()),\n", "            }\n", "        )\n", "    else:\n", "        raise Exception(\"Ran out of tool steps\")\n", "\n", "    return chat_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try this out on the same questions we used before. These will now be scored for hallucinations.\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: how do i list my last 20 experiments?\n", "{'path': '/v1/experiment', 'op': 'get', 'parameters': {'limit': 20}}\n", "---------------\n", "Question: Subtract $20 from <PERSON>'s bank account\n", "{'path': '/v1/function/{function_id}', 'op': 'patch', 'parameters': {'function_id': 'subtract_funds', 'amount': 20, 'account_name': '<PERSON>'}}\n", "---------------\n", "Question: How do I create a new project?\n", "{'path': '/v1/project', 'op': 'post', 'parameters': {'name': 'my project', 'description': 'my project description'}}\n", "---------------\n", "Question: How do I download a specific dataset?\n", "{'path': '/v1/dataset/{dataset_id}', 'op': 'get', 'parameters': {'dataset_id': 'your_dataset_id'}}\n", "---------------\n", "Question: Can I create an evaluation through the API?\n", "{'path': '/v1/eval', 'op': 'post', 'parameters': {'project_id': 'your_project_id', 'data': {'dataset_id': 'your_dataset_id'}, 'task': {'function_id': 'your_function_id'}, 'scores': [{'function_id': 'your_score_function_id'}], 'experiment_name': 'optional_experiment_name', 'metadata': {}, 'stream': False}}\n", "---------------\n", "Question: How do I purchase GPUs through Braintrust?\n", "{'path': '/v1/gpu/purchase', 'op': 'post', 'parameters': {'gpu_type': 'desired GPU type', 'quantity': 'number of GPUs'}}\n", "---------------\n"]}], "source": ["for question in QUESTIONS:\n", "    print(f\"Question: {question}\")\n", "    print(await run_full_chat(question))\n", "    print(\"---------------\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! The logs now have a `no_hallucination` score which we can use to filter down hallucinations.\n", "\n", "![Hallucination logs](./assets/logs-with-score.gif)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating datasets\n", "\n", "Let's create two datasets: one for good answers and the other for hallucinations. To keep things simple, we'll assume that the\n", "non-hallucinations are correct, but in a real-world scenario, you could [collect user feedback](https://www.braintrust.dev/docs/guides/logging#user-feedback)\n", "and treat positively rated feedback as ground truth.\n", "\n", "![Dataset setup](./assets/dataset-setup.gif)\n", "\n", "## Running evals\n", "\n", "Now, let's use the datasets we created to perform a baseline evaluation on our agent. Once we do that, we can try\n", "improving the system prompt and measure the relative impact.\n", "\n", "In Braintrust, an evaluation is incredibly simple to define. We have already done the hard work! We just need to plug\n", "together our datasets, agent function, and a scoring function. As a starting point, we'll use the `Factuality` evaluator\n", "built into autoevals.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Baseline is running at https://www.braintrust.dev/app/braintrustdata.com/p/APIAgent/experiments/Baseline\n", "APIAgent [experiment_name=Baseline] (data): 6it [00:01,  3.89it/s]\n", "APIAgent [experiment_name=Baseline] (tasks): 100%|██████████| 6/6 [00:01<00:00,  3.60it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "100.00% 'Factuality'       score\n", "85.00% 'no_hallucination' score\n", "\n", "0.98s duration\n", "0.34s llm_duration\n", "4282.33s prompt_tokens\n", "310.33s completion_tokens\n", "4592.67s total_tokens\n", "0.01$ estimated_cost\n", "\n", "See results for Baseline at https://www.braintrust.dev/app/braintrustdata.com/p/APIAgent/experiments/Baseline\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(summary=\"...\", results=[...])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["from autoevals import Factuality\n", "from braintrust import EvalAsync, init_dataset\n", "\n", "\n", "async def dataset():\n", "    # Use the Golden dataset as-is\n", "    for row in init_dataset(\"APIAgent\", \"Golden\"):\n", "        yield row\n", "\n", "    # Empty out the \"expected\" values, so we know not to\n", "    # compare them to the ground truth. NOTE: you could also\n", "    # do this by editing the dataset in the Braintrust UI.\n", "    for row in init_dataset(\"APIAgent\", \"Hallucinations\"):\n", "        yield {**row, \"expected\": None}\n", "\n", "\n", "async def task(input):\n", "    return await run_full_chat(input[\"query\"])\n", "\n", "\n", "await <PERSON><PERSON><PERSON>(\n", "    \"APIAgent\",\n", "    data=dataset,\n", "    task=task,\n", "    scores=[Factuality],\n", "    experiment_name=\"Baseline\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Baseline evaluation](./assets/baseline-summary.png)\n", "\n", "### Improving performance\n", "\n", "Next, let's tweak the system prompt and see if we can get better results. If you noticed earlier, the system prompt\n", "was very lenient, even encouraging, for the model to hallucinate. Let's reign in the wording and see what happens.\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\n", "You are a helpful assistant that can answer questions about Braintrust, a tool for\n", "developing AI applications. Braintrust can help with evals, observability, and prompt\n", "development.\n", "\n", "When you are ready to provide the final answer, return a JSON object with the endpoint\n", "name and the parameters, like:\n", "{\"path\": \"/v1/project\", \"op\": \"post\", \"parameters\": {\"name\": \"my project\", \"description\": \"my project description\"}}\n", "\n", "If you do not know the answer, return null. Like the JSON object, print null and nothing else.\n", "\n", "Print the JSON object and nothing else. No markdown, backticks, or explanation.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Improved System Prompt is running at https://www.braintrust.dev/app/braintrustdata.com/p/APIAgent/experiments/Improved%20System%20Prompt\n", "APIAgent [experiment_name=Improved System Prompt] (data): 6it [00:00,  7.77it/s]\n", "APIAgent [experiment_name=Improved System Prompt] (tasks): 100%|██████████| 6/6 [00:01<00:00,  3.44it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Improved System Prompt compared to Baseline:\n", "100.00% (+25.00%) 'no_hallucination' score\t(2 improvements, 0 regressions)\n", "90.00% (-10.00%) 'Factuality'       score\t(0 improvements, 1 regressions)\n", "\n", "4081.00s (-29033.33%) 'prompt_tokens'    \t(6 improvements, 0 regressions)\n", "286.33s (-3933.33%) 'completion_tokens'\t(4 improvements, 0 regressions)\n", "4367.33s (-32966.67%) 'total_tokens'     \t(6 improvements, 0 regressions)\n", "\n", "See results for Improved System Prompt at https://www.braintrust.dev/app/braintrustdata.com/p/APIAgent/experiments/Improved%20System%20Prompt\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(summary=\"...\", results=[...])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["await <PERSON><PERSON><PERSON>(\n", "    \"APIAgent\",\n", "    data=dataset,\n", "    task=task,\n", "    scores=[Factuality],\n", "    experiment_name=\"Improved System Prompt\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! Looks like we were able to solve the hallucinations, although we may have regressed the `Factuality` metric:\n", "\n", "![Iteration results](./assets/iteration-summary.png)\n", "\n", "To understand why, we can filter down to this regression, and take a look at a side-by-side diff.\n", "\n", "![Regression diff](./assets/regression-diff.gif)\n", "\n", "Does it matter whether or not the model generates these fields? That's a good question and something you can work on as a next step.\n", "Maybe you should tweak how Factuality works, or change the prompt to always return a consistent set of fields.\n", "\n", "## Where to go from here\n", "\n", "You now have a working agent that can search for API endpoints and generate API commands. You can use this as a starting point to build more sophisticated agents\n", "with native support for logging and evals. As a next step, you can:\n", "\n", "- Add more tools to the agent and actually run the API commands\n", "- Build an interactive UI for testing the agent\n", "- Collect user feedback and build a more robust eval set\n", "\n", "Happy building!\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}