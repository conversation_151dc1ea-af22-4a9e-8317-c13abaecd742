{"cells": [{"cell_type": "markdown", "id": "c8f4c12a", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "07c34ffc-a3a1-4915-b866-e32d97a98e7c", "metadata": {}, "source": ["# Tool calls in LLaMa 3.1\n", "\n", "LLaMa 3.1 is distributed as an instruction-tuned model with 8B, 70B, and 405B parameter variants. As part of the release, Meta mentioned that\n", "\n", "> These are multilingual and have a significantly longer context length of 128K, state-of-the-art tool use, and overall stronger reasoning capabilities.\n", "\n", "Let's dig into how we can use these models with tools, and run an eval to see how they compare to gpt-4o on a benchmark.\n"]}, {"cell_type": "markdown", "id": "78277a53-7763-41cc-aa51-89354e729875", "metadata": {}, "source": ["## Setup\n", "\n", "You can access LLaMa 3.1 models through inference services like [Together](https://www.together.ai/), which has generous rate limits and OpenAI protocol compatibility. We'll use Together, through the\n", "[Braintrust proxy](https://www.braintrust.dev/docs/guides/proxy) to access LLaMa 3.1 and OpenAI models.\n", "\n", "To get started, make sure you have a Braintrust account and an API key for [Together](https://www.together.ai) and [OpenAI](https://platform.openai.com/). Make sure to plug them into your Braintrust account's\n", "[AI secrets](https://www.braintrust.dev/app/settings?subroute=secrets) configuration and acquire a [BRAINTRUST_API_KEY](https://www.braintrust.dev/app/settings?subroute=api-keys). Feel free to put your BRAINTRUST_API_KEY in a `.env.local` file next to this notebook, or just hardcode it into the code below.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b8e621c8", "metadata": {}, "outputs": [], "source": ["import dotenv from \"dotenv\";\n", "import * as fs from \"fs\";\n", "\n", "if (fs.existsSync(\".env.local\")) {\n", "  dotenv.config({ path: \".env.local\", override: true });\n", "}\n"]}, {"cell_type": "code", "execution_count": 5, "id": "ef910f35-4b64-403b-b5fb-7c3a9ebde8c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["However, I'm a large language model, I don't have real-time access to current weather conditions. But I can suggest some ways for you to find out the current weather in Tokyo:\n", "\n", "1. **Check online weather websites**: You can visit websites like AccuWeather, Weather.com, or the Japan Meteorological Agency (JMA) website to get the current weather conditions in Tokyo.\n", "2. **Use a weather app**: You can download a weather app on your smartphone, such as Dark Sky or Weather Underground, to get the current weather conditions in Tokyo.\n", "3. **Check social media**: You can also check social media platforms like Twitter or Facebook to see if there are any updates on the weather in Tokyo.\n", "\n", "That being said, I can provide you with some general information about the climate in Tokyo. Tokyo has a humid subtropical climate, with four distinct seasons:\n", "\n", "* **Spring (March to May)**: Mild temperatures, with average highs around 18°C (64°F) and lows around 10°C (50°F).\n", "* **Summer (June to August)**: Hot and humid, with average highs around 28°C (82°F) and lows around 22°C (72°F).\n", "* **Autumn (September to November)**: Comfortable temperatures, with average highs around 20°C (68°F) and lows around 12°C (54°F).\n", "* **Winter (December to February)**: Cool temperatures, with average highs around 25°C (77°F).\n", "* **Autumn (September to November)**: Comfort Index: 7/10\n", "* **Autumn (September to November)**: Comfortable temperatures, with average highs around 20°C (68°F) and lows around 12°C (54°F).\n", "* **Winter (December to February)**: Cool temperatures, with average highs around 10°C (50°F) and lows around 2°C (36°F).\n", "\n", "Please note that these are general temperature ranges, and the actual weather conditions can vary from year to year.\n", "\n", "If you provide me with the PDFs are often related to education, government, or business.\n"]}], "source": ["import { OpenAI } from \"openai\";\n", "import { wrapOpenAI } from \"braintrust\";\n", "\n", "const client = wrapOpenAI(\n", "  new OpenAI({\n", "    apiKey: process.env.BRAINTRUST_API_KEY,\n", "    baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "    defaultHeaders: { \"x-bt-use-cache\": \"never\" },\n", "  })\n", ");\n", "\n", "const LLAMA31_8B = \"meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo\";\n", "const LLAMA31_70B = \"meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo\";\n", "const LLAMA31_405B = \"meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo\";\n", "\n", "const response = await client.chat.completions.create({\n", "  model: LLAMA31_8B,\n", "  messages: [\n", "    {\n", "      role: \"user\",\n", "      content: \"What is the weather in Tokyo?\",\n", "    },\n", "  ],\n", "  max_tokens: 1024,\n", "  temperature: 0,\n", "});\n", "\n", "console.log(response.choices[0].message.content);\n"]}, {"cell_type": "markdown", "id": "28709b66", "metadata": {}, "source": ["As expected, the model can't answer the question without access to some tools. Traditionally, LLaMa models haven't supported tool calling. Some inference providers have attempted to solve this with controlled generation or similar methods, although to limited success. However, the [documentation](https://www.llama.com/docs/model-cards-and-prompt-formats/llama3_1/#json-based-tool-calling) alludes to a new approach to tool calls:\n", "\n", "```text\n", "Think very carefully before calling functions.\n", "If you choose to call a function ONLY reply in the following format with no prefix or suffix:\n", "\n", "<function=example_function_name>{\"example_name\": \"example_value\"}</function>\n", "```\n", "\n", "Let's see if we can make this work with the commonly used weather tool definition.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "305ef967", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<function=get_current_weather>{\"location\": \"Tokyo, JP\"}</function>\n"]}], "source": ["const weatherTool = {\n", "  name: \"get_current_weather\",\n", "  description: \"Get the current weather in a given location\",\n", "  parameters: {\n", "    type: \"object\",\n", "    properties: {\n", "      location: {\n", "        type: \"string\",\n", "        description: \"The city and state, e.g. San Francisco, CA\",\n", "      },\n", "    },\n", "    required: [\"location\"],\n", "  },\n", "};\n", "\n", "const toolPrompt = `You have access to the following functions:\n", "\n", "Use the function '${weatherTool.name}' to '${weatherTool.description}':\n", "${JSON.stringify(weatherTool)}\n", "\n", "If you choose to call a function ONLY reply in the following format with no prefix or suffix:\n", "\n", "<function=example_function_name>{\"example_name\": \"example_value\"}</function>\n", "\n", "Reminder:\n", "- If looking for real time information use relevant functions before falling back to brave_search\n", "- Function calls MUST follow the specified format, start with <function= and end with </function>\n", "- Required parameters MUST be specified\n", "- Only call one function at a time\n", "- Put the entire function call reply on one line\n", "\n", "`;\n", "\n", "const response = await client.chat.completions.create({\n", "  model: LLAMA31_8B,\n", "  messages: [\n", "    {\n", "      role: \"user\",\n", "      content: \"What is the weather in Tokyo?\",\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content: toolPrompt,\n", "    },\n", "  ],\n", "  max_tokens: 1024,\n", "  temperature: 0,\n", "});\n", "\n", "console.log(response.choices[0].message.content);\n"]}, {"cell_type": "markdown", "id": "f8b3f503", "metadata": {}, "source": ["Wow cool! Looks like we can get the model to call the tool. Let's quickly write a parser that can extract the function call from the response.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bbb28746", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  functionName: 'get_current_weather',\n", "  args: { location: 'Tokyo, JP' }\n", "}\n"]}], "source": ["function parseToolResponse(response: string) {\n", "  const functionRegex = /<function=(\\w+)>(.*?)<\\/function>/;\n", "  const match = response.match(functionRegex);\n", "\n", "  if (match) {\n", "    const [, functionName, argsString] = match;\n", "    try {\n", "      const args = JSON.parse(argsString);\n", "      return {\n", "        functionName,\n", "        args,\n", "      };\n", "    } catch (error) {\n", "      console.error(\"Error parsing function arguments:\", error);\n", "      return null;\n", "    }\n", "  }\n", "\n", "  return null;\n", "}\n", "\n", "const parsedResponse = parseToolResponse(response.choices[0].message.content);\n", "console.log(parsedResponse);\n"]}, {"cell_type": "markdown", "id": "77ae5ffe-c66d-4bbd-8b99-08a09af5900d", "metadata": {}, "source": ["## A real use case: LLM-as-a-Judge evaluators that make tool calls\n", "\n", "At Braintrust, we maintain a suite of evaluator functions in the [Autoevals](https://github.com/braintrustdata/autoevals) library. Many of these evaluators, like `Factuality`, are \"LLM-as-a-Judge\"\n", "evaluators that use a well-crafted prompt to an LLM to reason about the quality of a response. We are big fans of tool calling, and leverage it extensively in `autoevals` to make it easy and reliable\n", "to parse the scores and reasoning they produce.\n", "\n", "As we change autoevals, we run evals to make sure we improve performance and avoid regressing key scenarios. We'll run some of our autoeval evals as a way of assessing how well LLaMa 3.1 stacks up to gpt-4o.\n", "\n", "Here is a quick example of the `Factuality` scorer, a popular LLM-as-a-Judge evaluator that uses the following prompt:\n", "\n", "```ansi\n", "You are comparing a submitted answer to an expert answer on a given question. Here is the data:\n", "[BEGIN DATA]\n", "************\n", "[Question]: {{{input}}}\n", "************\n", "[Expert]: {{{expected}}}\n", "************\n", "[Submission]: {{{output}}}\n", "************\n", "[END DATA]\n", "\n", "Compare the factual content of the submitted answer with the expert answer. Ignore any differences in style, grammar, or punctuation.\n", "The submitted answer may either be a subset or superset of the expert answer, or it may conflict with it. Determine which case applies. Answer the question by selecting one of the following options:\n", "(A) The submitted answer is a subset of the expert answer and is fully consistent with it.\n", "(B) The submitted answer is a superset of the expert answer and is fully consistent with it.\n", "(C) The submitted answer contains all the same details as the expert answer.\n", "(D) There is a disagreement between the submitted answer and the expert answer.\n", "(E) The answers differ, but these differences don't matter from the perspective of factuality.\n", "```\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9eeb7c5f-f502-4f7e-b958-981dea2b62e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: 'Factuality',\n", "  score: 1,\n", "  metadata: {\n", "    rationale: '1. The expert answer states that the weather in Tokyo is \"extremely hot.\"\\n' +\n", "      '2. The submitted answer states that the weather in Tokyo is \"scorching.\"\\n' +\n", "      '3. Both \"extremely hot\" and \"scorching\" convey the same factual content, indicating very high temperatures.\\n' +\n", "      '4. There is no additional information in either answer that would make one a subset or superset of the other.\\n' +\n", "      '5. Therefore, the submitted answer contains all the same details as the expert answer.',\n", "    choice: 'C'\n", "  }\n", "}\n"]}], "source": ["import { Factuality } from \"autoevals\";\n", "\n", "console.log(\n", "  await Factuality({\n", "    input: \"What is the weather in Tokyo?\",\n", "    output: \"The weather in Tokyo is scorching.\",\n", "    expected: \"The weather in Tokyo is extremely hot.\",\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "id": "f5166207", "metadata": {}, "source": ["Now let's reproduce this with LLaMa 3.1.\n"]}, {"cell_type": "code", "execution_count": 10, "id": "d93a5585", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: 'Factuality',\n", "  score: 0.6,\n", "  metadata: {\n", "    rationale: \"The submitted answer 'The weather in Tokyo is scorching' is not a subset of the expert answer 'The weather in Tokyo is extremely hot' because 'scorching' is not a synonym of 'extremely hot'. However, 'scorching' is a synonym of 'extremely hot' in the context of describing hot weather. Therefore, the submitted answer is a superset of the expert answer and is fully consistent with it.\",\n", "    choice: 'B'\n", "  }\n", "}\n"]}], "source": ["import { templates } from \"autoevals\";\n", "import * as yaml from \"js-yaml\";\n", "import mustache from \"mustache\";\n", "\n", "const template = yaml.load(templates[\"factuality\"]);\n", "\n", "const selectTool = {\n", "  name: \"select_choice\",\n", "  description: \"Call this function to select a choice.\",\n", "  parameters: {\n", "    properties: {\n", "      reasons: {\n", "        description:\n", "          \"Write out in a step by step manner your reasoning to be sure that your conclusion is correct. Avoid simply stating the correct answer at the outset.\",\n", "        title: \"Reasoning\",\n", "        type: \"string\",\n", "      },\n", "      choice: {\n", "        description: \"The choice\",\n", "        title: \"Choice\",\n", "        type: \"string\",\n", "        enum: Object.keys(template.choice_scores),\n", "      },\n", "    },\n", "    required: [\"reasons\", \"choice\"],\n", "    title: \"CoTResponse\",\n", "    type: \"object\",\n", "  },\n", "};\n", "\n", "async function LLaMaFactuality({\n", "  model,\n", "  input,\n", "  output,\n", "  expected,\n", "}: {\n", "  model: string;\n", "  input: string;\n", "  output: string;\n", "  expected: string;\n", "}) {\n", "  const toolPrompt = `You have access to the following functions:\n", "\n", "Use the function '${selectTool.name}' to '${selectTool.description}':\n", "${JSON.stringify(selectTool)}\n", "\n", "If you choose to call a function ONLY reply in the following format with no prefix or suffix:\n", "\n", "<function=example_function_name>{\"example_name\": \"example_value\"}</function>\n", "\n", "Reminder:\n", "- If looking for real time information use relevant functions before falling back to brave_search\n", "- Function calls MUST follow the specified format, start with <function= and end with </function>\n", "- Required parameters MUST be specified\n", "- Only call one function at a time\n", "- Put the entire function call reply on one line\n", "\n", "`;\n", "\n", "  const response = await client.chat.completions.create({\n", "    model,\n", "    messages: [\n", "      {\n", "        role: \"user\",\n", "        content: mustache.render(template.prompt, {\n", "          input,\n", "          output,\n", "          expected,\n", "        }),\n", "      },\n", "      {\n", "        role: \"user\",\n", "        content: toolPrompt,\n", "      },\n", "    ],\n", "    temperature: 0,\n", "  });\n", "\n", "  try {\n", "    const parsed = parseToolResponse(response.choices[0].message.content);\n", "    return {\n", "      name: \"Factuality\",\n", "      score: template.choice_scores[parsed?.args.choice],\n", "      metadata: {\n", "        rationale: parsed?.args.reasons,\n", "        choice: parsed?.args.choice,\n", "      },\n", "    };\n", "  } catch (e) {\n", "    return {\n", "      name: \"Factuality\",\n", "      score: null,\n", "      metadata: {\n", "        error: `${e}`,\n", "      },\n", "    };\n", "  }\n", "}\n", "\n", "console.log(\n", "  await LLaMaFactuality({\n", "    model: LLAMA31_8B,\n", "    input: \"What is the weather in Tokyo?\",\n", "    output: \"The weather in Tokyo is scorching.\",\n", "    expected: \"The weather in Tokyo is extremely hot.\",\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "id": "05e7efe1", "metadata": {}, "source": ["Ok interesting! It parses but the response is a little different from the GPT-4o response. Let's put this to the test at scale with some evals.\n"]}, {"cell_type": "markdown", "id": "0c179efc", "metadata": {}, "source": ["## Running evals\n", "\n", "We use a subset of the [CoQA](https://stanfordnlp.github.io/coqa/) dataset to test the Factuality scorer. Let's load the dataset and take a look at an example.\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c5bdca48", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Factuality\n", "{\n", "  name: 'Factuality',\n", "  score: 0,\n", "  metadata: {\n", "    rationale: '1. The question asks about the color of Cotton.\\n' +\n", "      \"2. The expert answer is 'white,' which directly addresses the color of Cotton.\\n\" +\n", "      \"3. The submitted answer is 'in a barn,' which does not address the color of Cotton at all.\\n\" +\n", "      '4. Since the submitted answer does not provide any information about the color of Cotton, it conflicts with the expert answer.\\n' +\n", "      '\\n' +\n", "      'Therefore, there is a disagreement between the submitted answer and the expert answer.',\n", "    choice: 'D'\n", "  }\n", "}\n", "LLaMa-3.1-8B Factuality\n", "{\n", "  name: 'Factuality',\n", "  score: undefined,\n", "  metadata: { rationale: undefined, choice: undefined }\n", "}\n"]}], "source": ["interface CoqaCase {\n", "  input: {\n", "    input: string;\n", "    output: string;\n", "    expected: string;\n", "  };\n", "  expected: number;\n", "}\n", "\n", "const data: CoqaCase[] = JSON.parse(\n", "  fs.readFileSync(\"coqa-factuality.json\", \"utf-8\")\n", ");\n", "\n", "console.log(\"Factuality\");\n", "console.log(await Factuality(data[1].input));\n", "\n", "console.log(\"LLaMa-3.1-8B Factuality\");\n", "console.log(\n", "  await LLaMaFactuality({\n", "    model: LLAMA31_8B,\n", "    ...data[1].input,\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "id": "964befc0", "metadata": {}, "source": ["Not bad!\n", "\n", "### GPT-4o\n", "\n", "Let's run a full eval with gpt-4o, LLaMa-3.1-8B, LLaMa-3.1-70B, and LLaMa-3.1-405B to see how they stack up. Since the evaluator generates a number\n", "between 0 and 1, we'll use the `NumericDiff` scorer to assess accuracy, and a custom `NonNull` scorer to measure how many invalid tool calls are generated.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "21ea3e9a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" ████████████████████████░░░░░░░░░░░░░░░░ | LLaMa-3.1-Tools [experimentName=gpt-4o]  |  60% | 60/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "gpt-4o-8a54393b compared to gpt-4o-c699540b:\n", "100.00% (0.00%) 'NonNull'     score\t(0 improvements, 0 regressions)\n", "86.67% (*****%) 'NumericDiff' score\t(2 improvements, 0 regressions)\n", "\n", "4.54s 'duration'      \t(9 improvements, 51 regressions)\n", "0.00$ 'estimated_cost'\t(20 improvements, 14 regressions)\n", "\n", "See results for gpt-4o-8a54393b at https://www.braintrust.dev/app/braintrustdata.com/p/LLaMa-3.1-Tools/experiments/gpt-4o-8a54393b\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "import { NumericDiff } from \"autoevals\";\n", "\n", "function NonNull({ output }: { output: number | null }) {\n", "  return output !== null && output !== undefined ? 1 : 0;\n", "}\n", "\n", "const evalResult = await <PERSON><PERSON>(\"LLaMa-3.1-Tools\", {\n", "  data: data,\n", "  task: async (input) =>\n", "    (\n", "      await Factuality({\n", "        ...input,\n", "        openAiDefaultHeaders: { \"x-bt-use-cache\": \"never\" },\n", "      })\n", "    ).score,\n", "  scores: [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],\n", "  experimentName: \"gpt-4o\",\n", "  metadata: {\n", "    model: \"gpt-4o\",\n", "  },\n", "});\n"]}, {"cell_type": "markdown", "id": "3629a3a4", "metadata": {}, "source": ["It looks like GPT-4o does pretty well. Tool calling has been a highlight of OpenAI's feature set for a while, so it's not surprising that it's able to successfully parse 100% of the tool calls.\n", "\n", "![gpt-4o-result](./assets/gpt-4o-result.png)\n", "\n", "### LLama-3.1-8B, 70B, and 405B\n", "\n", "Now let's evaluate each of the LLaMa-3.1 models.\n"]}, {"cell_type": "code", "execution_count": 33, "id": "449cd41a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": [" ████████████████████████░░░░░░░░░░░░░░░░ | LLaMa-3.1-Tools [experimentName=meta-... |  60% | 60/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo compared to gpt-4o:\n", "80.00% (-20.00%) 'NonNull'     score\t(0 improvements, 12 regressions)\n", "68.90% (-19.43%) 'NumericDiff' score\t(1 improvements, 20 regressions)\n", "\n", "3.99s 'duration'      \t(21 improvements, 39 regressions)\n", "0.00$ 'estimated_cost'\t(60 improvements, 0 regressions)\n", "\n", "See results for meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo at https://www.braintrust.dev/app/braintrustdata.com/p/LLaMa-3.1-Tools/experiments/meta-llama%2FMeta-Llama-3.1-8B-Instruct-Turbo\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", " ████████████████████████░░░░░░░░░░░░░░░░ | LLaMa-3.1-Tools [experimentName=meta-... |  60% | 60/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo compared to meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo:\n", "100.00% (+20.00%) 'NonNull'     score\t(12 improvements, 0 regressions)\n", "90.00% (+21.10%) 'NumericDiff' score\t(23 improvements, 2 regressions)\n", "\n", "5.52s 'duration'      \t(15 improvements, 45 regressions)\n", "0.00$ 'estimated_cost'\t(0 improvements, 60 regressions)\n", "\n", "See results for meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo at https://www.braintrust.dev/app/braintrustdata.com/p/LLaMa-3.1-Tools/experiments/meta-llama%2FMeta-Llama-3.1-70B-Instruct-Turbo\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Error parsing function arguments: SyntaxError: Expected double-quoted property name in JSON at position 36 (line 1 column 37)\n", "    at JSON.parse (<anonymous>)\n", "    at Proxy.parseToolResponse (evalmachine.<anonymous>:9:31)\n", "    at Proxy.LLaMaFactuality (evalmachine.<anonymous>:97:32)\n", "    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n", "    at async Object.task (evalmachine.<anonymous>:5:33)\n", "    at async rootSpan.traced.name (/Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4488:26)\n", "    at async callback (/Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4484:11)\n", "    at async /Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4619:16\n", "Error parsing function arguments: SyntaxError: Expected double-quoted property name in JSON at position 36 (line 1 column 37)\n", "    at JSON.parse (<anonymous>)\n", "    at Proxy.parseToolResponse (evalmachine.<anonymous>:9:31)\n", "    at Proxy.LLaMaFactuality (evalmachine.<anonymous>:97:32)\n", "    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n", "    at async Object.task (evalmachine.<anonymous>:5:33)\n", "    at async rootSpan.traced.name (/Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4488:26)\n", "    at async callback (/Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4484:11)\n", "    at async /Users/<USER>/projects/braintrust/cookbook/content/node_modules/.pnpm/braintrust@0.0.145_openai@4.52.7_react@18.3.1_svelte@4.2.18_vue@3.4.32_zod@3.23.8/node_modules/braintrust/dist/index.js:4619:16\n", " ████████████████████████░░░░░░░░░░░░░░░░ | LLaMa-3.1-Tools [experimentName=meta-... |  60% | 60/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo compared to meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo:\n", "90.57% (+0.57%) 'NumericDiff' score\t(0 improvements, 2 regressions)\n", "88.33% (-11.67%) 'NonNull'     score\t(0 improvements, 7 regressions)\n", "\n", "7.68s 'duration'      \t(23 improvements, 37 regressions)\n", "0.00$ 'estimated_cost'\t(0 improvements, 60 regressions)\n", "\n", "See results for meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo at https://www.braintrust.dev/app/braintrustdata.com/p/LLaMa-3.1-Tools/experiments/meta-llama%2FMeta-Llama-3.1-405B-Instruct-Turbo\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for (const model of [LLAMA31_8B, LLAMA31_70B, LLAMA31_405B]) {\n", "  await <PERSON><PERSON>(\"LLaMa-3.1-Tools\", {\n", "    data: data,\n", "    task: async (input) => (await LLaMaFactuality({ model, ...input }))?.score,\n", "    scores: [<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],\n", "    experimentName: model,\n", "    metadata: {\n", "      model,\n", "    },\n", "  });\n", "}\n"]}, {"cell_type": "markdown", "id": "091c0f65", "metadata": {}, "source": ["### Analyzing the results: LLaMa-3.1-8B\n", "\n", "Ok, let's dig into the results. To start, we'll look at how LLaMa-3.1-8B compares to GPT-4o.\n", "\n", "![llama-3.1-8B-result](./assets/llama-3_1-8B-result.png)\n", "\n", "Although it's a fraction of the cost, it's both slower (likely due to rate limits) and worse performing than GPT-4o. 12 of the 60 cases failed to parse. Let's take a look at one of those in depth.\n", "\n", "![parsing-failure](./assets/parsing-failure.gif)\n", "\n", "That definitely looks like an invalid tool call. Maybe we can experiment with tweaking the prompt to get better results.\n", "\n", "### Analyzing all models\n", "\n", "If we look across models, we'll start to see some interesting takeaways.\n", "\n", "![all-results](./assets/all-results.png)\n", "\n", "- LLaMa-3.1-70B has no parsing errors, which is better than LLaMa-3.1-405B!\n", "- Both LLaMa-3.1-70B and LLaMa-3.1-405B performed better than GPT-4o, although by a fairly small margin.\n", "- LLaMa-3-70B is less than 25% the cost of GPT-4o, and is actually a bit better.\n"]}, {"cell_type": "markdown", "id": "53783a49-1c41-4668-92e3-142887534c00", "metadata": {}, "source": ["## Where to go from here\n", "\n", "In just a few minutes, we've cracked the code on how to perform tool calls with LLaMa-3.1 models and run a benchmark to compare their performance to GPT-4o. In doing so, we've\n", "found a few specific areas for improvement, e.g. parsing errors for tool calls, and a surprising outcome that LLaMa-3.1-70B is better than both LLaMa-3.1-405B and GPT-4o, yet a\n", "fraction of the cost.\n", "\n", "To explore this further, you could:\n", "\n", "- Expand the benchmark to measure other kinds of evaluators.\n", "- Try providing few-shot examples or fine-tuning the models to improve their performance.\n", "- Play with other models, like GPT-4o-mini or Claude to see how they compare.\n", "\n", "Happy evaluating!\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}