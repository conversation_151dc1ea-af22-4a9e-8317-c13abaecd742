{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating a prompt chaining agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prompt chaining systems coordinate LLMs to solve complex tasks through a series of smaller, specialized steps. Without careful evaluation, these systems can produce unpredictable results since small inaccuracies can compound across multiple steps. To produce reliable, production-ready agents, you need to understand exactly what's going on under the hood. In this cookbook, we'll demonstrate how to:\n", "\n", "1. <PERSON> and evaluate a complete end-to-end agent in Braintrust.\n", "2. Isolate and evaluate a particular step in the prompt chain to identify and measure issues.\n", "\n", "We’ll walk through a travel-planning agent that decides what actions to take (for example, calling a weather or flight API) and uses an LLM call evaluator (we'll call this the \"judge step\") to decide if each step is valid. As a final step, it produces an itinerary. We’ll do an end-to-end evaluation, then zoom in on the judge step to see how effectively it flags unnecessary actions.\n", "\n", "![diagram](./assets/cbdiagram.png)\n", "\n", "## Getting started\n", "\n", "To get started, you'll need [Braintrust](https://www.braintrust.dev/signup) and [OpenAI](https://platform.openai.com/) accounts, along with their corresponding API keys. Plug your OpenAI API key into your Braintrust account's [AI providers](https://www.braintrust.dev/app/settings?subroute=secrets) configuration. You can also add an API key for any other AI provider you'd like but be sure to change the code to use that model. Lastly, add your `BRAINTRUST_API_KEY` to your Python environment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_BRAINTRUST_API_KEY\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<Callout type=\"info\">\n", "Best practice is to export your API key as an environment variable. However, to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Start by installing the required Python dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install braintrust openai autoevals pydantic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we'll import all of the modules we need and initialize our OpenAI client. We're wrapping the client so that we have access to Braintrust features."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import random\n", "from datetime import datetime, timedelta\n", "from typing import Dict, Any, List, Optional, Union\n", "from pydantic import BaseModel, ValidationError, Field\n", "from enum import Enum\n", "\n", "import openai\n", "import braintrust\n", "import autoevals\n", "\n", "# Uncomment if you want to hardcode your API keys\n", "# BRAINTRUST_API_KEY=\"YOUR_BRAINTRUST_API_KEY\"\n", "\n", "BRAINTRUST_API_KEY = os.environ.get(\"BRAINTRUST_API_KEY\")\n", "\n", "client = braintrust.wrap_openai(\n", "    openai.OpenAI(\n", "        api_key=BRAINTRUST_API_KEY,\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define mock APIs\n", "\n", "For the purposes of this cookbook, we'll define placeholder “mock” APIs for weather and flight searches. In production applications, you'd call external services or databases. However, here we'll simulate dynamic outputs (randomly chosen weather, airfare prices, and seat availability) to confirm the agent logic works without external dependencies.\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["def get_future_date() -> str:\n", "    base = datetime(2025, 1, 23)\n", "    if random.random() < 0.7:\n", "        days_ahead = random.randint(1, 10)\n", "    else:\n", "        days_ahead = random.randint(11, 365)\n", "    return (base + timedelta(days=days_ahead)).strftime(\"%Y-%m-%d\")\n", "\n", "\n", "def mock_weather_api(city: str, date: str) -> Dict[str, Any]:\n", "    return {\n", "        \"condition\": random.choice([\"sunny\", \"rainy\", \"cloudy\"]),\n", "        \"temperature\": random.ran<PERSON><PERSON>(40, 95),\n", "        \"date\": date,\n", "    }\n", "\n", "\n", "def mock_flight_api(origin: str, destination: str) -> Dict[str, Any]:\n", "    return {\n", "        \"economy_price\": random.randint(200, 800),\n", "        \"business_price\": random.randint(800, 2000),\n", "        \"seats_left\": random.randint(0, 100),\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Schema definition and validation helpers\n", "\n", "To keep the agent's output consistent, we'll use a JSON schema enforced via `pydantic`. The agent can only return one of four actions: `GET_WEATHER`, `GET_FLIGHTS`, `GENERATE_ITINERARY`, or `DONE`. This constraint ensures we can reliably parse the agent’s response and handle it safely. "]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["class ActionEnum(str, Enum):\n", "    GET_WEATHER = \"GET_WEATHER\"\n", "    GET_FLIGHTS = \"GET_FLIGHTS\"\n", "    GENERATE_ITINERARY = \"GENERATE_ITINERARY\"\n", "    DONE = \"DONE\"\n", "\n", "\n", "class Parameters(BaseModel):\n", "    city: Union[str, None] = Field(..., nullable=True)\n", "    date: Union[str, None] = Field(..., nullable=True)\n", "    origin: Union[str, None] = Field(..., nullable=True)\n", "    destination: Union[str, None] = Field(..., nullable=True)\n", "\n", "    class Config:\n", "        # Disallow extra fields, as structured outputs also require no additionalProperties\n", "        extra = \"forbid\"\n", "\n", "\n", "class ActionStep(BaseModel):\n", "    action: ActionEnum\n", "    parameters: Parameters\n", "\n", "    class Config:\n", "        extra = \"forbid\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agent action validation \n", "\n", "The agent may propose actions that are unnecessary (for example, fetching weather repeatedly) or that contradict existing data. To solve this, we define an LLM call evaluator, or \"judge step,\" to validate each proposed step. For example, if the agent attempts to `GET_WEATHER` a second time for data that has already been fetched, the judge flags it, and then we prompt the LLM to fix it."]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["def judge_step_with_cot(\n", "    step_description: str, context_data: Dict[str, Any] = None\n", ") -> (bool, str):\n", "    with braintrust.start_span(name=\"judge_step\") as jspan:\n", "        context_snippet = \"\"\n", "        if context_data:\n", "            origin = context_data[\"input_data\"].get(\"origin\", \"\")\n", "            destination = context_data[\"input_data\"].get(\"destination\", \"\")\n", "            budget = context_data[\"input_data\"].get(\"budget\", \"\")\n", "            preferences = context_data[\"input_data\"].get(\"preferences\", {})\n", "            wdata = context_data[\"weather_data\"]\n", "            fdata = context_data[\"flight_data\"]\n", "\n", "            context_snippet = (\n", "                f\"Context:\\n\"\n", "                f\" - Origin: {origin}\\n\"\n", "                f\" - Destination: {destination}\\n\"\n", "                f\" - Budget: {budget}\\n\"\n", "                f\" - Preferences: {preferences}\\n\"\n", "                f\" - Known Weather: {json.dumps(wdata, indent=2)}\\n\"\n", "                f\" - Known Flight: {json.dumps(fdata, indent=2)}\\n\"\n", "            )\n", "\n", "        prompt_msg = f\"\"\"You are a strict judge of correctness in a travel-planning chain. \n", "Your task is to determine whether or not the next step is logically valid. \n", "Typically a valid step is if we do NOT already have that piece of data \n", "(e.g., fetching weather for an unknown city/date). If we already have that info, \n", "the step is invalid. If all data is known, generating the itinerary is valid.\n", "\n", "{context_snippet}\n", "\n", "Step description:\n", "\\\"\\\"\\\"\n", "{step_description}\n", "\\\"\\\"\\\"\n", "\n", "Provide a short chain-of-thought. \n", "Then end with: \"Final Decision: Y\" or \"Final Decision: N\"\n", "\"\"\"\n", "\n", "        try:\n", "            response = client.chat.completions.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\n", "                        \"role\": \"system\",\n", "                        \"content\": \"You are a meticulous correctness judge.\",\n", "                    },\n", "                    {\"role\": \"user\", \"content\": prompt_msg},\n", "                ],\n", "                temperature=0,\n", "            )\n", "            content = response.choices[0].message.content.strip()\n", "            jspan.log(metadata={\"raw_judge_response\": content})\n", "\n", "            lines = content.splitlines()\n", "            final_decision = \"N\"\n", "            rationale_lines = []\n", "            for line in lines:\n", "                if line.strip().startswith(\"Final Decision:\"):\n", "                    if \"Y\" in line.upper():\n", "                        final_decision = \"Y\"\n", "                    else:\n", "                        final_decision = \"N\"\n", "                else:\n", "                    rationale_lines.append(line)\n", "\n", "            rationale_text = \"\\n\".join(rationale_lines).strip()\n", "            is_ok = final_decision.upper() == \"Y\"\n", "            return is_ok, rationale_text\n", "\n", "        except Exception as e:\n", "            jspan.log(error=f\"Judge LLM error: {e}\")\n", "            return False, \"Error in judge <PERSON><PERSON>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Itinerary generation\n", "\n", "Once the agent gathers enough information, we expect a generated final itinerary. Below is a function that takes all the gathered data, such as user preferences, API responses, and budget details, and constructs a cohesive multi-day travel plan. The result is a textual trip description, including recommended accommodations, daily activities, or tips."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["def generate_final_itinerary(context: Dict[str, Any]) -> Optional[str]:\n", "    with braintrust.start_span(name=\"generate_itinerary\"):\n", "        input_data = context[\"input_data\"]\n", "        weather_data = context[\"weather_data\"]\n", "        flight_data = context[\"flight_data\"]\n", "\n", "        prompt = (\n", "            f\"Based on the data, generate a travel itinerary.\\n\\n\"\n", "            f\"Origin: {input_data['origin']}\\n\"\n", "            f\"Destination: {input_data['destination']}\\n\"\n", "            f\"Start Date: {input_data['start_date']}\\n\"\n", "            f\"Budget: {input_data['budget']}\\n\"\n", "            f\"Preferences: {json.dumps(input_data['preferences'])}\\n\\n\"\n", "            f\"Weather Data: {json.dumps(weather_data, indent=2)}\\n\"\n", "            f\"Flight Data: {json.dumps(flight_data, indent=2)}\\n\\n\"\n", "            \"Create a day-by-day plan, mention booking recs, accommodations, etc. \"\n", "            \"Use a helpful, concise style.\"\n", "        )\n", "        try:\n", "            response = client.chat.completions.create(\n", "                model=\"gpt-4o\",\n", "                messages=[\n", "                    {\"role\": \"system\", \"content\": \"You are a thorough travel planner.\"},\n", "                    {\"role\": \"user\", \"content\": prompt},\n", "                ],\n", "                temperature=0.3,\n", "            )\n", "            return response.choices[0].message.content.strip()\n", "        except Exception as e:\n", "            braintrust.current_span().log(error=f\"Error generating itinerary: {e}\")\n", "            return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deciding the \"next action\"\n", "\n", "Next, we create a system prompt that summarizes known data like weather and flights, and reiterates the JSON schema requirements. This ensures the agent doesn’t redundantly fetch data and responds in valid JSON."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["def generate_agent_prompt(context: Dict[str, Any]) -> str:\n", "    input_data = context[\"input_data\"]\n", "    weather_data = context[\"weather_data\"]\n", "    flight_data = context[\"flight_data\"]\n", "\n", "    system_instructions = (\n", "        \"You are an autonomous travel planning assistant.\\n\"\n", "        \"You MUST return valid JSON with this schema:\\n\"\n", "        \"  action: one of [GET_WEATHER, GET_FLIGHTS, GENERATE_ITINERARY, DONE]\\n\"\n", "        \"  parameters: { city: (string|null), date: (string|null), origin: (string|null), destination: (string|null) }\\n\\n\"\n", "        \"If you already have flight info, do not fetch flights again.\\n\"\n", "        \"If you already have weather for a given city/date, do not fetch it again.\\n\"\n", "        \"If you have all the data you need, use action=GENERATE_ITINERARY.\\n\"\n", "        \"If everything is complete/filled out, use action=DONE.\\n\"\n", "    )\n", "\n", "    user_prompt = \"Current Travel Context:\\n\"\n", "    user_prompt += f\" - Origin: {input_data['origin']}\\n\"\n", "    user_prompt += f\" - Destination: {input_data['destination']}\\n\"\n", "    user_prompt += f\" - Start Date: {input_data['start_date']}\\n\"\n", "    user_prompt += f\" - Budget: {input_data['budget']}\\n\"\n", "    user_prompt += f\" - Preferences: {json.dumps(input_data['preferences'])}\\n\\n\"\n", "\n", "    if weather_data:\n", "        user_prompt += f\"Weather Data: {json.dumps(weather_data, indent=2)}\\n\\n\"\n", "    if flight_data:\n", "        user_prompt += f\"Flight Data: {json.dumps(flight_data, indent=2)}\\n\\n\"\n", "\n", "    user_prompt += (\n", "        \"Reply with valid JSON only, no extra keys.\\n\"\n", "        \"Example:\\n\"\n", "        '{\"action\": \"GET_WEATHER\", \"parameters\": {\"city\": \"NYC\", \"date\": \"2025-02-02\", \"origin\": null, \"destination\": null}}\\n\\n'\n", "        \"What is your next step?\"\n", "    )\n", "\n", "    return system_instructions + \"\\n\" + user_prompt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The main agent loop\n", "\n", "Finally, we build the core loop that powers our entire travel planning agent. It runs for a given maximum number of iterations, performing the following steps each time:\n", "\n", "- **Prompt** the LLM for the next action.\n", "- **Validate** the JSON response against our schema.\n", "- **Judge** if the step is logical in context. If it fails, attempt to fix it.\n", "- **Execute** the step if valid (calling the mock weather/flight APIs).\n", "- If the agent indicates `GENERATE_ITINERARY`, produce the final itinerary and exit.\n", "\n", "By iterating until a final plan is reached (or until we exhaust retries), we create a semi-autonomous workflow that can correct missteps along the way."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["@braintrust.traced\n", "def agent_loop(client: openai.OpenAI, input_data: Dict[str, Any]) -> str:\n", "    context: Dict[str, Any] = {\n", "        \"input_data\": input_data,\n", "        \"weather_data\": {},\n", "        \"flight_data\": {},\n", "        \"itinerary\": None,\n", "        \"iteration_logs\": [],\n", "    }\n", "\n", "    max_iterations = 10\n", "    for iteration in range(1, max_iterations + 1):\n", "        with braintrust.start_span(\n", "            name=f\"travel_planning_iteration_{iteration}\"\n", "        ) as iter_span:\n", "            # 1) Build the prompt for the next action\n", "            llm_prompt = generate_agent_prompt(context)\n", "\n", "            # 2) Use structured outputs => parse to ActionStep\n", "            try:\n", "                response = client.beta.chat.completions.parse(\n", "                    model=\"gpt-4o-2024-08-06\",  # or updated gpt-4o\n", "                    messages=[{\"role\": \"system\", \"content\": llm_prompt}],\n", "                    response_format=ActionStep,\n", "                    temperature=0,\n", "                )\n", "            except Exception as e:\n", "                iter_span.log(error=f\"Error calling LLM parse: {e}\")\n", "                context[\"itinerary\"] = f\"Failed to parse LLM: {e}\"\n", "                break\n", "\n", "            action_msg = response.choices[0].message\n", "\n", "            # Check if model refused\n", "            if action_msg.refusal:\n", "                iter_span.log(error=f\"LLM refusal: {action_msg.refusal}\")\n", "                context[\"itinerary\"] = f\"LLM refused: {action_msg.refusal}\"\n", "                break\n", "\n", "            step = action_msg.parsed  # A validated ActionStep\n", "            action = step.action\n", "            parameters = step.parameters\n", "            step_desc = f\"Action={action}, Params={parameters}\"\n", "\n", "            # 3) Domain judge\n", "            is_ok, rationale = judge_step_with_cot(step_desc, context)\n", "            iteration_log = {\n", "                \"iteration\": iteration,\n", "                \"action\": action.value,\n", "                \"parameters\": parameters.dict(),\n", "                \"judge_decision\": \"Y\" if is_ok else \"N\",\n", "                \"judge_rationale\": rationale,\n", "            }\n", "            context[\"iteration_logs\"].append(iteration_log)\n", "\n", "            if not is_ok:\n", "                iter_span.log(\n", "                    error=\"Judge flagged an error => We'll just reprompt next iteration.\"\n", "                )\n", "                continue\n", "\n", "            # 4) Execute the action\n", "            if action == ActionEnum.GET_WEATHER:\n", "                if (parameters.city is None) or (parameters.date is None):\n", "                    iter_span.log(error=\"Missing city/date => re-iterate.\")\n", "                    continue\n", "                wdata = mock_weather_api(parameters.city, parameters.date)\n", "                context[\"weather_data\"][parameters.date] = wdata\n", "                iter_span.log(metadata={\"fetched_weather\": wdata})\n", "\n", "            elif action == ActionEnum.GET_FLIGHTS:\n", "                if (parameters.origin is None) or (parameters.destination is None):\n", "                    iter_span.log(error=\"Missing origin/dest => re-iterate.\")\n", "                    continue\n", "                fdata = mock_flight_api(parameters.origin, parameters.destination)\n", "                context[\"flight_data\"] = fdata\n", "                iter_span.log(metadata={\"fetched_flight\": fdata})\n", "\n", "            elif action == ActionEnum.GENERATE_ITINERARY:\n", "                itinerary = generate_final_itinerary(context)\n", "                context[\"itinerary\"] = itinerary or \"Failed to generate itinerary.\"\n", "                break\n", "\n", "            elif action == ActionEnum.DONE:\n", "                iter_span.log(metadata={\"status\": \"LLM indicated DONE\"})\n", "                break\n", "\n", "    final_data = {\n", "        \"final_itinerary\": context[\"itinerary\"],\n", "        \"iteration_logs\": context[\"iteration_logs\"],\n", "        \"input_data\": context[\"input_data\"],\n", "    }\n", "    return json.dumps(final_data, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation dataset\n", "\n", "Our workflow needs sample input data for testing. Below are three hardcoded test cases with different origins, destinations, budgets, and preferences. In a production application, you'd have a more extensive dataset of test cases."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["def dataset() -> List[braintrust.EvalCase]:\n", "    return [\n", "        braintrust.EvalCase(\n", "            input={\n", "                \"origin\": \"NYC\",\n", "                \"destination\": \"Miami\",\n", "                \"start_date\": get_future_date(),\n", "                \"budget\": \"high\",\n", "                \"preferences\": {\"activity_level\": \"high\", \"foodie\": True},\n", "            },\n", "        ),\n", "        braintrust.EvalCase(\n", "            input={\n", "                \"origin\": \"SFO\",\n", "                \"destination\": \"Seattle\",\n", "                \"start_date\": get_future_date(),\n", "                \"budget\": \"medium\",\n", "                \"preferences\": {\"activity_level\": \"low\"},\n", "            },\n", "        ),\n", "        braintrust.EvalCase(\n", "            input={\n", "                \"origin\": \"IAH\",\n", "                \"destination\": \"Paris\",\n", "                \"start_date\": get_future_date(),\n", "                \"budget\": \"low\",\n", "                \"preferences\": {\"activity_level\": \"low\"},\n", "            },\n", "        ),\n", "    ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Defining our scoring function\n", "\n", "For our scoring function, we implement a custom LLM-based correctness scorer that checks whether the final itinerary actually meets the user’s preferences. For instance, if the user wants a “high-activity trip,” but the final plan doesn’t suggest outdoor excursions or active elements, the scorer may judge that it’s missing key requirements."]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["judge_itinerary = autoevals.LLMClassifier(\n", "    name=\"LLM Itinerary Judge\",\n", "    prompt_template=(\n", "        \"User preferences: {{input.preferences}}\\n\\n\"\n", "        \"Here is the final itinerary:\\n{{output}}\\n\\n\"\n", "        \"Does this itinerary meet the user preferences? (Y/N)\\n\"\n", "        \"Provide a short chain-of-thought, then say 'Final: Y' or 'Final: N'.\\n\"\n", "    ),\n", "    choice_scores={\"Y\": 1.0, \"N\": 0.0},\n", "    use_cot=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the agent end-to-end\n", "\n", "For our end-to-end evaluation, we define a `chain_task` that calls `agent_loop()`, then run an eval. Because the `agent_loop()` is wrapped with `@braintrust.traced`, each iteration and sub-step gets logged in the Braintrust UI.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def chain_task(input_data: Dict[str, Any], hooks) -> str:\n", "    hooks.metadata[\"origin\"] = input_data[\"origin\"]\n", "    hooks.metadata[\"destination\"] = input_data[\"destination\"]\n", "    return agent_loop(client, input_data)\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    braintrust.Eval(\n", "        name=\"TravelPlannerDemo\",\n", "        data=dataset,\n", "        task=chain_task,\n", "        scores=[judge_itinerary],\n", "        experiment_name=\"end-to-end-eval\",\n", "        metadata={\"notes\": \"End to end evaluation of our travel planning agent\"},\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![end-to-end](./assets/e2e.png)\n", "\n", "Starting with this top-down approach is generally recommended because it allows you to spot where the chain is not performing as expected. The Braintrust UI allows you to click into any given component, and view information such as the prompt or metadata. Viewing each step can help decide which sub-process (weather fetch, flight fetch, or judge) might need a closer look or tuning. You would then run a separate evaluation on that component."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the judge step in isolation\n", "\n", "After evaluating the end-to-end performance of an agent, you might want to take a closer look at a single sub-process. For instance, if you notice that the agent frequently repeats certain actions when it shouldn’t, you might suspect the judge logic is misclassifying steps. To do this, we'll need to create a new experiment, a new dataset of test cases, and new scorers.\n", "\n", "<Callout type=\"info\">\n", "Depending on the complexity of your agent or how you like to organize your work in Braintrust, you can choose to create a new project for this evaluation instead of adding it to the existing project as we do here.\n", "</Callout>\n", "\n", "For demonstration purposes, we'll use a simple approach. We create a judge-only dataset, along with a minimal `judge_eval_task` that passes the sample inputs through `judge_step_with_cot()` and then compares the response to our expected label using a heuristic scorer called [`ExactMatch()`](https://github.com/braintrustdata/autoevals/blob/8b254b4e17897b7309bdc44880f55d3b88aa6744/py/autoevals/value.py#L9) from our built-in library of scoring functions, [autoevals](https://github.com/braintrustdata/autoevals)."]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["def dataset_judge_eval() -> List[braintrust.EvalCase]:\n", "    return [\n", "        braintrust.EvalCase(\n", "            input={\n", "                \"step_description\": \"Action=GET_WEATHER, Params={'city': 'NYC', 'date': '2025-02-01', 'origin': null, 'destination': null}\",\n", "                \"context_data\": {\n", "                    \"input_data\": {\n", "                        \"origin\": \"NYC\",\n", "                        \"destination\": \"Miami\",\n", "                        \"budget\": \"medium\",\n", "                        \"preferences\": {\"foodie\": True},\n", "                    },\n", "                    \"weather_data\": {},  # no weather => expect \"Y\"\n", "                    \"flight_data\": {},\n", "                },\n", "            },\n", "            expected=\"Y\",\n", "        ),\n", "        braintrust.EvalCase(\n", "            input={\n", "                \"step_description\": \"Action=GET_FLIGHTS, Params={'origin': 'NYC', 'destination': 'Miami', 'city': null, 'date': null}\",\n", "                \"context_data\": {\n", "                    \"input_data\": {\n", "                        \"origin\": \"NYC\",\n", "                        \"destination\": \"Miami\",\n", "                        \"budget\": \"low\",\n", "                        \"preferences\": {},\n", "                    },\n", "                    \"weather_data\": {},\n", "                    \"flight_data\": {\n", "                        \"economy_price\": 300,\n", "                        \"business_price\": 1200,\n", "                        \"seats_left\": 10,\n", "                    },\n", "                },\n", "            },\n", "            expected=\"N\",\n", "        ),\n", "    ]\n", "\n", "\n", "def judge_eval_task(inputs: Dict[str, Any], hooks) -> str:\n", "    step_desc = inputs[\"step_description\"]\n", "    context_data = inputs[\"context_data\"]\n", "    is_ok, _ = judge_step_with_cot(step_desc, context_data)\n", "    return \"Y\" if is_ok else \"N\"\n", "\n", "\n", "if __name__ == \"__main__\":\n", "\n", "    braintrust.Eval(\n", "        name=\"TravelPlannerDemo\",\n", "        data=dataset_judge_eval,\n", "        task=judge_eval_task,\n", "        scores=[autoevals.ExactMatch()],\n", "        experiment_name=\"judge-step-eval\",\n", "        metadata={\"notes\": \"Evaluating the judge_step_with_cot function in isolation.\"},\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After you run this evaluation, you can return to your original project in Braintrust. There, you'll see the new experiment for the judge step."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![homepage](./assets/homepage.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you select the experiment, you can see all of the different evaluations and summaries. You can also select an individual row to view a full trace, which includes the task function, metadata, and the scorers."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![judge-eval](./assets/judge.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps:\n", "\n", "- Learn more about [how to evaluate agents](https://www.braintrust.dev/blog/evaluating-agents)\n", "\n", "- Check out the [guide to what you should do after running an eval](https://www.braintrust.dev/blog/after-evals)\n", "\n", "- Try out another [agent cookbook](https://www.braintrust.dev/docs/cookbook/recipes/APIAgent-Py)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}