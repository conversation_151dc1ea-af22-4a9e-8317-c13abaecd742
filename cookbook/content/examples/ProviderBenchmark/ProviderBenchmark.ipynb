{"cells": [{"cell_type": "markdown", "id": "07c34ffc-a3a1-4915-b866-e32d97a98e7c", "metadata": {}, "source": ["# Benchmarking providers\n", "\n", "Although there are a small handful of open-source LLMs, there are a variety of inference providers that can host them for you, each with different cost,\n", "speed, and as we'll see below, accuracy trade-offs. And even if one provider excels at a certain model size, it may not be the best choice for another.\n", "\n", "## Key takeaways\n", "\n", "It's very important to evaluate your specific use case against a variety of both models and providers to make an informed decision about which to use.\n", "What I learned is that the results are pretty unpredictable and vary across both provider and model size. Just because one provider has a good 8b model,\n", "doesn't mean that its 405b is fast or accurate.\n", "\n", "Here are some things that surprised me:\n", "\n", "- **8b models are consistently fast, but have high variance in accuracy**\n", "- **One provider is fastest for 8b and 70b, yet slowest for 405b**\n", "- **The best provider is different across the two benchmarks we ran**\n", "\n", "Hopefully this analysis will help you create your own benchmarks and make an informed decision about which provider to use.\n"]}, {"cell_type": "markdown", "id": "78277a53-7763-41cc-aa51-89354e729875", "metadata": {}, "source": ["## Setup\n", "\n", "Before you get started, make sure you have a [Braintrust account](https://www.braintrust.dev/signup) and API keys for all the providers you want to test. Here, we're testing [Together](https://www.together.ai), [Fireworks](https://fireworks.ai/), and [Lepton](https://www.lepton.ai/), although Braintrust supports several others (including Azure, Bedrock, Groq, and more).\n", "\n", "Make sure to plug each provider's API key into your Braintrust account's [AI secrets](https://www.braintrust.dev/app/settings?subroute=secrets) configuration and acquire a [`BRAINTRUST_API_KEY`](https://www.braintrust.dev/app/settings?subroute=api-keys).\n", "\n", "Put your `BRAINTRUST_API_KEY` in a `.env.local` file next to this notebook, or just hardcode it into the code below.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "b8e621c8", "metadata": {}, "outputs": [], "source": ["import dotenv from \"dotenv\";\n", "import * as fs from \"fs\";\n", "\n", "if (fs.existsSync(\".env.local\")) {\n", "  dotenv.config({ path: \".env.local\", override: true });\n", "}\n"]}, {"cell_type": "markdown", "id": "fda1d6e8", "metadata": {}, "source": ["### Task code\n", "\n", "We are going to reuse the task function from [Tool calls in LLaMa 3.1](https://www.braintrust.dev/docs/cookbook/recipes/LLaMa-3_1-Tools), which is below. For a detailed explanation of the task, see that recipe.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ef910f35-4b64-403b-b5fb-7c3a9ebde8c1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(node:12633) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.\n", "(Use `node --trace-deprecation ...` to show where the warning was created)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: 'Factuality',\n", "  score: 0.6,\n", "  metadata: {\n", "    rationale: \"The submitted answer 'The weather in Tokyo is scorching' is a superset of the expert answer 'The weather in Tokyo is extremely hot' because it includes the same information and adds more detail. The word 'scorching' is a synonym for 'extremely hot', so the submitted answer is fully consistent with the expert answer.\",\n", "    choice: 'B'\n", "  }\n", "}\n"]}], "source": ["import { OpenAI } from \"openai\";\n", "import { wrapOpenAI } from \"braintrust\";\n", "\n", "import { templates } from \"autoevals\";\n", "import * as yaml from \"js-yaml\";\n", "import mustache from \"mustache\";\n", "\n", "const client = wrapOpenAI(\n", "  new OpenAI({\n", "    apiKey: process.env.BRAINTRUST_API_KEY,\n", "    baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "    defaultHeaders: { \"x-bt-use-cache\": \"never\" },\n", "  })\n", ");\n", "\n", "function parseToolResponse(response: string) {\n", "  const functionRegex = /<function=(\\w+)>(.*?)(?:<\\/function>|$)/;\n", "  const match = response.match(functionRegex);\n", "\n", "  if (match) {\n", "    const [, functionName, argsString] = match;\n", "    try {\n", "      const args = JSON.parse(argsString);\n", "      return {\n", "        functionName,\n", "        args,\n", "      };\n", "    } catch (error) {\n", "      console.error(\"Error parsing function arguments:\", error);\n", "      return null;\n", "    }\n", "  }\n", "\n", "  return null;\n", "}\n", "\n", "const template = yaml.load(templates[\"factuality\"]);\n", "\n", "const selectTool = {\n", "  name: \"select_choice\",\n", "  description: \"Call this function to select a choice.\",\n", "  parameters: {\n", "    properties: {\n", "      reasons: {\n", "        description:\n", "          \"Write out in a step by step manner your reasoning to be sure that your conclusion is correct. Avoid simply stating the correct answer at the outset.\",\n", "        title: \"Reasoning\",\n", "        type: \"string\",\n", "      },\n", "      choice: {\n", "        description: \"The choice\",\n", "        title: \"Choice\",\n", "        type: \"string\",\n", "        enum: Object.keys(template.choice_scores),\n", "      },\n", "    },\n", "    required: [\"reasons\", \"choice\"],\n", "    title: \"CoTResponse\",\n", "    type: \"object\",\n", "  },\n", "};\n", "\n", "async function LLaMaFactuality({\n", "  model,\n", "  input,\n", "  output,\n", "  expected,\n", "}: {\n", "  model: string;\n", "  input: string;\n", "  output: string;\n", "  expected: string;\n", "}) {\n", "  const toolPrompt = `You have access to the following functions:\n", "\n", "Use the function '${selectTool.name}' to '${selectTool.description}':\n", "${JSON.stringify(selectTool)}\n", "\n", "If you choose to call a function ONLY reply in the following format with no prefix or suffix:\n", "\n", "<function=example_function_name>{\"example_name\": \"example_value\"}</function>\n", "\n", "Reminder:\n", "- If looking for real time information use relevant functions before falling back to brave_search\n", "- Function calls MUST follow the specified format, start with <function= and end with </function>\n", "- Required parameters MUST be specified\n", "- Only call one function at a time\n", "- Put the entire function call reply on one line\n", "\n", "Here are a few examples:\n", "\n", "`;\n", "\n", "  const response = await client.chat.completions.create({\n", "    model,\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content: toolPrompt,\n", "      },\n", "      {\n", "        role: \"user\",\n", "        content: mustache.render(template.prompt, {\n", "          input,\n", "          output,\n", "          expected,\n", "        }),\n", "      },\n", "    ],\n", "    temperature: 0,\n", "    max_tokens: 2048,\n", "  });\n", "\n", "  try {\n", "    const parsed = parseToolResponse(response.choices[0].message.content);\n", "    return {\n", "      name: \"Factuality\",\n", "      score: template.choice_scores[parsed?.args.choice],\n", "      metadata: {\n", "        rationale: parsed?.args.reasons,\n", "        choice: parsed?.args.choice,\n", "      },\n", "    };\n", "  } catch (e) {\n", "    return {\n", "      name: \"Factuality\",\n", "      score: -1,\n", "      metadata: {\n", "        error: `${e}`,\n", "      },\n", "    };\n", "  }\n", "}\n", "\n", "console.log(\n", "  await LLaMaFactuality({\n", "    model: \"meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo\",\n", "    input: \"What is the weather in Tokyo?\",\n", "    output: \"The weather in Tokyo is scorching.\",\n", "    expected: \"The weather in Tokyo is extremely hot.\",\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "id": "0c179efc", "metadata": {}, "source": ["### Dataset\n", "\n", "We'll use the same data as well: a subset of the [CoQA](https://stanfordnlp.github.io/coqa/) dataset.\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c5bdca48", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLaMa-3.1-8B Factuality\n", "{\n", "  name: 'Factuality',\n", "  score: 0,\n", "  metadata: {\n", "    rationale: \"The submitted answer 'in a barn' does not contain the word 'white' which is present in the expert answer. Therefore, it is not a subset or superset of the expert answer. The submitted answer also does not contain all the same details as the expert answer. There is a disagreement between the submitted answer and the expert answer.\",\n", "    choice: 'D'\n", "  }\n", "}\n"]}], "source": ["interface CoqaCase {\n", "  input: {\n", "    input: string;\n", "    output: string;\n", "    expected: string;\n", "  };\n", "  expected: number;\n", "}\n", "\n", "const data: CoqaCase[] = JSON.parse(\n", "  fs.readFileSync(\"../LLaMa-3_1-Tools/coqa-factuality.json\", \"utf-8\")\n", ");\n", "\n", "console.log(\"LLaMa-3.1-8B Factuality\");\n", "console.log(\n", "  await LLaMaFactuality({\n", "    model: \"meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo\",\n", "    ...data[1].input,\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "id": "605f04ca", "metadata": {}, "source": ["## Running evals\n", "\n", "Let's create a list of the providers we want to evaluate. Each provider conveniently names its flavor of each model slightly differently, so we can use these as a unique identifier.\n", "\n", "To facilitate this test, we also self-hosted an official Meta-LLaMa-3.1-405B-Instruct-FP8 model, which is available on [Hugging Face](https://huggingface.co/meta-llama/Meta-Llama-3.1-405B-Instruct-FP8) using [vLLM](https://vllm.readthedocs.io/en/latest/). You can configure this model as a custom endpoint in Braintrust to use it alongside other providers.\n", "\n", "### Provider map\n"]}, {"cell_type": "code", "execution_count": null, "id": "5c70e172", "metadata": {}, "outputs": [], "source": ["const providers = [\n", "  {\n", "    provider: \"Provider 1\",\n", "    models: [\n", "      \"accounts/fireworks/models/llama-v3p1-8b-instruct\",\n", "      \"accounts/fireworks/models/llama-v3p1-70b-instruct\",\n", "      \"accounts/fireworks/models/llama-v3p1-405b-instruct\",\n", "    ],\n", "  },\n", "  {\n", "    provider: \"Provider 2\",\n", "    models: [\n", "      \"meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo\",\n", "      \"meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo\",\n", "      \"meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo\",\n", "    ],\n", "  },\n", "  {\n", "    provider: \"Provider 3\",\n", "    models: [\"llama3-1-8b\", \"llama3-1-70b\", \"llama3-1-405b\"],\n", "  },\n", "  {\n", "    provider: \"Self-hosted vLLM\",\n", "    models: [\"meta-llama/Meta-Llama-3.1-405B-Instruct-FP8\"],\n", "  },\n", "];\n"]}, {"cell_type": "markdown", "id": "aa33aa4d", "metadata": {}, "source": ["### Eval code\n", "\n", "We'll run each provider in parallel, and within the provider, we'll run each model in parallel. This roughly assumes that rate limits are per model, not per provider.\n", "\n", "We're also running with a low concurrency level (3) to avoid overwhelming a provider and hitting rate limits. The [Braintrust proxy](https://www.braintrust.dev/docs/guides/proxy) handles rate limits for us, but they are reflected in the final task duration.\n", "\n", "You'll also notice that we parse and track the provider as well as the model in each experiment's metadata. This allows us to do some rich analysis on the results.\n"]}, {"cell_type": "code", "execution_count": null, "id": "449cd41a", "metadata": {}, "outputs": [], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "import { Score, NumericDiff } from \"autoevals\";\n", "\n", "function NonNull({ output }: { output: number | null }) {\n", "  return output !== null && output !== undefined ? 1 : 0;\n", "}\n", "\n", "async function CorrectScore({\n", "  output,\n", "  expected,\n", "}: {\n", "  output: number | null;\n", "  expected: number | null;\n", "}): Promise<Score> {\n", "  if (output === null || expected === null) {\n", "    return {\n", "      name: \"CorrectScore\",\n", "      score: 0,\n", "      metadata: {\n", "        error: output === null ? \"output is null\" : \"expected is null\",\n", "      },\n", "    };\n", "  }\n", "  return {\n", "    ...(await NumericDiff({ output, expected })),\n", "    name: \"CorrectScore\",\n", "  };\n", "}\n", "\n", "async function runProviderBenchmark(provider: (typeof providers)[number]) {\n", "  const evals = [];\n", "  for (const model of provider.models) {\n", "    const size = model.toLowerCase().includes(\"8b\")\n", "      ? \"8b\"\n", "      : model.toLowerCase().includes(\"70b\")\n", "        ? \"70b\"\n", "        : \"405b\";\n", "\n", "    evals.push(\n", "      Eval(\"LLaMa-3.1-Multi-Provider-Benchmark\", {\n", "        data: data,\n", "        task: async (input) =>\n", "          (await LLaMaFactuality({ model, ...input }))?.score,\n", "        scores: [<PERSON><PERSON><PERSON><PERSON><PERSON>, NonNull],\n", "        metadata: {\n", "          size,\n", "          provider: provider.provider,\n", "          model,\n", "        },\n", "        experimentName: `${provider.provider} (${size})`,\n", "        maxConcurrency: 3,\n", "        trialCount: 3,\n", "      })\n", "    );\n", "  }\n", "  await Promise.all(evals);\n", "}\n", "\n", "await Promise.all(providers.map(runProviderBenchmark));\n"]}, {"cell_type": "markdown", "id": "5576e43a", "metadata": {}, "source": ["## Results\n", "\n", "Let's start by looking at the project view. Braintrust makes it easy to morph this into a multi-level grouped analysis where we can see the score vs. duration in a scatter plot, and how each provider stacks up in the table.\n", "\n", "![Setting up the table](./assets/configuring-graph.gif)\n", "\n", "### Insights\n", "\n", "Now let's dig into this chart and see what we can learn.\n", "\n", "1. **70b hits a nice sweet spot**\n", "\n", "It looks like on average, each weight class costs you an extra second on average. However, the jump in average accuracy from 8b to 70b is 16%+ while\n", "70b to 405b is only 2.87%.\n", "\n", "![Pivot table](./assets/aggregate-tradeoff.png)\n", "\n", "2. **8b models are consistently really fast, but some providers' 70b models are slower than others'**\n", "\n", "The distribution among providers for 8b latency is very tight, but that starts to change with 70b and even more so with 405b models.\n", "\n", "![Speed distribution](./assets/speed-variance.png)\n", "\n", "3. **High accuracy variance in 8b models**\n", "\n", "Within 8b models in particular, there is a pretty significant difference in accuracy\n", "\n", "![Accuracy distribution](./assets/performance-variance-8b.png)\n", "\n", "4. **Provider 1 is the fastest except for 405b**\n", "\n", "![Provider 1](./assets/provider-1-insight.png)\n", "\n", "Interestingly, provider 1's 8b model is both the fastest and most accurate. However, its 405b model, while accurate, is the slowest by far. This is likely due to\n", "rate limits, or perhaps they have optimized it using a different method.\n", "\n", "5. **Self-hosting strikes a nice balance**\n", "\n", "Self-hosting strikes a nice balance between latency and quality (note: we only tested self-hosted 405b). Of course, this comes at a price -- around $27/hour using [Lambda Labs](https://lambdalabs.com/)\n", "\n", "![Self-hosted](./assets/self-hosted.png)\n", "\n", "### Another benchmark\n", "\n", "We also used roughly the same code on a different, more-realistic, internal benchmark which measures how well our [AI search](https://www.braintrust.dev/docs/cookbook/recipes/AISearch) bar works. Here is the same\n", "visualization for that benchmark:\n", "\n", "![AISearch](./assets/ai-search.png)\n", "\n", "As you can see, certain things are consistent, but others are not. Again, this highlights how important it is to run this analysis on your own use case.\n", "\n", "- **Provider 1 is less differentiated**. Although Provider 1 is still the fastest, it comes at the cost of accuracy in the 70b and 405b classes, where Provider 2 wins on accuracy. Provider 2 also wins on speed for 405b.\n", "- **Provider 3 has a hard time in the 70b class**. This workload is heavy on prompt tokens (~3500 per test case). Maybe that has something to do with it?\n", "- **More latency variance across the board**. Again, this may have to do with the significant jump in prompt tokens.\n", "- **Self-hosted seems to be about the same**. Interestingly, the self-hosted model appears at about the same spot in the graph!\n"]}, {"cell_type": "markdown", "id": "53783a49-1c41-4668-92e3-142887534c00", "metadata": {}, "source": ["## Where to go from here\n", "\n", "This is just one benchmark, but as you can see, there is a pretty significant difference in speed and accuracy between providers. I'd highly encourage testing\n", "on your own workload and using a tool like [Braintrust](https://www.braintrust.dev) to help you construct a good eval and understand the trade-offs across providers\n", "in depth.\n", "\n", "Feel free to [reach out](mailto:<EMAIL>) if we can help, or feel free to [sign up](https://www.braintrust.dev/signup) to try out Braintrust for yourself.\n", "If you enjoy performing this kind of analysis, we are [hiring](https://www.braintrust.dev/careers).\n", "\n", "Happy evaluating!\n", "\n", "_Thanks to [<PERSON><PERSON>](https://x.com/HamelHusain) for hosting the self-hosted model and feedback on drafts._\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}