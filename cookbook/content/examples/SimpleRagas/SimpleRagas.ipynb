{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Optimizing Ragas to evaluate a RAG pipeline\n", "\n", "Ragas is a popular framework for evaluating Retrieval Augmented Generation (RAG) applications. Braintrust natively supports the [Ragas](https://arxiv.org/abs/2309.15217) metrics, with several improvements to aid debugging and accuracy, in our open source [`autoevals`](https://github.com/braintrustdata/autoevals) library.\n", "\n", "In this cookbook, we'll walk through using a few Ragas metrics to evaluate a simple RAG pipeline that does Q&A on [Coda's help desk](https://coda.io/). We'll reuse many of the components we built in a [previous cookbook](https://www.braintrust.dev/docs/cookbook/CodaHelpDesk) on RAG, which\n", "you can check out to learn some of the basics around evaluating RAG systems.\n", "\n", "Let's dive in and start by installing dependencies:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -U autoevals braintrust openai scipy lancedb markdownify --quiet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting up the RAG application\n", "\n", "We'll quickly set up a full end-to-end RAG application, based on our earlier [cookbook](https://www.braintrust.dev/docs/cookbook/CodaHelpDesk). We use the Coda Q&A dataset, LanceDB for our vector database, and OpenAI's embedding model.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/projects/braintrust/cookbook/content/examples/SimpleRagas/.venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/plain": ["20"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import asyncio\n", "import os\n", "import re\n", "import tempfile\n", "\n", "import braintrust\n", "import lancedb\n", "import markdownify\n", "import openai\n", "import requests\n", "\n", "NUM_SECTIONS = 20\n", "CODA_QA_FILE_LOC = \"https://gist.githubusercontent.com/wong-codaio/b8ea0e087f800971ca5ec9eef617273e/raw/39f8bd2ebdecee485021e20f2c1d40fd649a4c77/articles.json\"\n", "\n", "braintrust.login(\n", "    api_key=os.environ.get(\"BRAINTRUST_API_KEY\", \"Your BRAINTRUST_API_KEY here\")\n", ")\n", "\n", "openai_client = braintrust.wrap_openai(\n", "    openai.AsyncOpenAI(\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "        default_headers={\"x-bt-use-cache\": \"always\"},\n", "        api_key=os.environ.get(\"OPENAI_API_KEY\", \"Your OPENAI_API_KEY here\"),\n", "    )\n", ")\n", "\n", "coda_qa_content_data = requests.get(CODA_QA_FILE_LOC).json()\n", "\n", "markdown_sections = [\n", "    {\"doc_id\": row[\"id\"], \"markdown\": section.strip()}\n", "    for row in coda_qa_content_data\n", "    for section in re.split(r\"(.*\\n=+\\n)\", markdownify.markdownify(row[\"body\"]))\n", "    if section.strip() and not re.match(r\".*\\n=+\\n\", section)\n", "]\n", "\n", "\n", "LANCE_DB_PATH = os.path.join(tempfile.TemporaryDirectory().name, \"docs-lancedb\")\n", "\n", "\n", "@braintrust.traced\n", "async def embed_text(text: str):\n", "    params = dict(input=text, model=\"text-embedding-3-small\")\n", "    response = await openai_client.embeddings.create(**params)\n", "    embedding = response.data[0].embedding\n", "    return embedding\n", "\n", "\n", "embeddings = await asyncio.gather(\n", "    *(embed_text(section[\"markdown\"]) for section in markdown_sections)\n", ")\n", "\n", "db = lancedb.connect(LANCE_DB_PATH)\n", "table = db.create_table(\n", "    \"sections\",\n", "    data=[\n", "        {\n", "            \"doc_id\": row[\"doc_id\"],\n", "            \"section_id\": i,\n", "            \"markdown\": row[\"markdown\"],\n", "            \"vector\": embedding,\n", "        }\n", "        for i, (row, embedding) in enumerate(\n", "            zip(markdown_sections[:NUM_SECTIONS], embeddings)\n", "        )\n", "    ],\n", ")\n", "\n", "table.count_rows()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Done! Next, we'll write some simple, framework-free code to (a) retrieve relevant documents and (b) generate an answer given those documents.\n", "\n", "### Retrieving documents\n", "\n", "To perform retrieval, we'll use the same embedding model as we did for the document sections to embed the `input` query, and then search for the\n", "`TOP_K` (2) most relevant documents.\n", "\n", "You'll notice that here and elsewhere we've decorated functions with `@braintrust.traced`. For now, it's a no-op, but we'll see shortly how `@braintrust.traced`\n", "helps us trace python functions and debug them in Braintrust.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "from typing import Iterable, List\n", "\n", "QA_ANSWER_MODEL = \"gpt-3.5-turbo\"\n", "TOP_K = 2\n", "\n", "\n", "@braintrust.traced\n", "async def fetch_top_k_relevant_sections(input: str) -> List[str]:\n", "    embedding = await embed_text(input)\n", "    results = table.search(embedding).limit(TOP_K).to_arrow().to_pylist()\n", "    return [result[\"markdown\"] for result in results]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try it out on a simple question, and take a look at the retrieved documents:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----\n", "Not all Coda docs are used in the same way. You'll inevitably have a few that you use every week, and some that you'll only use once. This is where starred docs can help you stay organized.\n", "\n", "\n", "\n", "Starring docs is a great way to mark docs of personal importance. After you star a doc, it will live in a section on your doc list called **[My Shortcuts](https://coda.io/shortcuts)**. All starred docs, even from multiple different workspaces, will live in this section.\n", "\n", "\n", "\n", "Starring docs only saves them to your personal My Shortcuts. It doesn’t affect the view for others in your workspace. If you’re wanting to shortcut docs not just for yourself but also for others in your team or workspace, you’ll [use pinning](https://help.coda.io/en/articles/2865511-starred-pinned-docs) instead.\n", "\n", "\n"]}], "source": ["question = (\n", "    \"What impact does starring a document have on other workspace members in Coda?\"\n", ")\n", "\n", "relevant_sections = await fetch_top_k_relevant_sections(question)\n", "\n", "for section in relevant_sections:\n", "    print(\"----\")\n", "    print(section)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating the final answer\n", "\n", "To generate the final answer, we can simply pass in the retrieved documents and the original question to a simple prompt defined below. Feel free to tweak this prompt as you experiment!\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["@braintrust.traced\n", "async def generate_answer_from_docs(question: str, relevant_sections: Iterable[str]):\n", "    context = \"\\n\\n\".join(relevant_sections)\n", "    completion = await openai_client.chat.completions.create(\n", "        model=QA_ANSWER_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": dedent(\n", "                    f\"\"\"\\\n", "            Given the following context\n", "            {context}\n", "            Please answer the following question:\n", "            Question: {question}\n", "            \"\"\"\n", "                ),\n", "            }\n", "        ],\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starring a document in Coda only affects the individual who starred it. It does not impact other workspace members as the starred document will only appear in the individual's personal My Shortcuts section. It is a way to mark documents of personal importance for easy access.\n"]}], "source": ["answer = await generate_answer_from_docs(question, relevant_sections)\n", "\n", "print(answer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Combining retrieval and generation\n", "\n", "We'll define a convenience function to combine these two steps, and return both the final answer and the retrieved documents so we can observe if we picked useful documents! (Later, returning documents will come in useful for evaluations)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starring a document in Coda only affects the individual who starred it. It does not impact other workspace members as the starred document will only appear in the individual's personal My Shortcuts section. It is a way to mark documents of personal importance for easy access.\n"]}], "source": ["@braintrust.traced\n", "async def generate_answer_e2e(question: str):\n", "    retrieved_content = await fetch_top_k_relevant_sections(question)\n", "    answer = await generate_answer_from_docs(question, retrieved_content)\n", "    return dict(answer=answer, retrieved_docs=retrieved_content)\n", "\n", "\n", "e2e_answer = await generate_answer_e2e(question)\n", "print(e2e_answer[\"answer\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Perfect! Now that we have the whole system working, we can compute Ragas metrics and try a couple improvements.\n", "\n", "## Baseline Ragas metrics with autoevals\n", "\n", "To get a large enough sample size for evaluations, we're going to use the synthetic test questions we generated in [our earlier cookbook](https://www.braintrust.dev/docs/cookbook/CodaHelpDesk). Feel free to check out that cookbook for details on how the synthetic data generation process works.\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': 'What is the purpose of starred docs in Coda?',\n", " 'expected': 'Starring docs in Coda helps to mark documents of personal importance and organizes them in a specific section called My Shortcuts for easy access.',\n", " 'metadata': {'document_id': '8179780',\n", "  'section_id': 0,\n", "  'question_idx': 0,\n", "  'answer_idx': 0,\n", "  'id': 0,\n", "  'split': 'train'}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "CODA_QA_PAIRS_LOC = \"https://gist.githubusercontent.com/nelsonauner/2ef4d38948b78a9ec2cff4aa265cff3f/raw/c47306b4469c68e8e495f4dc050f05aff9f997e1/qa_pairs_coda_data.jsonl\"\n", "\n", "\n", "coda_qa_pairs = requests.get(CODA_QA_PAIRS_LOC)\n", "qa_pairs = [json.loads(line) for line in coda_qa_pairs.text.split(\"\\n\") if line]\n", "qa_pairs[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ragas provides a [variety of metrics](https://docs.ragas.io/en/stable/concepts/metrics/index.html), but for the purposes of this guide, we'll show you how to calculate two scores we've found to be useful:\n", "\n", "- `ContextRecall` compares the retrieved context to the information in the ground truth answer. This is a helpful way of testing how relevant the retrieved documents are with respect to the answer itself.\n", "- `AnswerCorrectness` evaluates the generated answer to the golden answer. Under the hood, it checks each statement in the answer and classifies it as a true positive, false positive, or false negative.\n", "\n", "Before we calculate metrics, we'll write a short wrapper class that splits the returned output and context into two arguments that our Ragas evaluator classes can easily ingest.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now we can run our evaluation!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment RAG gpt-3.5-turbo is running at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/RAG%20gpt-3.5-turbo\n", "Rag Metrics with Ragas [experiment_name=RAG gpt-3.5-turbo] (data): 20it [00:00, 51941.85it/s]\n", "Rag Metrics with Ra<PERSON> [experiment_name=RAG gpt-3.5-turbo] (tasks): 100%|██████████| 20/20 [00:01<00:00, 10.48it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "95.00% 'ContextRecall'     score\n", "67.28% 'AnswerCorrectness' score\n", "\n", "1.58s duration\n", "\n", "See results for RAG gpt-3.5-turbo at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/RAG%20gpt-3.5-turbo\n"]}], "source": ["from braintrust import EvalAsync\n", "\n", "from autoevals import AnswerCorrectness, ContextRecall\n", "\n", "\n", "# Wrap ContextRecall() to propagate along the \"answer\" and \"context\" values separately\n", "async def context_recall(output, **kwargs):\n", "    return await ContextRecall().eval_async(\n", "        output=output[\"answer\"], context=output[\"retrieved_docs\"], **kwargs\n", "    )\n", "\n", "\n", "async def answer_correctness(output, **kwargs):\n", "    return await AnswerCorrectness().eval_async(output=output[\"answer\"], **kwargs)\n", "\n", "\n", "eval_result = await EvalAsync(\n", "    name=\"<PERSON><PERSON> with Ragas\",\n", "    experiment_name=f\"RAG {QA_ANSWER_MODEL}\",\n", "    data=qa_pairs[:NUM_SECTIONS],\n", "    task=generate_answer_e2e,\n", "    scores=[context_recall, answer_correctness],\n", "    metadata=dict(model=QA_ANSWER_MODEL, topk=TOP_K),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Not bad! It looks like we're doing really well on context recall, but worse on the final answer's correctness.\n", "\n", "### Interpreting the results in Braintrust\n", "\n", "Although Ragas is very powerful, it can be difficult to get detailed insight into low scoring values. Braintrust makes that very simple.\n", "\n", "Sometimes an average of 67% means that 2/3 of the values had a score of 1 and 1/3 had a score of 0. However, the distribution chart makes it clear\n", "that in our case, many of the scores are partially correct:\n", "\n", "![distribution chart](./assets/distribution_chart.png)\n", "\n", "Now, let's dig into one of these records. Braintrust allows us to see all the raw outputs from the constituent pieces:\n", "\n", "![constituent pieces](./assets/constituent_pieces.png)\n", "\n", "To me, this looks like it might be an error in the scoring function itself. `No, starring a doc in Coda does not affect other users` seems like a true, not false, positive.\n", "Let's try changing the scoring model for `AnswerCorrectness` to be `gpt-4`, and see if that changes anything.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Swapping grading model\n", "\n", "By default, Ragas is configured to use `gpt-3.5-turbo-16k`. As we observed, it looks like the `AnswerCorrectness` score may be returning bogus\n", "results, and maybe we should try using `gpt-4` instead. Braintrust lets us test the effect of this quickly, directly in the UI, before we run\n", "a full experiment:\n", "\n", "![try gpt-4](./assets/try-gpt-4.gif)\n", "\n", "Looks better. Let's update our scoring function to use it and re-run the experiment.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Score with gpt-4 is running at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/Score%20with%20gpt-4\n", "Rag Metrics with Ragas [experiment_name=Score with gpt-4] (data): 20it [00:00, 19864.10it/s]\n", "Rag Metrics with <PERSON><PERSON> [experiment_name=Score with gpt-4] (tasks): 100%|██████████| 20/20 [00:07<00:00,  2.71it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Score with gpt-4 compared to RAG gpt-3.5-turbo:\n", "72.10% (+04.83%) 'AnswerCorrectness' score\t(10 improvements, 4 regressions)\n", "95.00% (-) 'ContextRecall'     score\t(0 improvements, 0 regressions)\n", "\n", "4.67s (+309.08%) 'duration'\t(0 improvements, 20 regressions)\n", "\n", "See results for Score with gpt-4 at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/Score%20with%20gpt-4\n"]}], "source": ["# Wrap ContextRecall() to propagate along the \"answer\" and \"context\" values separately\n", "async def context_recall(output, **kwargs):\n", "    return await ContextRecall().eval_async(\n", "        output=output[\"answer\"], context=output[\"retrieved_docs\"], **kwargs\n", "    )\n", "\n", "\n", "async def answer_correctness(output, **kwargs):\n", "    return await AnswerCorrectness(model=\"gpt-4\").eval_async(\n", "        output=output[\"answer\"], **kwargs\n", "    )\n", "\n", "\n", "eval_result = await EvalAsync(\n", "    name=\"<PERSON><PERSON> with Ragas\",\n", "    experiment_name=f\"Score with gpt-4\",\n", "    data=qa_pairs[:NUM_SECTIONS],\n", "    task=generate_answer_e2e,\n", "    scores=[context_recall, answer_correctness],\n", "    metadata=dict(model=QA_ANSWER_MODEL, topk=TOP_K),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Great, it looks like changing our grading model improved the answer correctness score for the same set of questions:\n", "\n", "![score progression](./assets/score_progression.png)\n", "\n", "### Optimizing document retrieval\n", "\n", "Now, let's see if we can further optimize our RAG pipeline without regressing scores. We're going to try pulling just one document, rather than two.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment TOP_K=1 is running at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/TOP_K%3D1\n", "Rag Metrics with Ragas [experiment_name=TOP_K=1] (data): 20it [00:00, 99039.06it/s]\n", "Rag Metrics with <PERSON><PERSON> [experiment_name=TOP_K=1] (tasks): 100%|██████████| 20/20 [00:01<00:00, 11.07it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "TOP_K=1 compared to Score with gpt-4:\n", "97.29% (+02.29%) 'ContextRecall'     score\t(1 improvements, 3 regressions)\n", "71.99% (-00.12%) 'AnswerCorrectness' score\t(9 improvements, 11 regressions)\n", "\n", "1.56s (-311.18%) 'duration'\t(20 improvements, 0 regressions)\n", "\n", "See results for TOP_K=1 at https://www.braintrust.dev/app/braintrustdata.com/p/Rag%20Metrics%20with%20Ragas/experiments/TOP_K%3D1\n"]}], "source": ["TOP_K = 1\n", "\n", "eval_result = await EvalAsync(\n", "    name=\"<PERSON><PERSON> with Ragas\",\n", "    experiment_name=f\"TOP_K={TOP_K}\",\n", "    data=qa_pairs[:NUM_SECTIONS],\n", "    task=generate_answer_e2e,\n", "    scores=[context_recall, answer_correctness],\n", "    metadata=dict(model=QA_ANSWER_MODEL, topk=TOP_K),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Although not a pure fail, it does seem like in 3 cases we're not retrieving the right documents anymore, and 11 cases had worse results.\n", "\n", "![topk1](./assets/topk1.png)\n", "\n", "We can drill down on individual examples of each regression type to better understand it. The side-by-side diffs built into Braintrust make\n", "it easy to deeply understand every step of the pipeline, for example, which documents were missing, and why.\n", "\n", "![missing docs](./assets/missing-docs.gif)\n", "\n", "And there you have it! Ragas is a powerful technique, that with the right tools and iteration can lead to really high quality RAG applications. Happy evaling!\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}