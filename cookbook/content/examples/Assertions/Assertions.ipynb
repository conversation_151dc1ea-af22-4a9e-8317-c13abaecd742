{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using Assertions to validate complex chatbot criteria\n", "\n", "[Zapier](https://zapier.com/) is the #1 workflow automation platform for small and midsize businesses, connecting to more than 6000 of the most popular work apps. We were also one of the first companies to build and ship AI features into our core products. We've had the opportunity to work with Braintrust since the early days of the product, which now powers the evaluation and observability infrastructure across our AI features.\n", "\n", "One of the most powerful features of Zapier is the wide range of integrations that we support. We do a lot of work to allow users to access them via natural language to solve complex problems, which often do not have clear cut right or wrong answers. Instead, we define a set of criteria that need to be met (assertions). Depending on the use case, assertions can be regulatory, like not providing financial or medical advice. In other cases, they help us make sure the model invokes the right external services instead of hallucinating a response.\n", "\n", "By implementing assertions and evaluating them in Braintrust, we've seen a 60%+ improvement in our quality metrics. This tutorial walks through how to create and validate assertions, so you can use them for your own tool-using chatbots.\n", "\n", "## Initial setup\n", "\n", "We're going to create a chatbot that has access to a single tool, _weather lookup_, and throw a series of questions at it. Some questions will involve the weather and others won't. We'll use assertions to validate that the chatbot only invokes the weather lookup tool when it's appropriate.\n", "\n", "Let's create a simple request handler and hook up a weather tool to it.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import { wrapOpenAI } from \"braintrust\";\n", "import pick from \"lodash/pick\";\n", "import { ChatCompletionTool } from \"openai/resources/chat/completions\";\n", "import OpenAI from \"openai\";\n", "import { z } from \"zod\";\n", "import zodToJsonSchema from \"zod-to-json-schema\";\n", "\n", "// This wrap function adds some useful tracing in Braintrust\n", "const openai = wrapOpenAI(new OpenAI());\n", "\n", "// Convenience function for defining an OpenAI function call\n", "const makeFunctionDefinition = (\n", "  name: string,\n", "  description: string,\n", "  schema: <PERSON>.<PERSON>\n", "): ChatCompletionTool => ({\n", "  type: \"function\",\n", "  function: {\n", "    name,\n", "    description,\n", "    parameters: {\n", "      type: \"object\",\n", "      ...pick(\n", "        zodToJsonSchema(schema, {\n", "          name: \"root\",\n", "          $refStrategy: \"none\",\n", "        }).definitions?.root,\n", "        [\"type\", \"properties\", \"required\"]\n", "      ),\n", "    },\n", "  },\n", "});\n", "\n", "const weatherTool = makeFunctionDefinition(\n", "  \"weather\",\n", "  \"Look up the current weather for a city\",\n", "  z.object({\n", "    city: z.string().describe(\"The city to look up the weather for\"),\n", "    date: z.string().optional().describe(\"The date to look up the weather for\"),\n", "  })\n", ");\n", "\n", "// This is the core \"workhorse\" function that accepts an input and returns a response\n", "// which optionally includes a tool call (to the weather API).\n", "async function task(input: string) {\n", "  const completion = await openai.chat.completions.create({\n", "    model: \"gpt-3.5-turbo\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content: `You are a highly intelligent AI that can look up the weather.`,\n", "      },\n", "      { role: \"user\", content: input },\n", "    ],\n", "    tools: [weatherTool],\n", "    max_tokens: 1000,\n", "  });\n", "\n", "  return {\n", "    responseChatCompletions: [completion.choices[0].message],\n", "  };\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's try it out on a few examples!\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"responseChatCompletions\": [\n", "    {\n", "      \"role\": \"assistant\",\n", "      \"content\": null,\n", "      \"tool_calls\": [\n", "        {\n", "          \"id\": \"call_vlOuDTdxGXurjMzy4VDFHGBS\",\n", "          \"type\": \"function\",\n", "          \"function\": {\n", "            \"name\": \"weather\",\n", "            \"arguments\": \"{\\n  \\\"city\\\": \\\"San Francisco\\\"\\n}\"\n", "          }\n", "        }\n", "      ]\n", "    }\n", "  ]\n", "}\n"]}], "source": ["JSON.stringify(await task(\"What's the weather in San Francisco?\"), null, 2);\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"responseChatCompletions\": [\n", "    {\n", "      \"role\": \"assistant\",\n", "      \"content\": \"I'm sorry, but I can't provide you with your bank balance. You will need to check with your bank directly for that information.\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["JSON.stringify(await task(\"What is my bank balance?\"), null, 2);\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"responseChatCompletions\": [\n", "    {\n", "      \"role\": \"assistant\",\n", "      \"content\": \"I need more information to provide you with the weather. Could you please specify the city and the date for which you would like to know the weather?\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["JSON.stringify(await task(\"What is the weather?\"), null, 2);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scoring outputs\n", "\n", "Validating these cases is subtle. For example, if someone asks \"What is the weather?\", the correct answer is to ask for clarification. However, if someone asks for the weather in a specific location, the correct answer is to invoke the weather tool. How do we validate these different types of responses?\n", "\n", "### Using assertions\n", "\n", "Instead of trying to score a specific response, we'll use a technique called _assertions_ to validate certain criteria about a response. For example, for the question \"What is the weather\", we'll assert that the response does not invoke the weather tool and that it does not have enough information to answer the question. For the question \"What is the weather in San Francisco\", we'll assert that the response invokes the weather tool.\n", "\n", "### Assertion types\n", "\n", "Let's start by defining a few assertion types that we'll use to validate the chatbot's responses.\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["type AssertionTypes =\n", "  | \"equals\"\n", "  | \"exists\"\n", "  | \"not_exists\"\n", "  | \"llm_criteria_met\"\n", "  | \"semantic_contains\";\n", "\n", "type Assertion = {\n", "  path: string;\n", "  assertion_type: AssertionTypes;\n", "  value: string;\n", "};\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`equals`, `exists`, and `not_exists` are heuristics. `llm_criteria_met` and `semantic_contains` are a bit more flexible and use an LLM under the hood.\n", "\n", "Let's implement a scoring function that can handle each type of assertion.\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import { ClosedQA } from \"autoevals\";\n", "import get from \"lodash/get\";\n", "import every from \"lodash/every\";\n", "\n", "/**\n", " * Uses an LLM call to classify if a substring is semantically contained in a text.\n", " * @param text The full text you want to check against\n", " * @param needle The string you want to check if it is contained in the text\n", " */\n", "async function semanticContains({\n", "  text1,\n", "  text2,\n", "}: {\n", "  text1: string;\n", "  text2: string;\n", "}): Promise<boolean> {\n", "  const system = `\n", "  You are a highly intelligent AI. You will be given two texts, TEXT_1 and TEXT_2. Your job is to tell me if TEXT_2 is semantically present in TEXT_1.\n", "  Examples:\n", "  \\`\\`\\`\n", "  TEXT_1: \"I've just sent “hello world” to the #testing channel on Slack as you requested. Can I assist you with anything else?\"\n", "  TEXT_2: \"Can I help you with something else?\"\n", "  Result: YES\n", "  \\`\\`\\`\n", "  \n", "  \\`\\`\\`\n", "  TEXT_1: \"I've just sent “hello world” to the #testing channel on Slack as you requested. Can I assist you with anything else?\"\n", "  TEXT_2: \"Sorry, something went wrong.\"\n", "  Result: NO\n", "  \\`\\`\\`\n", "  \n", "  \\`\\`\\`\n", "  TEXT_1: \"I've just sent “hello world” to the #testing channel on Slack as you requested. Can I assist you with anything else?\"\n", "  TEXT_2: \"#testing channel Slack\"\n", "  Result: YES\n", "  \\`\\`\\`\n", "  \n", "  \\`\\`\\`\n", "  TEXT_1: \"I've just sent “hello world” to the #testing channel on Slack as you requested. Can I assist you with anything else?\"\n", "  TEXT_2: \"#general channel Slack\"\n", "  Result: NO\n", "  \\`\\`\\`\n", "  `;\n", "\n", "  const toolSchema = z.object({\n", "    rationale: z\n", "      .string()\n", "      .describe(\n", "        \"A string that explains the reasoning behind your answer. It's a step-by-step explanation of how you determined that TEXT_2 is or isn't semantically present in TEXT_1.\"\n", "      ),\n", "    answer: z.boolean().describe(\"Your answer\"),\n", "  });\n", "\n", "  const completion = await openai.chat.completions.create({\n", "    model: \"gpt-3.5-turbo\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content: system,\n", "      },\n", "      {\n", "        role: \"user\",\n", "        content: `TEXT_1: \"${text1}\"\\nTEXT_2: \"${text2}\"`,\n", "      },\n", "    ],\n", "    tools: [\n", "      makeFunctionDefinition(\n", "        \"semantic_contains\",\n", "        \"The result of the semantic presence check\",\n", "        toolSchema\n", "      ),\n", "    ],\n", "    tool_choice: {\n", "      function: { name: \"semantic_contains\" },\n", "      type: \"function\",\n", "    },\n", "    max_tokens: 1000,\n", "  });\n", "\n", "  try {\n", "    const { answer } = toolSchema.parse(\n", "      JSON.parse(\n", "        completion.choices[0].message.tool_calls![0].function.arguments\n", "      )\n", "    );\n", "    return answer;\n", "  } catch (e) {\n", "    console.error(e, \"Error parsing semanticContains response\");\n", "    return false;\n", "  }\n", "}\n", "\n", "const AssertionScorer = async ({\n", "  input,\n", "  output,\n", "  expected: assertions,\n", "}: {\n", "  input: string;\n", "  output: any;\n", "  expected: As<PERSON><PERSON>[];\n", "}) => {\n", "  // for each assertion, perform the comparison\n", "  const assertionResults: {\n", "    status: string;\n", "    path: string;\n", "    assertion_type: string;\n", "    value: string;\n", "    actualValue: string;\n", "  }[] = [];\n", "  for (const assertion of assertions) {\n", "    const { assertion_type, path, value } = assertion;\n", "    const actualValue = get(output, path);\n", "    let passedTest = false;\n", "\n", "    try {\n", "      switch (assertion_type) {\n", "        case \"equals\":\n", "          passedTest = actualValue === value;\n", "          break;\n", "        case \"exists\":\n", "          passedTest = actualValue !== undefined;\n", "          break;\n", "        case \"not_exists\":\n", "          passedTest = actualValue === undefined;\n", "          break;\n", "        case \"llm_criteria_met\":\n", "          const closedQA = await ClosedQA({\n", "            input:\n", "              \"According to the provided criterion is the submission correct?\",\n", "            criteria: value,\n", "            output: actualValue,\n", "          });\n", "          passedTest = !!closedQA.score && closedQA.score > 0.5;\n", "          break;\n", "        case \"semantic_contains\":\n", "          passedTest = await semanticContains({\n", "            text1: actualValue,\n", "            text2: value,\n", "          });\n", "          break;\n", "        default:\n", "          assertion_type satisfies never; // if you see a ts error here, its because your switch is not exhaustive\n", "          throw new Error(`unknown assertion type ${assertion_type}`);\n", "      }\n", "    } catch (e) {\n", "      passedTest = false;\n", "    }\n", "    assertionResults.push({\n", "      status: passedTest ? \"passed\" : \"failed\",\n", "      path,\n", "      assertion_type,\n", "      value,\n", "      actualValue,\n", "    });\n", "  }\n", "\n", "  const allPassed = every(assertionResults, (r) => r.status === \"passed\");\n", "\n", "  return {\n", "    name: \"Assertions Score\",\n", "    score: allPassed ? 1 : 0,\n", "    metadata: {\n", "      assertionResults,\n", "    },\n", "  };\n", "};\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["const data = [\n", "  {\n", "    input: \"What's the weather like in San Francisco?\",\n", "    expected: [\n", "      {\n", "        path: \"responseChatCompletions[0].tool_calls[0].function.name\",\n", "        assertion_type: \"equals\",\n", "        value: \"weather\",\n", "      },\n", "    ],\n", "  },\n", "  {\n", "    input: \"What's the weather like?\",\n", "    expected: [\n", "      {\n", "        path: \"responseChatCompletions[0].tool_calls[0].function.name\",\n", "        assertion_type: \"not_exists\",\n", "        value: \"\",\n", "      },\n", "      {\n", "        path: \"responseChatCompletions[0].content\",\n", "        assertion_type: \"llm_criteria_met\",\n", "        value:\n", "          \"Response reflecting the bot does not have enough information to look up the weather\",\n", "      },\n", "    ],\n", "  },\n", "  {\n", "    input: \"How much is AAPL stock today?\",\n", "    expected: [\n", "      {\n", "        path: \"responseChatCompletions[0].tool_calls[0].function.name\",\n", "        assertion_type: \"not_exists\",\n", "        value: \"\",\n", "      },\n", "      {\n", "        path: \"responseChatCompletions[0].content\",\n", "        assertion_type: \"llm_criteria_met\",\n", "        value:\n", "          \"Response reflecting the bot does not have access to the ability or tool to look up stock prices.\",\n", "      },\n", "    ],\n", "  },\n", "  {\n", "    input: \"What can you do?\",\n", "    expected: [\n", "      {\n", "        path: \"responseChatCompletions[0].content\",\n", "        assertion_type: \"semantic_contains\",\n", "        value: \"look up the weather\",\n", "      },\n", "    ],\n", "  },\n", "];\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Weather Bot',\n", "  experimentName: 'HEAD-1707465445',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot/HEAD-1707465445',\n", "  comparisonExperimentName: undefined,\n", "  scores: undefined,\n", "  metrics: undefined\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Weather Bot                              |   4% | 4/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Weather Bot',\n", "  experimentName: 'HEAD-1707465445',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot/HEAD-1707465445',\n", "  comparisonExperimentName: undefined,\n", "  scores: undefined,\n", "  metrics: undefined\n", "}\n"]}], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "\n", "await <PERSON><PERSON>(\"Weather Bot\", {\n", "  data,\n", "  task: async (input) => {\n", "    const result = await task(input);\n", "    return result;\n", "  },\n", "  scores: [AssertionScorer],\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyzing results\n", "\n", "It looks like half the cases passed.\n", "\n", "![Initial experiment](./assets/initial-experiment.png)\n", "\n", "In one case, the chatbot did not clearly indicate that it needs more information.\n", "\n", "![result-1](./assets/reason-1.png)\n", "\n", "In the other case, the chatbot halucinated a stock tool.\n", "\n", "![result-2](./assets/reason-2.png)\n", "\n", "## Improving the prompt\n", "\n", "Let's try to update the prompt to be more specific about asking for more information and not hallucinating a stock tool.\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["async function task(input: string) {\n", "  const completion = await openai.chat.completions.create({\n", "    model: \"gpt-3.5-turbo\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content: `You are a highly intelligent AI that can look up the weather.\n", "        \n", "Do not try to use tools other than those provided to you. If you do not have the tools needed to solve a problem, just say so.\n", "\n", "If you do not have enough information to answer a question, make sure to ask the user for more info. Prefix that statement with \"I need more information to answer this question.\"\n", "        `,\n", "      },\n", "      { role: \"user\", content: input },\n", "    ],\n", "    tools: [weatherTool],\n", "    max_tokens: 1000,\n", "  });\n", "\n", "  return {\n", "    responseChatCompletions: [completion.choices[0].message],\n", "  };\n", "}\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"responseChatCompletions\": [\n", "    {\n", "      \"role\": \"assistant\",\n", "      \"content\": \"I'm sorry, but I don't have the tools to look up stock prices.\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["JSON.stringify(await task(\"How much is AAPL stock today?\"), null, 2);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Re-running eval\n", "\n", "Let's re-run the eval and see if our changes helped.\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Weather Bot',\n", "  experimentName: 'HEAD-1707465778',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot/HEAD-1707465778',\n", "  comparisonExperimentName: 'HEAD-1707465445',\n", "  scores: {\n", "    'Assertions Score': {\n", "      name: 'Assertions Score',\n", "      score: 0.75,\n", "      diff: 0.25,\n", "      improvements: 1,\n", "      regressions: 0\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 1.5197500586509705,\n", "      unit: 's',\n", "      diff: -0.10424983501434326,\n", "      improvements: 2,\n", "      regressions: 2\n", "    }\n", "  }\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Weather Bot                              |   4% | 4/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Weather Bot',\n", "  experimentName: 'HEAD-1707465778',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Weather%20Bot/HEAD-1707465778',\n", "  comparisonExperimentName: 'HEAD-1707465445',\n", "  scores: {\n", "    'Assertions Score': {\n", "      name: 'Assertions Score',\n", "      score: 0.75,\n", "      diff: 0.25,\n", "      improvements: 1,\n", "      regressions: 0\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 1.5197500586509705,\n", "      unit: 's',\n", "      diff: -0.10424983501434326,\n", "      improvements: 2,\n", "      regressions: 2\n", "    }\n", "  }\n", "}\n"]}], "source": ["await <PERSON><PERSON>(\"Weather Bot\", {\n", "  data: data,\n", "  task: async (input) => {\n", "    const result = await task(input);\n", "    return result;\n", "  },\n", "  scores: [AssertionScorer],\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Nice! We were able to improve the \"needs more information\" case.\n", "\n", "![second experiment](./assets/second-experiment.png)\n", "\n", "However, we now halucinate and ask for the weather in NYC. Getting to 100% will take a bit more iteration!\n", "\n", "![bad tool call](./assets/bad-tool-call.png)\n", "\n", "Now that you have a solid evaluation framework in place, you can continue experimenting and try to solve this problem. Happy evaling!\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}