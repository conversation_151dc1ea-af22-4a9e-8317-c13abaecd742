{"cells": [{"cell_type": "markdown", "metadata": {"id": "nGyRX7A3MqkL"}, "source": ["# Evaluating video QA with Twelve Labs\n", "\n", "[Twelve Labs](https://www.twelvelabs.io) is a video intelligence platform that builds models for video understanding. Their video-first language model, [Pegasus](https://www.twelvelabs.io/product/models-overview#pegasus), can analyze, understand, and generate text from video content. Through its visual and audio understanding capabilities, it enables sophisticated video analysis, Q&A generation, content summarization, and detailed insights extraction from video content.\n", "\n", "In this cookbook, we'll evaluate a Pegasus-based video question-answering (video QA) system using the [MMVU dataset](https://mmvu-benchmark.github.io/). The MMVU dataset includes multi-disciplinary videos paired with questions and ground-truth answers, spanning many different topics.\n", "\n", "By the end, you'll have a repeatable workflow for quantitatively evaluating video QA performance, which you can adapt to different datasets or use cases. You can also use other models for video QA by following [this cookbook](/docs/cookbook/recipes/VideoQA). "]}, {"cell_type": "markdown", "metadata": {"id": "gzbc_s3-MqkN"}, "source": ["## Getting started\n", "\n", "First, we'll install the required packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vZx4MGzPMqkN", "outputId": "f74709aa-bea5-4ff0-cecf-469b66fa5f1f"}, "outputs": [], "source": ["!pip install requests datasets braintrust autoevals twelvelabs"]}, {"cell_type": "markdown", "metadata": {"id": "SygzfXbwMqkO"}, "source": ["Next, we'll import our modules and define constants:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6zthhpYaMqkO"}, "outputs": [], "source": ["import os\n", "from typing import List, Dict, Any, Optional\n", "\n", "import requests\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "\n", "from datasets import load_dataset\n", "\n", "import braintrust\n", "import autoevals\n", "\n", "from twelvelabs import TwelveLabs\n", "\n", "\n", "RETRY_TOTAL = 3\n", "RETRY_BACKOFF = 0.5\n", "STATUS_FORCELIST = [502, 503, 504]"]}, {"cell_type": "markdown", "metadata": {"id": "tndcJbf8MqkO"}, "source": ["If you haven't already, sign up for [Braintrust](https://www.braintrust.dev/signup) and [Twelve Labs](https://www.twelvelabs.io). To authenticate, export your `BRAINTRUST_API_KEY` and `TWELVE_LABS_API_KEY` as environment variables:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "export TWELVE_LABS_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "XHe5AK-KMqkP"}, "outputs": [], "source": ["# Initialize Braintrust\n", "BRAINTRUST_API_KEY = os.environ[\"BRAINTRUST_API_KEY\"]\n", "\n", "if not BRAINTRUST_API_KEY:\n", "    raise ValueError(\"Please set the BRAINTRUST_API_KEY environment variable.\")\n", "\n", "# Initialize Twelve Labs\n", "TWELVE_LABS_API_KEY = os.environ[\"TWELVE_LABS_API_KEY\"]\n", "\n", "if not TWELVE_LABS_API_KEY:\n", "    raise ValueError(\"Please set the TWELVE_LABS_API_KEY environment variable.\")\n", "\n", "twelvelabs_client = TwelveLabs(api_key=TWELVE_LABS_API_KEY)"]}, {"cell_type": "markdown", "metadata": {"id": "vzOdnW93MqkP"}, "source": ["## Downloading or reading raw video data\n", "\n", "Storing the raw video file as an attachment in Braintrust can simplify debugging by allowing you to easily reference the original source. The helper function `get_video_data` retrieves a video file either from a local path or URL:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "f5acIvA7MqkP"}, "outputs": [], "source": ["def get_video_data(video_path: str, session: requests.Session) -> Optional[bytes]:\n", "    try:\n", "        if video_path.startswith(\"http\"):\n", "            response = session.get(video_path, timeout=10)\n", "            response.raise_for_status()\n", "            return response.content\n", "        else:\n", "            with open(video_path, \"rb\") as f:\n", "                return f.read()\n", "    except Exception as e:\n", "        print(f\"Error retrieving video data from {video_path}: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {"id": "OM78wVMZMqkP"}, "source": ["## Setting up Twelve Labs video indexing\n", "\n", "While traditional LLMs sometimes require processing individual frames, Twelve Labs can analyze entire videos through its powerful indexing system, making it more efficient for video understanding tasks. We also don't need to manage the frames directly.\n", "\n", "Before we can ask questions about our videos, we need to create an index and upload our content to Twelve Labs. Let's start by creating an index with the appropriate configuration:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HLFR2eTWMqkP", "outputId": "78d06970-5aff-46a1-a9e1-42f8f3794aae"}, "outputs": [], "source": ["# Create or retrieve pegasus index\n", "models = [{\"name\": \"pegasus1.2\", \"options\": [\"visual\", \"audio\"]}]\n", "\n", "index_name = \"mmvu_videos\"\n", "indices_list = twelvelabs_client.index.list(name=index_name)\n", "\n", "if len(indices_list) == 0:\n", "    index = twelvelabs_client.index.create(\n", "        name=index_name, models=models, addons=[\"thumbnail\"]\n", "    )\n", "    print(\n", "        f\"A new index has been created: id={index.id} name={index.name} models={index.models}\"\n", "    )\n", "else:\n", "    index = indices_list[0]\n", "    print(\n", "        f\"Index already exists: id={index.id} name={index.name} models={index.models}\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "uqXgTISYMqkP"}, "source": ["Next, we'll create a function called `upload_video_to_twelve_labs` that handles the video upload and indexing process. This function takes a video URL as input and returns a `video_id` that we'll use later to query and analyze the video content."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "t-7Rk5F9MqkP"}, "outputs": [], "source": ["def on_task_update(task):\n", "    print(f\"  Status={task.status}\")\n", "\n", "\n", "def upload_video_to_twelve_labs(index, video_url):\n", "\n", "    task = twelvelabs_client.task.create(index_id=index.id, url=video_url)\n", "    print(f\"Task created: id={task.id} status={task.status}\")\n", "\n", "    task.wait_for_done(sleep_interval=5, callback=on_task_update)\n", "\n", "    if task.status != \"ready\":\n", "        raise RuntimeError(f\"Indexing failed with status {task.status}\")\n", "    print(f\"The unique identifier of your video is {task.video_id}.\")\n", "\n", "    # return the video id\n", "    return task.video_id"]}, {"cell_type": "markdown", "metadata": {"id": "_PHGHCXiMqkQ"}, "source": ["# Loading the data\n", "\n", "We'll work with the first 20 samples from the MMVU validation split. Each sample contains a video, a question, and an expected answer. We'll index each video using Twelve Labs, attach the `video_id` for the indexed video, and include the question-answer pair.\n", "\n", "First, we'll create `video_id_dict` to store `video_id`s so we don't accidentally re-index videos:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "n9JZ0enKMqkQ"}, "outputs": [], "source": ["video_id_dict = {}"]}, {"cell_type": "markdown", "metadata": {"id": "RMF46jGlMqkQ"}, "source": ["Next, we'll create our `load_data_subset` function:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "ulhEod3qMqkQ"}, "outputs": [], "source": ["def load_data_subset() -> List[Dict[str, Any]]:\n", "    ds = load_dataset(\"yale-nlp/MMVU\", split=\"validation[:20]\")\n", "\n", "    session = requests.Session()\n", "    retry = Retry(\n", "        total=RETRY_TOTAL,\n", "        backoff_factor=RETRY_BACKOFF,\n", "        status_forcelist=STATUS_FORCELIST,\n", "    )\n", "    adapter = HTTPAdapter(max_retries=retry)\n", "    session.mount(\"http://\", adapter)\n", "    session.mount(\"https://\", adapter)\n", "\n", "    data_list = []\n", "\n", "    for row in ds:\n", "        question_type = row[\"question_type\"]\n", "        video_path = row[\"video\"]\n", "        print(row[\"video\"])\n", "\n", "        raw_video = get_video_data(video_path, session)\n", "\n", "        choices_data = (\n", "            row.get(\"choices\") if question_type == \"multiple-choice\" else None\n", "        )\n", "\n", "        if video_path in video_id_dict.keys():\n", "            video_id = video_id_dict[video_path]\n", "        else:\n", "            video_id = upload_video_to_twelve_labs(index, video_path)\n", "            video_id_dict[video_path] = video_id\n", "\n", "        data_list.append(\n", "            {\n", "                \"input\": {\n", "                    \"video_id\": video_id,\n", "                    \"question\": row[\"question\"],\n", "                    \"question_type\": question_type,\n", "                    \"choices\": choices_data,\n", "                    \"video_attachment\": braintrust.Attachment(\n", "                        filename=os.path.basename(video_path),\n", "                        content_type=\"video/mp4\",\n", "                        data=raw_video,\n", "                    ),\n", "                },\n", "                \"expected\": {\"answer\": row[\"answer\"]},\n", "                \"metadata\": {\n", "                    \"subject\": row[\"metadata\"][\"subject\"],\n", "                    \"textbook\": row[\"metadata\"][\"textbook\"],\n", "                    \"question_type\": question_type,\n", "                },\n", "            }\n", "        )\n", "\n", "    session.close()\n", "    return data_list"]}, {"cell_type": "markdown", "metadata": {"id": "on6FoExSMqkQ"}, "source": ["Finally, we will load the data. It may take a few minutes depending on the size of your subset. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["459a7613d822443393b909b4b1de78e6", "6f82d212f78243bcaa75b5a2f2fdfb1a", "8b5a9b6118a24ecd9b6ed77e33765982", "5d5caaabc6624c9ebe4f1f079dac1115", "8ccb975f147f42e2a3209c4dfe5ad502", "dc47fe328ea4483cb9ab1f413ca3a63e", "4a61026b41494031a810ed6c3fa274ef", "25f0029f7acc4fb591908c4c460c484c", "7a0f2bb6a9034520906a4801bbb8b6b5", "ac2334646173448da0f8440638f89434", "d238aa0ecfff47cab4e3ddb94983333f", "c0e77e8185eb4ca89b094b99a2fb2d32", "293c9dcdad394e2980f6e56d1fac2b06", "30bf9b53d3c84c0890c3b03c231e37b1", "e2b98730a9634cb4868f3d22e539d3c5", "89e690a216c647a09cc4c07990457aba", "da97d378e77b4d21a119a4167618601e", "f29f347f8f4749099949f0ca9046c27b", "4615e5d384df4ecabe0704a244756c2a", "d047ebb5ab63416f8b300faadc22b608", "16d12a55b6a1469e9fa961c86569699b", "19a1cf41587d4048ba546f55884ccc52", "a27403bdf2ba4be693f9582e6ffa06dc", "b218bbff4bfb4aad9b236af5fbd6e8c7", "9974186ceb0e4857aa52d02020be361b", "b32a6215768d4834b515149754599687", "99b33e910f6942259aa8886b0624accb", "0a804a4af887462a99acdf5cf076bbfc", "5e0679c11586488c80edf528880af041", "1956e1ec685147d8bff33b9384d140ea", "d4798e412f1c4440a06b1aa05bb02fd8", "3839e422aa6448fd865c4b3d36f64bef", "0a02668f7b5c403889069db5fc6b834a"]}, "id": "zl3ogFpEMqkQ", "outputId": "227c2148-669c-42f4-f64f-87e99ee59f96"}, "outputs": [], "source": ["load_data_subset()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After you run the evaluation, you'll be able to investigate each video as an attachment in Braintrust, so you can dig into any cases that may need attention during evaluation. \n", "\n", "![View attachment](./assets/view-attachment.gif)"]}, {"cell_type": "markdown", "metadata": {"id": "RYNaUUFhMqkQ"}, "source": ["## Prompting <PERSON><PERSON>asus\n", "\n", "Next, we'll define a `video_qa` function to prompt Pegasus for answers. It constructs a prompt with the `video_id`, the question, and, for multiple-choice questions, the available options:"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"id": "c-7CJEpzMqkQ"}, "outputs": [], "source": ["def video_qa(input_dict: Dict[str, Any]) -> str:\n", "    video_id = input_dict[\"video_id\"]\n", "    question = input_dict[\"question\"]\n", "    question_type = input_dict.get(\"question_type\", \"open-ended\")\n", "    choices_data = input_dict.get(\"choices\")\n", "\n", "    if question_type == \"multiple-choice\" and choices_data:\n", "        if isinstance(choices_data, dict):\n", "            options_text = \"\\n\".join(\n", "                f\"{key}: {value}\" for key, value in choices_data.items()\n", "            )\n", "        else:\n", "            options_text = \"\\n\".join(\n", "                f\"{chr(65 + i)}: {option}\" for i, option in enumerate(choices_data)\n", "            )\n", "        prompt_text = (\n", "            f\"answer the following question: {question}.\\n\\n\"\n", "            f\"Here are your options:\\n{options_text}\\n\"\n", "            \"Choose the correct option in the format 'answer: X' where X is the letter that corresponds to the correct choice. If uncertain, guess. You MUST pick something.\"\n", "        )\n", "    else:\n", "        prompt_text = (\n", "            f\"Answer the following question: {question}. Use the most succinct language possible.\\n\"\n", "            \"If uncertain, guess. Provide the best possible answer. You MUST answer to the best of your ability.\"\n", "        )\n", "\n", "    res = twelvelabs_client.generate.text(video_id=video_id, prompt=prompt_text)\n", "    return res.data"]}, {"cell_type": "markdown", "metadata": {"id": "y2LWxENSMqkQ"}, "source": ["## Evaluating the model's answers\n", "\n", "To evaluate the model's answers, we'll define a function called `evaluator` that uses the `LLMClassifier` from the [autoevals](https://github.com/braintrustdata/autoevals?tab=readme-ov-file#custom-evaluation-prompts) library as a starting point. This scorer compares the model's output with the expected answer, assigning 1 if they match and 0 otherwise."]}, {"cell_type": "code", "execution_count": 32, "metadata": {"id": "WJY7milmMqkQ"}, "outputs": [], "source": ["evaluator = autoevals.LLMClassifier(\n", "    name=\"evaluator\",\n", "    prompt_template=(\n", "        \"You are a judge evaluating a model's ability to answer a question \"\n", "        \"Model's answer:\\n{{output}}\\n\\n\"\n", "        \"Expected answer:\\n{{expected.answer}}\\n\\n\"\n", "        \"Is the model's answer correct? (Y/N)? Only Y or N.\"\n", "    ),\n", "    choice_scores={\"Y\": 1, \"N\": 0},\n", "    use_cot=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "hci3_5xTMqkQ"}, "source": ["# Running the evaluation\n", "\n", "Now that we have the three required components (a dataset, task, and prompt), we can run the eval. It loads data using `load_data_subset`, uses `video_qa` to get answers from Pegasus, and scores each response with `evaluator`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["3dce6581a2c64cdf98eb5da31e4aa59b", "d91265bfd2af4c7080baffe7b57914a1", "11e5c369fa4e451f94727e58b0fe1c69", "ac0f1dd54c6e4858a0ea0caf718d5484", "fca72c3208ca4f8e929f82a9ca069e16", "4857af932f7649a2a351e5e8397a6564", "0646a3bcce8f4f7eb22b30b0a2e55fea", "ec09347d81a64ef2b8636f1a45101b24", "81b9798f76614efd8997277e870508fd", "e14bd22bd65c45208d5f26281f7ec9f4", "d0547eda42fa42e797e0674cb8970f01", "9e3905bb3b87404db0d13fd0a8d14ac3", "2923af234ee64a69baf6ecfa239cc7ca", "9acf90b19148437d8f379fd32c63e903", "42ac85e0f4b6407b8e941a11a03b5fb2", "5a89b22a291843cabef53f8c3c96324a", "dded183c1e3948ca812092eb5096ef1c", "b912ed2d9974422d925e71f4f0b6e7e9", "513da7e817064a7197409cb177f3b5d0", "99ed443700c04cfabe8b59c2e61d95b5", "2ffd387febee4b10ab6bb9f5bda9a83d", "0aa9050b92ab481ab55adf983bfdd9b1", "38fb48e3543d4473bcb2754462d806fd", "76bd3eeedbc944229a88e5124b45aa88", "8682c6eac50b4436a45e097e60964111", "03dbec89137c4ea1a116e0260e0b7c93", "183990e246e24454a431bb224c883b92", "25a098b7650f49dba9024196b3ec05b0", "b349cc073be0431382d1eba245ece45d", "3215f0959d9940c397f38d035bdb0419", "41982a520d254fcc8b6221392754c324", "bb0ba261d43540e493a0eac13e021d11", "4be0ebb860694595b126f044d362f86a", "72c2876428174c9e8f33632e26024d1e", "c54f798b365244d3a37a307bab539302", "9cfad12acc554de7aebed20b9dcdb514", "2b14c2d7cbf6411b833b641f80f783dd", "769988f805844864a2334a37260d1921", "b5c30b47d4424fe68e2718ab447e6ddc", "9d05c8711c08438a95402fd3f09a8d33", "b58648602c6f4871b19a2c00c7c7a9ed", "6fd215a12dda4cafb22a6dec7dd24a78", "26f67b5945304dcea2c38fa2656c6032", "592d542ebd5f44dcad3e04b75721ac6c"]}, "id": "lQ3vfX6EMqkQ", "outputId": "a68e64aa-0493-43f1-a006-354f4c486223"}, "outputs": [], "source": ["await braintrust.<PERSON>l<PERSON>(\n", "    \"Twelve Labs Video QA\",\n", "    data=load_data_subset,\n", "    task=video_qa,\n", "    scores=[evaluator],\n", "    metadata={\"model\": \"pegasus1.2\"},\n", "    experiment_name=\"mmvu_eval\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "MP-t7gGrMqkR"}, "source": ["## Analyzing results\n", "\n", "After running the evaluation, navigate to **Evaluations** > **Experiments** in the Braintrust UI to see your results. Select your most recent experiment to review the videos included in our dataset, the model's answer for each sample, and the scoring by our LLM-based judge. We also attached metadata like subject and question_type, which you can use to filter in the Braintrust UI. This makes it easy to see whether the model underperforms on a certain type of question or domain. \n", "\n", "![Experiment](./assets/12-labs-experiment.png)\n", "\n", "If you discover specific weaknesses, you can consider:\n", "* Refining your model prompt with more subject-specific context\n", "* Refining your LLM-as-a-judge scorer\n", "* [Switching models](/docs/cookbook/recipes/VideoQA) and running experiments in tandem\n", "* Refining the QA dataset to optimize for a particular domain"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"03dbec89137c4ea1a116e0260e0b7c93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bb0ba261d43540e493a0eac13e021d11", "placeholder": "​", "style": "IPY_MODEL_4be0ebb860694595b126f044d362f86a", "value": " 1000/1000 [00:00&lt;00:00, 11546.57 examples/s]"}}, "0646a3bcce8f4f7eb22b30b0a2e55fea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a02668f7b5c403889069db5fc6b834a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a804a4af887462a99acdf5cf076bbfc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0aa9050b92ab481ab55adf983bfdd9b1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "11e5c369fa4e451f94727e58b0fe1c69": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ec09347d81a64ef2b8636f1a45101b24", "max": 5609, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_81b9798f76614efd8997277e870508fd", "value": 5609}}, "16d12a55b6a1469e9fa961c86569699b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "183990e246e24454a431bb224c883b92": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1956e1ec685147d8bff33b9384d140ea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19a1cf41587d4048ba546f55884ccc52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25a098b7650f49dba9024196b3ec05b0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "25f0029f7acc4fb591908c4c460c484c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "26f67b5945304dcea2c38fa2656c6032": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2923af234ee64a69baf6ecfa239cc7ca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dded183c1e3948ca812092eb5096ef1c", "placeholder": "​", "style": "IPY_MODEL_b912ed2d9974422d925e71f4f0b6e7e9", "value": "validation.json: 100%"}}, "293c9dcdad394e2980f6e56d1fac2b06": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_da97d378e77b4d21a119a4167618601e", "placeholder": "​", "style": "IPY_MODEL_f29f347f8f4749099949f0ca9046c27b", "value": "validation.json: 100%"}}, "2b14c2d7cbf6411b833b641f80f783dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_26f67b5945304dcea2c38fa2656c6032", "placeholder": "​", "style": "IPY_MODEL_592d542ebd5f44dcad3e04b75721ac6c", "value": " 20/20 [00:57&lt;00:00,  2.92s/it]"}}, "2ffd387febee4b10ab6bb9f5bda9a83d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "30bf9b53d3c84c0890c3b03c231e37b1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4615e5d384df4ecabe0704a244756c2a", "max": 1574914, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d047ebb5ab63416f8b300faadc22b608", "value": 1574914}}, "3215f0959d9940c397f38d035bdb0419": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3839e422aa6448fd865c4b3d36f64bef": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38fb48e3543d4473bcb2754462d806fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_76bd3eeedbc944229a88e5124b45aa88", "IPY_MODEL_8682c6eac50b4436a45e097e60964111", "IPY_MODEL_03dbec89137c4ea1a116e0260e0b7c93"], "layout": "IPY_MODEL_183990e246e24454a431bb224c883b92"}}, "3dce6581a2c64cdf98eb5da31e4aa59b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d91265bfd2af4c7080baffe7b57914a1", "IPY_MODEL_11e5c369fa4e451f94727e58b0fe1c69", "IPY_MODEL_ac0f1dd54c6e4858a0ea0caf718d5484"], "layout": "IPY_MODEL_fca72c3208ca4f8e929f82a9ca069e16"}}, "41982a520d254fcc8b6221392754c324": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "42ac85e0f4b6407b8e941a11a03b5fb2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ffd387febee4b10ab6bb9f5bda9a83d", "placeholder": "​", "style": "IPY_MODEL_0aa9050b92ab481ab55adf983bfdd9b1", "value": " 1.57M/1.57M [00:00&lt;00:00, 49.6MB/s]"}}, "459a7613d822443393b909b4b1de78e6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6f82d212f78243bcaa75b5a2f2fdfb1a", "IPY_MODEL_8b5a9b6118a24ecd9b6ed77e33765982", "IPY_MODEL_5d5caaabc6624c9ebe4f1f079dac1115"], "layout": "IPY_MODEL_8ccb975f147f42e2a3209c4dfe5ad502"}}, "4615e5d384df4ecabe0704a244756c2a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4857af932f7649a2a351e5e8397a6564": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a61026b41494031a810ed6c3fa274ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4be0ebb860694595b126f044d362f86a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "513da7e817064a7197409cb177f3b5d0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "592d542ebd5f44dcad3e04b75721ac6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5a89b22a291843cabef53f8c3c96324a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d5caaabc6624c9ebe4f1f079dac1115": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ac2334646173448da0f8440638f89434", "placeholder": "​", "style": "IPY_MODEL_d238aa0ecfff47cab4e3ddb94983333f", "value": " 5.61k/5.61k [00:00&lt;00:00, 392kB/s]"}}, "5e0679c11586488c80edf528880af041": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6f82d212f78243bcaa75b5a2f2fdfb1a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dc47fe328ea4483cb9ab1f413ca3a63e", "placeholder": "​", "style": "IPY_MODEL_4a61026b41494031a810ed6c3fa274ef", "value": "README.md: 100%"}}, "6fd215a12dda4cafb22a6dec7dd24a78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "72c2876428174c9e8f33632e26024d1e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c54f798b365244d3a37a307bab539302", "IPY_MODEL_9cfad12acc554de7aebed20b9dcdb514", "IPY_MODEL_2b14c2d7cbf6411b833b641f80f783dd"], "layout": "IPY_MODEL_769988f805844864a2334a37260d1921"}}, "769988f805844864a2334a37260d1921": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "76bd3eeedbc944229a88e5124b45aa88": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_25a098b7650f49dba9024196b3ec05b0", "placeholder": "​", "style": "IPY_MODEL_b349cc073be0431382d1eba245ece45d", "value": "Generating validation split: 100%"}}, "7a0f2bb6a9034520906a4801bbb8b6b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "81b9798f76614efd8997277e870508fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8682c6eac50b4436a45e097e60964111": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3215f0959d9940c397f38d035bdb0419", "max": 1000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_41982a520d254fcc8b6221392754c324", "value": 1000}}, "89e690a216c647a09cc4c07990457aba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b5a9b6118a24ecd9b6ed77e33765982": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_25f0029f7acc4fb591908c4c460c484c", "max": 5609, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7a0f2bb6a9034520906a4801bbb8b6b5", "value": 5609}}, "8ccb975f147f42e2a3209c4dfe5ad502": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9974186ceb0e4857aa52d02020be361b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1956e1ec685147d8bff33b9384d140ea", "max": 1000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d4798e412f1c4440a06b1aa05bb02fd8", "value": 1000}}, "99b33e910f6942259aa8886b0624accb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "99ed443700c04cfabe8b59c2e61d95b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9acf90b19148437d8f379fd32c63e903": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_513da7e817064a7197409cb177f3b5d0", "max": 1574914, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_99ed443700c04cfabe8b59c2e61d95b5", "value": 1574914}}, "9cfad12acc554de7aebed20b9dcdb514": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b58648602c6f4871b19a2c00c7c7a9ed", "max": 20, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6fd215a12dda4cafb22a6dec7dd24a78", "value": 20}}, "9d05c8711c08438a95402fd3f09a8d33": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9e3905bb3b87404db0d13fd0a8d14ac3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2923af234ee64a69baf6ecfa239cc7ca", "IPY_MODEL_9acf90b19148437d8f379fd32c63e903", "IPY_MODEL_42ac85e0f4b6407b8e941a11a03b5fb2"], "layout": "IPY_MODEL_5a89b22a291843cabef53f8c3c96324a"}}, "a27403bdf2ba4be693f9582e6ffa06dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b218bbff4bfb4aad9b236af5fbd6e8c7", "IPY_MODEL_9974186ceb0e4857aa52d02020be361b", "IPY_MODEL_b32a6215768d4834b515149754599687"], "layout": "IPY_MODEL_99b33e910f6942259aa8886b0624accb"}}, "ac0f1dd54c6e4858a0ea0caf718d5484": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e14bd22bd65c45208d5f26281f7ec9f4", "placeholder": "​", "style": "IPY_MODEL_d0547eda42fa42e797e0674cb8970f01", "value": " 5.61k/5.61k [00:00&lt;00:00, 304kB/s]"}}, "ac2334646173448da0f8440638f89434": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b218bbff4bfb4aad9b236af5fbd6e8c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0a804a4af887462a99acdf5cf076bbfc", "placeholder": "​", "style": "IPY_MODEL_5e0679c11586488c80edf528880af041", "value": "Generating validation split: 100%"}}, "b32a6215768d4834b515149754599687": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3839e422aa6448fd865c4b3d36f64bef", "placeholder": "​", "style": "IPY_MODEL_0a02668f7b5c403889069db5fc6b834a", "value": " 1000/1000 [00:00&lt;00:00, 8403.89 examples/s]"}}, "b349cc073be0431382d1eba245ece45d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b58648602c6f4871b19a2c00c7c7a9ed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5c30b47d4424fe68e2718ab447e6ddc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b912ed2d9974422d925e71f4f0b6e7e9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bb0ba261d43540e493a0eac13e021d11": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c0e77e8185eb4ca89b094b99a2fb2d32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_293c9dcdad394e2980f6e56d1fac2b06", "IPY_MODEL_30bf9b53d3c84c0890c3b03c231e37b1", "IPY_MODEL_e2b98730a9634cb4868f3d22e539d3c5"], "layout": "IPY_MODEL_89e690a216c647a09cc4c07990457aba"}}, "c54f798b365244d3a37a307bab539302": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5c30b47d4424fe68e2718ab447e6ddc", "placeholder": "​", "style": "IPY_MODEL_9d05c8711c08438a95402fd3f09a8d33", "value": "mmvu_eval [experiment_name=mmvu_eval] (tasks): 100%"}}, "d047ebb5ab63416f8b300faadc22b608": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d0547eda42fa42e797e0674cb8970f01": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d238aa0ecfff47cab4e3ddb94983333f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d4798e412f1c4440a06b1aa05bb02fd8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d91265bfd2af4c7080baffe7b57914a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4857af932f7649a2a351e5e8397a6564", "placeholder": "​", "style": "IPY_MODEL_0646a3bcce8f4f7eb22b30b0a2e55fea", "value": "README.md: 100%"}}, "da97d378e77b4d21a119a4167618601e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dc47fe328ea4483cb9ab1f413ca3a63e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dded183c1e3948ca812092eb5096ef1c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e14bd22bd65c45208d5f26281f7ec9f4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e2b98730a9634cb4868f3d22e539d3c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_16d12a55b6a1469e9fa961c86569699b", "placeholder": "​", "style": "IPY_MODEL_19a1cf41587d4048ba546f55884ccc52", "value": " 1.57M/1.57M [00:00&lt;00:00, 3.74MB/s]"}}, "ec09347d81a64ef2b8636f1a45101b24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f29f347f8f4749099949f0ca9046c27b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fca72c3208ca4f8e929f82a9ca069e16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}