{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Prompt versioning and deployment\n", "\n", "Large language models can sometimes feel unpredictable, where small changes to a prompt can dramatically change the quality and tone of generated responses. In customer support, this is especially important, since customer satisfaction, brand tone, and the clarity of solutions offered all rely on consistent, high-quality prompts. Optimizing this process involves creating a couple variations, measuring their effectiveness, and sometimes returning to previous versions that performed better.\n", "\n", "In this cookbook, we'll build a support chatbot and walk through the complete cycle of prompt development. Starting with a basic implementation, we'll create increasingly sophisticated prompts, keep track of different versions, evaluate their performance, and switch back to earlier versions when necessary."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started\n", "\n", "Before getting started, make sure you have a [Braintrust account](https://www.braintrust.dev/signup) and an API key for [OpenAI](https://platform.openai.com/signup). Make sure to plug the OpenAI key into your Braintrust account's [AI provider configuration](https://www.braintrust.dev/app/settings?subroute=secrets). \n", "\n", "Once you have your Braintrust account set up with an OpenAI API key, install the following dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install braintrust autoevals openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "To authenticate with Braintrust, export your `BRAINTRUST_API_KEY` as an environment variable:\n", "```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>\n", "\n", "Once the API key is set, we can import our modules and initialize the OpenAI client using the AI proxy:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import subprocess\n", "from openai import OpenAI\n", "from braintrust import Eval, wrap_openai, invoke\n", "from autoevals import LLMClassifier\n", "\n", "# Uncomment the following line to hardcode your API key\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_API_KEY_HERE\"\n", "\n", "# Initialize OpenAI client with Braintrust wrapper\n", "client = wrap_openai(\n", "    OpenAI(\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "        api_key=os.environ[\"BRAINTRUST_API_KEY\"],\n", "    )\n", ")\n", "\n", "project_name = \"SupportChatbot\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a dataset\n", "\n", "We'll create a small dataset of sample customer complaints and inquiries to evaluate our prompts. In a production application, you'd want to use real customer interactions from your logs to create a representative dataset.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["dataset = [\n", "    {\n", "        \"input\": \"Why did my package disappear after tracking showed it was delivered?\",\n", "        \"metadata\": {\"category\": \"shipping\"},\n", "    },\n", "    {\n", "        \"input\": \"Your product smells like burnt rubber - what’s wrong with it?\",\n", "        \"metadata\": {\"category\": \"product\"},\n", "    },\n", "    {\n", "        \"input\": \"I ordered 3 items but only got 1, where’s the rest?\",\n", "        \"metadata\": {\"category\": \"shipping\"},\n", "    },\n", "    {\n", "        \"input\": \"Why does your app crash every time I try to check out?\",\n", "        \"metadata\": {\"category\": \"tech\"},\n", "    },\n", "    {\n", "        \"input\": \"My refund was supposed to be here 2 weeks ago - what’s the holdup?\",\n", "        \"metadata\": {\"category\": \"returns\"},\n", "    },\n", "    {\n", "        \"input\": \"Your instructions say ‘easy setup’ but it took me 3 hours!\",\n", "        \"metadata\": {\"category\": \"product\"},\n", "    },\n", "    {\n", "        \"input\": \"Why does your delivery guy keep leaving packages at the wrong house?\",\n", "        \"metadata\": {\"category\": \"shipping\"},\n", "    },\n", "    {\n", "        \"input\": \"The discount code you sent me doesn’t work - fix it!\",\n", "        \"metadata\": {\"category\": \"sales\"},\n", "    },\n", "    {\n", "        \"input\": \"Your support line hung up on me twice - what’s going on?\",\n", "        \"metadata\": {\"category\": \"support\"},\n", "    },\n", "    {\n", "        \"input\": \"Why is your website saying my account doesn’t exist when I just made it?\",\n", "        \"metadata\": {\"category\": \"tech\"},\n", "    },\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a scoring function\n", "\n", "When evaluating support responses, we care about tone, helpfulness, and professionalism, not just accuracy. To do this, we use an [LLMClassifier](https://github.com/braintrustdata/autoevals?tab=readme-ov-file#python-3) that checks for alignment with brand guidelines:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["brand_alignment_scorer = LLMClassifier(\n", "    name=\"BrandAlignment\",\n", "    prompt_template=\"\"\"\n", "    Evaluate if the response aligns with our brand guidelines (Y/N):\n", "    1. **Positive Tone**: Uses upbeat language, avoids negativity (e.g., \"We’re thrilled to help!\" vs. \"That’s your problem\").\n", "    2. **Proactive Approach**: Offers a clear next step or solution (e.g., \"We’ll track it now!\" vs. vague promises).\n", "    3. **Apologetic When Appropriate**: Acknowledges issues with empathy (e.g., \"So sorry for the mix-up!\" vs. ignoring the complaint).\n", "    4. **Solution-Oriented**: Focuses on fixing the issue for the customer (e.g., \"Here’s how we’ll make it right!\" vs. excuses).\n", "    5. **Professionalism**: There should be no profanity, or emojis.\n", "    \n", "    Response: {{output}}\n", "\n", "\n", "    Only give a Y if all the criteria are met. If one is missing and it should be there, give a N. \n", "    \"\"\",\n", "    choice_scores={\n", "        \"Y\": 1,\n", "        \"N\": 0,\n", "    },  # This scorer will return a 1 if the response fully matches all brand guidelines, and a 0 otherwise.\n", "    use_cot=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a prompt\n", "\n", "To push a prompt to Braintrust, we need to create a new Python file `prompt_v1.py` that defines the prompt. Once we've created the file, we can push it to Braintrust via the CLI. Let's start with a basic prompt that provides a direct response to customer inquiries:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Create a prompt_v1.py file\n", "\n", "import braintrust\n", "\n", "project = braintrust.projects.create(name=\"SupportChatbot\")\n", "\n", "prompt_v1 = project.prompts.create(\n", "    name=\"Brand Support V1\",\n", "    slug=\"brand-support-v1\",\n", "    description=\"Simple support prompt\",\n", "    model=\"gpt-4o\",\n", "    messages=[{\"role\": \"user\", \"content\": \"{{{input}}}\"}],\n", "    if_exists=\"replace\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To push the prompt to Braintrust, run:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "braintrust push prompt_v1.py\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After pushing the prompt, you'll see it in the Braintrust UI.\n", "\n", "![promptv1](./assets/promptv1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluating prompt v1\n", "\n", "Now that our first prompt is ready, we'll define a task function that calls this prompt. Then, we'll run an evaluation with our `brand_alignment_scorer`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define task using invoke with correct input\n", "def task_v1(input):\n", "    result = invoke(\n", "        project_name=project_name,\n", "        slug=\"brand-support-v1\",\n", "        input={\"input\": input},  # Matches {{{input}}} in our prompt\n", "    )\n", "    return result\n", "\n", "\n", "eval_task = Eval(\n", "    project_name,\n", "    data=lambda: dataset,\n", "    task=task_v1,\n", "    scores=[brand_alignment_scorer],\n", "    experiment_name=\"prompt_v1\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After running the evaluation, you'll see the results in the Braintrust UI:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![v1results1](./assets/v1results1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improving our prompt\n", "\n", "Our initial evaluation showed that there is room for improvement. Let's create a more sophisticated prompt that incorporates our brand guidelines to encourage a positive, proactive tone and clear solutions. Like before, we'll create a new Python file called `prompt_v2.py` and push it to Braintrust."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Create a prompt_v2.py file\n", "\n", "import braintrust\n", "\n", "project = braintrust.projects.create(name=\"SupportChatbot\")\n", "\n", "prompt_v2 = project.prompts.create(\n", "    name=\"Brand Support V2\",\n", "    slug=\"brand-support-v2\",\n", "    description=\"Brand-aligned support prompt\",\n", "    model=\"gpt-4o\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You’re a cheerful, proactive assistant for Sunshine Co. Always use a positive tone, apologize for issues with empathy, and offer clear solutions to delight customers! No emojis or profanity.\",\n", "        },\n", "        {\"role\": \"user\", \"content\": \"{{{input}}}\"},\n", "    ],\n", "    if_exists=\"replace\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To push the prompt to Braintrust, run:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "braintrust push prompt_v2.py\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluating prompt v2\n", "\n", "We now point our task function to the slug of our second prompt:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def task_v2(input):\n", "    result = invoke(\n", "        project_name=project_name,\n", "        slug=\"brand-support-v2\",\n", "        input={\"input\": input},\n", "    )\n", "    return result\n", "\n", "\n", "eval_task = Eval(\n", "    project_name,\n", "    data=lambda: dataset,\n", "    task=task_v2,\n", "    scores=[brand_alignment_scorer],\n", "    experiment_name=\"prompt_v2\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is a clear improvement in brand alignment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["![v2results1](./assets/v2results1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Experimenting with tone\n", "\n", "For our third prompt, let's create `prompt_v3.py` and exaggerate the brand voice further. This example is intentionally over the top to show how brand alignment might fail if the tone is too extreme or vague in offering solutions. In practice, you'd likely use more subtle variations."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Create a prompt_v3.py file\n", "\n", "import braintrust\n", "\n", "project = braintrust.projects.create(name=\"SupportChatbot\")\n", "\n", "prompt_v3 = project.prompts.create(\n", "    name=\"Brand Support V3\",\n", "    slug=\"brand-support-v3\",\n", "    description=\"Over-enthusiastic support prompt with middling performance\",\n", "    model=\"gpt-4o\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You’re a SUPER EXCITED Sunshine Co. assistant! SHOUT IN ALL CAPS WITH LOTS OF EXCLAMATIONS!!!! SAY SORRY IF SOMETHING’S WRONG BUT KEEP IT VAGUE AND FUN!!! Make customers HAPPY with BIG ENERGY, even if solutions are UNCLEAR!!!!\",\n", "        },\n", "        {\"role\": \"user\", \"content\": \"{{{input}}}\"},\n", "    ],\n", "    if_exists=\"replace\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To push the prompt to Braintrust, run:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "braintrust push prompt_v3.py\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluating prompt v3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def task_v3(input):\n", "    result = invoke(\n", "        project_name=project_name,\n", "        slug=\"brand-support-v3\",\n", "        input={\"input\": input},\n", "    )\n", "    return result\n", "\n", "\n", "eval_task = Eval(\n", "    project_name,\n", "    data=lambda: dataset,\n", "    task=task_v3,\n", "    scores=[brand_alignment_scorer],\n", "    experiment_name=\"prompt_v3\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You might notice a lower brand alignment score here. This highlights why controlled tone adjustments are crucial in real-world scenarios, and how you might need several iterations to find an optimal prompt.\n", "\n", "![v3results](./assets/v3results.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Managing prompt versions\n", "\n", "After evaluating all three versions, we found that our second prompt achieved the highest score.\n", "\n", "![scores](./assets/scores.png)\n", "\n", "\n", "Although we've iterated on the prompt, Braintrust makes it simple to revert to this high-performing version:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def task_reverted(input):\n", "    result = invoke(\n", "        project_name=project_name,\n", "        slug=\"brand-support-v2\",\n", "        input={\"input\": input},\n", "    )\n", "    return result\n", "\n", "\n", "eval_task = Eval(\n", "    project_name,\n", "    data=lambda: dataset,\n", "    task=task_reverted,\n", "    scores=[brand_alignment_scorer],\n", "    experiment_name=\"prompt_v2_reverted\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you keep the same slug for multiple changes, Braintrust’s built-in versioning allows you to revert within the UI. See the docs on [prompt versioning](/docs/guides/functions/prompts#in-code) for more information.\n", "\n", "![versions1](./assets/versions1.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "- Now that you have some prompts saved, you can rapidly test them with new models in our [prompt playground](/docs/guides/playground).\n", "- Learn more about [evaluating a chat assistant](/docs/cookbook/recipes/EvaluatingChatAssistant).\n", "- Think about how you might add more sophisticated [scoring functions](/docs/guides/evals/write#scorers) to your evals."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}