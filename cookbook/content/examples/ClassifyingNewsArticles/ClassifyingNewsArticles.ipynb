{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {"id": "vigvBmpNxHvb"}, "source": ["# Braintrust classification tutorial (article titles)\n", "\n", "Classification is a core natural language processing (NLP) task that large language models are good at, but building reliable systems is still challenging. In this cookbook, we'll walk through how to improve an LLM-based classification system that sorts news articles by category.\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "c0hvsPRZLCUz"}, "source": ["## Getting started\n", "\n", "Before getting started, make sure you have a [Braintrust account](https://www.braintrust.dev/signup) and an API key for [OpenAI](https://platform.openai.com/signup). Make sure to plug the OpenAI key into your Braintrust account's [AI provider configuration](https://www.braintrust.dev/app/settings?subroute=secrets). \n", "\n", "Once you have your Braintrust account set up with an OpenAI API key, install the following dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rTIA4DVQw1K7"}, "outputs": [], "source": ["%pip install -U braintrust openai datasets autoevals"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we'll import the libraries we need and load the [ag_news](https://huggingface.co/datasets/ag_news) dataset from Hugging Face. Once the dataset is loaded, we'll extract the category names to build a map from indices to names, allowing us to compare expected categories with model outputs. Then, we'll shuffle the dataset with a fixed seed, trim it to 20 data points, and restructure it into a list where each item includes the article text as input and its expected category name."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8UDe2_sAw1K7"}, "outputs": [], "source": ["import braintrust\n", "import os\n", "\n", "from datasets import load_dataset\n", "from autoevals import Levenshtein\n", "from openai import OpenAI\n", "\n", "dataset = load_dataset(\"ag_news\", split=\"train\")\n", "\n", "category_names = dataset.features[\"label\"].names\n", "category_map = dict([name for name in enumerate(category_names)])\n", "\n", "trimmed_dataset = dataset.shuffle(seed=42)[:20]\n", "articles = [\n", "    {\n", "        \"input\": trimmed_dataset[\"text\"][i],\n", "        \"expected\": category_map[trimmed_dataset[\"label\"][i]],\n", "    }\n", "    for i in range(len(trimmed_dataset[\"text\"]))\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To authenticate with Braintrust, export your `BRAINTRUST_API_KEY` as an environment variable:\n", "```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>\n", "\n", "Once the API key is set, we initialize the OpenAI client using the AI proxy:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Uncomment the following line to hardcode your API key\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_API_KEY_HERE\"\n", "\n", "client = braintrust.wrap_openai(\n", "    OpenAI(\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "        api_key=os.environ[\"BRAINTRUST_API_KEY\"],\n", "    )\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "3z5e5UG3w1K8"}, "source": ["## Writing the initial prompts\n", "\n", "We'll start by testing classification on a single article. We'll select it from the dataset to examine its input and expected output:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "9x5dPZiIw1K8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Article Title: Bangladesh paralysed by strikes Opposition activists have brought many towns and cities in Bangladesh to a halt, the day after 18 people died in explosions at a political rally.\n", "Article Label: World\n"]}], "source": ["# Here's the input and expected output for the first article in our dataset.\n", "test_article = articles[0]\n", "test_text = test_article[\"input\"]\n", "expected_text = test_article[\"expected\"]\n", "\n", "print(\"Article Title:\", test_text)\n", "print(\"Article Label:\", expected_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we've verified what's in our dataset and initialized the OpenAI client, it's time to try writing a prompt and classifying a title. We'll define a `classify_article` function that takes an input title and returns a category:\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "3yK7ZSQnw1K8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input: Bangladesh paralysed by strikes Opposition activists have brought many towns and cities in Bangladesh to a halt, the day after 18 people died in explosions at a political rally.\n", "Classified as: World\n", "Score: 1\n"]}], "source": ["MODEL = \"gpt-3.5-turbo\"\n", "\n", "\n", "@braintrust.traced\n", "def classify_article(input):\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"\"\"You are an editor in a newspaper who helps writers identify the right category for their news articles,\n", "by reading the article's title. The category should be one of the following: World, Sports, Business or Sci-Tech. Reply with one word corresponding to the category.\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Article title: {article_title} Category:\".format(\n", "                article_title=input\n", "            ),\n", "        },\n", "    ]\n", "    result = client.chat.completions.create(\n", "        model=MODEL,\n", "        messages=messages,\n", "        max_tokens=10,\n", "    )\n", "    category = result.choices[0].message.content\n", "    return category\n", "\n", "\n", "test_classify = classify_article(test_text)\n", "print(\"Input:\", test_text)\n", "print(\"Classified as:\", test_classify)\n", "print(\"Score:\", 1 if test_classify == expected_text else 0)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "bAQ3gHsdh3JU"}, "source": ["## Running an evaluation\n", "\n", "We've tested our prompt on a single article, so now we can test across the rest of the dataset using the `Eval` function. Behind the scenes, `Eval` will in parallel run the `classify_article` function on each article in the dataset, and then compare the results to the ground truth labels using a simple `Lev<PERSON>htein` scorer. When it finishes running, it will print out the results with a link to dig deeper.\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "pXzhZ8fdw1K9"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Original Prompt-db3e9cae is running at https://www.braintrust.dev/app/braintrustdata.com/p/Classifying%20News%20Articles%20Cookbook/experiments/Original%20Prompt-db3e9cae\n", "`Eval()` was called from an async context. For better performance, it is recommended to use `await EvalAsync()` instead.\n", "Classifying News Articles Cookbook [experiment_name=Original Prompt] (data): 20it [00:00, 41755.14it/s]\n", "Classifying News Articles Cookbook [experiment_name=Original Prompt] (tasks): 100%|██████████| 20/20 [00:02<00:00,  7.57it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Original Prompt-db3e9cae compared to New Prompt-9f185e9e:\n", "71.25% (-00.62%) 'Levenshtein' score\t(1 improvements, 2 regressions)\n", "\n", "1740081219.56s start\n", "1740081220.69s end\n", "1.10s (-298.16%) 'duration'         \t(12 improvements, 8 regressions)\n", "0.72s (-294.09%) 'llm_duration'     \t(10 improvements, 10 regressions)\n", "113.75tok (-) 'prompt_tokens'    \t(0 improvements, 0 regressions)\n", "2.20tok (-) 'completion_tokens'\t(0 improvements, 0 regressions)\n", "115.95tok (-) 'total_tokens'     \t(0 improvements, 0 regressions)\n", "0.00$ (-) 'estimated_cost'   \t(0 improvements, 0 regressions)\n", "\n", "See results for Original Prompt-db3e9cae at https://www.braintrust.dev/app/braintrustdata.com/p/Classifying%20News%20Articles%20Cookbook/experiments/Original%20Prompt-db3e9cae\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(summary=\"...\", results=[...])"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["await braintrust.<PERSON>(\n", "    \"Classifying News Articles Cookbook\",\n", "    data=articles,\n", "    task=classify_article,\n", "    scores=[<PERSON><PERSON><PERSON><PERSON>],\n", "    experiment_name=\"Original Prompt\",\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "90TGWYWihDP7"}, "source": ["## Analyzing the results\n", "\n", "Looking at our results table (in the screenshot below), we see our that any data points that involve the category `Sci/Tech` are not scoring 100%. Let's dive deeper.\n", "\n", "![Sci/Tech issue](./assets/table.png)\n", "\n", "## Reproducing an example\n", "\n", "First, let's see if we can reproduce this issue locally. We can test an article corresponding to the `Sci/Tech` category and reproduce the evaluation:\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "9x5dPZiIw1K8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A Cosmic Storm: When Galaxy Clusters Collide Astronomers have found what they are calling the perfect cosmic storm, a galaxy cluster pile-up so powerful its energy output is second only to the Big Bang.\n", "Sci/Tech\n", "Sci-Tech\n"]}], "source": ["sci_tech_article = [a for a in articles if \"Galaxy Clusters\" in a[\"input\"]][0]\n", "print(sci_tech_article[\"input\"])\n", "print(sci_tech_article[\"expected\"])\n", "\n", "out = classify_article(sci_tech_article[\"expected\"])\n", "print(out)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "bAQ3gHsdh3JU"}, "source": ["## Fixing the prompt\n", "\n", "Have you spotted the issue? It looks like we misspelled one of the categories in our prompt. The dataset's categories are `World`, `Sports`, `Business` and `Sci/Tech` - but we are using `Sci-Tech` in our prompt. Let's fix it:\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "pXzhZ8fdw1K9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sci/Tech\n"]}], "source": ["@braintrust.traced\n", "def classify_article(input):\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"\"\"You are an editor in a newspaper who helps writers identify the right category for their news articles,\n", "by reading the article's title. The category should be one of the following: World, Sports, Business or Sci/Tech. Reply with one word corresponding to the category.\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Article title: {input} Category:\".format(input=input),\n", "        },\n", "    ]\n", "    result = client.chat.completions.create(\n", "        model=MODEL,\n", "        messages=messages,\n", "        max_tokens=10,\n", "    )\n", "    category = result.choices[0].message.content\n", "    return category\n", "\n", "\n", "result = classify_article(sci_tech_article[\"input\"])\n", "\n", "print(result)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "bAQ3gHsdh3JU"}, "source": ["## Evaluate the new prompt\n", "\n", "The model classified the correct category `Sci/Tech` for this example. But, how do we know it works for the rest of the dataset? Let's run a new experiment to evaluate our new prompt:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pXzhZ8fdw1K9"}, "outputs": [], "source": ["await braintrust.<PERSON>(\n", "    \"Classifying News Articles Cookbook\",\n", "    data=articles,\n", "    task=classify_article,\n", "    scores=[<PERSON><PERSON><PERSON><PERSON>],\n", "    experiment_name=\"New Prompt\",\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {"id": "bAQ3gHsdh3JU"}, "source": ["## Conclusion\n", "\n", "Select the new experiment, and check it out. You should notice a few things:\n", "\n", "- Braintrust will automatically compare the new experiment to your previous one.\n", "- You should see the eval scores increase and you can see which test cases improved.\n", "- You can also filter the test cases by improvements to know exactly why the scores changed.\n", "\n", "![Compare](assets/inspect.gif)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "- [I ran an eval. Now what?](/blog/after-evals)\n", "- Add more [custom scorers](/docs/guides/functions/scorers#custom-scorers).\n", "- Try other models like xAI's [Grok 2](https://x.ai/blog/grok-2) or OpenAI's [o1](https://openai.com/o1/). To learn more about comparing evals across multiple AI models, check out this [cookbook](/docs/cookbook/recipes/ModelComparison).\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}