{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generating better GitHub issue titles with Braintrust\n", "\n", "This tutorial will teach you how to use Braintrust to generate better titles for Github issues, based on their\n", "content. This is a great way to learn how to work with text and evaluate subjective criteria, like summarization quality.\n", "\n", "We'll use a technique called **model graded evaluation** to automatically evaluate the newly generated titles\n", "against the original titles, and improve our prompt based on what we find.\n", "\n", "Before starting, please make sure that you have a Braintrust account. If you do not, please [sign up](https://www.braintrust.dev). After this tutorial, feel free to dig deeper by visiting [the docs](http://www.braintrust.dev/docs).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installing dependencies\n", "\n", "To see a list of dependencies, you can view the accompanying [package.json](https://github.com/braintrustdata/braintrust-cookbook/tree/main/examples/Github-Issues/package.json) file. Feel free to copy/paste snippets of this code to run in your environment, or use [tslab](https://github.com/yunabe/tslab) to run the tutorial in a Jupyter notebook.\n", "\n", "## Downloading the data\n", "\n", "We'll start by downloading some issues from Github using the `octokit` SDK. We'll use the popular open source project [next.js](https://github.com/vercel/next.js).\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import { Octokit } from \"@octokit/core\";\n", "\n", "const ISSUES = [\n", "  \"https://github.com/vercel/next.js/issues/59999\",\n", "  \"https://github.com/vercel/next.js/issues/59997\",\n", "  \"https://github.com/vercel/next.js/issues/59995\",\n", "  \"https://github.com/vercel/next.js/issues/59988\",\n", "  \"https://github.com/vercel/next.js/issues/59986\",\n", "  \"https://github.com/vercel/next.js/issues/59971\",\n", "  \"https://github.com/vercel/next.js/issues/59958\",\n", "  \"https://github.com/vercel/next.js/issues/59957\",\n", "  \"https://github.com/vercel/next.js/issues/59950\",\n", "  \"https://github.com/vercel/next.js/issues/59940\",\n", "];\n", "\n", "// Octokit.js\n", "// https://github.com/octokit/core.js#readme\n", "const octokit = new Octokit({\n", "  auth: process.env.GITHUB_ACCESS_TOKEN || \"Your Github Access Token\",\n", "});\n", "\n", "async function fetchIssue(url: string) {\n", "  // parse url of the form https://github.com/supabase/supabase/issues/15534\n", "  const [owner, repo, _, issue_number] = url!.trim().split(\"/\").slice(-4);\n", "\n", "  const data = await octokit.request(\n", "    \"GET /repos/{owner}/{repo}/issues/{issue_number}\",\n", "    {\n", "      owner,\n", "      repo,\n", "      issue_number: parseInt(issue_number),\n", "      headers: {\n", "        \"X-GitHub-Api-Version\": \"2022-11-28\",\n", "      },\n", "    }\n", "  );\n", "  return data.data;\n", "}\n", "\n", "const ISSUE_DATA = await Promise.all(ISSUES.map(fetchIssue));\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's take a look at one of the issues:\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The instrumentation hook is only called after visiting a route\n", "--------------------------------------------------------------\n", "### Link to the code that reproduces this issue\n", "\n", "https://github.com/daveyjones/nextjs-instrumentation-bug\n", "\n", "### To Reproduce\n", "\n", "```shell\n", "<NAME_EMAIL>:daveyjones/nextjs-instrumentation-bug.git\n", "cd nextjs-instrumentation-bug\n", "npm install\n", "npm run dev # The register function IS called\n", "npm run build && npm start # The register function IS NOT called until you visit http://localhost:3000\n", "```\n", "\n", "### Current vs. Expected behavior\n", "\n", "The `register` function should be called automatically after running `npm ...\n"]}], "source": ["console.log(ISSUE_DATA[0].title);\n", "console.log(\"-\".repeat(ISSUE_DATA[0].title.length));\n", "console.log(ISSUE_DATA[0].body.substring(0, 512) + \"...\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating better titles\n", "\n", "Let's try to generate better titles using a simple prompt. We'll use OpenAI, although you could try this out with any model that supports text generation.\n", "\n", "We'll start by initializing an OpenAI client and wrapping it with some Braintrust instrumentation. `wrapOpenAI`\n", "is initially a no-op, but later on when we use Braintrust, it will help us capture helpful debugging information about the model's performance.\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import { wrapOpenAI } from \"braintrust\";\n", "import { OpenAI } from \"openai\";\n", "\n", "const client = wrapOpenAI(\n", "  new OpenAI({\n", "    apiKey: process.env.OPENAI_API_KEY || \"Your OpenAI API Key\",\n", "  })\n", ");\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original title:  The instrumentation hook is only called after visiting a route\n", "Generated title: Next.js: `register` function not automatically called after build and start\n"]}], "source": ["import { ChatCompletionMessageParam } from \"openai/resources\";\n", "\n", "function titleGeneratorMessages(content: string): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content:\n", "        \"Generate a new title based on the github issue. Return just the title.\",\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content: \"Github issue: \" + content,\n", "    },\n", "  ];\n", "}\n", "\n", "async function generateTitle(input: string) {\n", "  const messages = titleGeneratorMessages(input);\n", "  const response = await client.chat.completions.create({\n", "    model: \"gpt-3.5-turbo\",\n", "    messages,\n", "    seed: 123,\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}\n", "\n", "const generatedTitle = await generateTitle(ISSUE_DATA[0].body);\n", "console.log(\"Original title: \", ISSUE_DATA[0].title);\n", "console.log(\"Generated title:\", generatedTitle);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scoring\n", "\n", "Ok cool! The new title looks pretty good. But how do we consistently and automatically evaluate whether the new titles are better than the old ones?\n", "\n", "With subjective problems, like summarization, one great technique is to use an LLM to grade the outputs. This is known as model graded evaluation. Below, we'll use a [summarization prompt](https://github.com/braintrustdata/autoevals/blob/main/templates/summary.yaml)\n", "from Braintrust's open source [autoevals](https://github.com/braintrustdata/autoevals) library. We encourage you to use these prompts, but also to copy/paste them, modify them, and create your own!\n", "\n", "The prompt uses [Chain of Thought](https://arxiv.org/abs/2201.11903) which dramatically improves a model's performance on grading tasks. Later, we'll see how it helps us debug the model's outputs.\n", "\n", "Let's try running it on our new title and see how it performs.\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: '<PERSON><PERSON><PERSON>',\n", "  score: 1,\n", "  metadata: {\n", "    rationale: \"Summary A ('The instrumentation hook is only called after visiting a route') is a partial and somewhat ambiguous statement. It does not specify the context of the 'instrumentation hook' or the technology involved.\\n\" +\n", "      \"Summary B ('Next.js: `register` function not automatically called after build and start') provides a clearer and more complete description. It specifies the technology ('Next.js') and the exact issue ('`register` function not automatically called after build and start').\\n\" +\n", "      'The original text discusses an issue with the `register` function in a Next.js application not being called as expected, which is directly reflected in Summary B.\\n' +\n", "      \"Summary B also aligns with the section 'Current vs. Expected behavior' from the original text, which states that the `register` function should be called automatically but is not until a route is visited.\\n\" +\n", "      \"Summary A lacks the detail that the issue is with the Next.js framework and does not mention the expectation of the `register` function's behavior, which is a key point in the original text.\",\n", "    choice: 'B'\n", "  },\n", "  error: undefined\n", "}\n"]}], "source": ["import { Summary } from \"autoevals\";\n", "\n", "await Summary({\n", "  output: generatedTitle,\n", "  expected: ISSUE_DATA[0].title,\n", "  input: ISSUE_DATA[0].body,\n", "  // In practice we've found gpt-4 class models work best for subjective tasks, because\n", "  // they are great at following criteria laid out in the grading prompts.\n", "  model: \"gpt-4-1106-preview\",\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initial evaluation\n", "\n", "Now that we have a way to score new titles, let's run an eval and see how our prompt performs across all 10 issues.\n"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Github Issues Cookbook',\n", "  experimentName: 'main-1706774628',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Github%20Issues%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Github%20Issues%20Cookbook/main-1706774628',\n", "  comparisonExperimentName: undefined,\n", "  scores: undefined,\n", "  metrics: undefined\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Github Issues Cookbook                   |  10% | 10/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Done!\n"]}], "source": ["import { <PERSON><PERSON>, login } from \"braintrust\";\n", "\n", "login({ apiKey: process.env.BRAINTUST_API_KEY || \"Your Braintrust API Key\" });\n", "\n", "await <PERSON><PERSON>(\"Github Issues Cookbook\", {\n", "  data: () =>\n", "    ISSUE_DATA.map((issue) => ({\n", "      input: issue.body,\n", "      expected: issue.title,\n", "      metadata: issue,\n", "    })),\n", "  task: generateTitle,\n", "  scores: [\n", "    async ({ input, output, expected }) =>\n", "      Summary({\n", "        input,\n", "        output,\n", "        expected,\n", "        model: \"gpt-4-1106-preview\",\n", "      }),\n", "  ],\n", "});\n", "\n", "console.log(\"Done!\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Great! We got an initial result. If you follow the link, you'll see an eval result showing an initial score of 40%.\n", "\n", "![Initial eval result](./assets/initial-experiment.png)\n", "\n", "## Debugging failures\n", "\n", "Let's dig into a couple examples to see what's going on. Thanks to the instrumentation we added earlier, we can see the model's reasoning for its scores.\n", "\n", "Issue https://github.com/vercel/next.js/issues/59995:\n", "\n", "![output-expected](./assets/output-expected.png)\n", "![reasons](./assets/reasons.png)\n", "\n", "Issue https://github.com/vercel/next.js/issues/59986:\n", "\n", "![output-expected-2](./assets/output-expected-2.png)\n", "![reasons2](./assets/reasons-2.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improving the prompt\n", "\n", "Hmm, it looks like the model is missing certain key details. Let's see if we can improve our prompt to encourage the model to include more details, without being too verbose.\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["function titleGeneratorMessages(content: string): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `Generate a new title based on the github issue. The title should include all of the key\n", "identifying details of the issue, without being longer than one line. Return just the title.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content: \"Github issue: \" + content,\n", "    },\n", "  ];\n", "}\n", "\n", "async function generateTitle(input: string) {\n", "  const messages = titleGeneratorMessages(input);\n", "  const response = await client.chat.completions.create({\n", "    model: \"gpt-3.5-turbo\",\n", "    messages,\n", "    seed: 123,\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Re-evaluating\n", "\n", "Now that we've tweaked our prompt, let's see how it performs by re-running our eval.\n"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Github Issues Cookbook',\n", "  experimentName: 'main-1706774676',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Github%20Issues%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Github%20Issues%20Cookbook/main-1706774676',\n", "  comparisonExperimentName: 'main-1706774628',\n", "  scores: {\n", "    Summary: {\n", "      name: '<PERSON><PERSON><PERSON>',\n", "      score: 0.7,\n", "      diff: 0.29999999999999993,\n", "      improvements: 3,\n", "      regressions: 0\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3292001008987427,\n", "      unit: 's',\n", "      diff: -0.002199888229370117,\n", "      improvements: 7,\n", "      regressions: 3\n", "    }\n", "  }\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Github Issues Cookbook                   |  10% | 10/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["All done!\n"]}], "source": ["await <PERSON><PERSON>(\"Github Issues Cookbook\", {\n", "  data: () =>\n", "    ISSUE_DATA.map((issue) => ({\n", "      input: issue.body,\n", "      expected: issue.title,\n", "      metadata: issue,\n", "    })),\n", "  task: generateTitle,\n", "  scores: [\n", "    async ({ input, output, expected }) =>\n", "      Summary({\n", "        input,\n", "        output,\n", "        expected,\n", "        model: \"gpt-4-1106-preview\",\n", "      }),\n", "  ],\n", "});\n", "console.log(\"All done!\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Wow, with just a simple change, we're able to boost summary performance by 30%!\n", "\n", "![Improved eval result](./assets/second-experiment.png)\n", "\n", "## Parting thoughts\n", "\n", "This is just the start of evaluating and improving this AI application. From here, you should dig into\n", "individual examples, verify whether they legitimately improved, and test on more data. You can even use\n", "[logging](https://www.braintrust.dev/docs/guides/logging) to capture real-user examples and incorporate\n", "them into your evals.\n", "\n", "Happy evaluating!\n", "\n", "![improvements](./assets/improvements.gif)\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}