{"cells": [{"cell_type": "markdown", "metadata": {"id": "quNDJhiCJ-AI"}, "source": ["# Braintrust Text2SQL Tutorial\n", "\n", "This tutorial will teach you how to create an application that converts natural language questions into SQL queries, and then evaluating how well\n", "the queries work. We'll even make an improvement to the prompts, and evaluate the impact! By the time you finish this tutorial, you should be ready\n", "to run your own experiments.\n", "\n", "Before starting, please make sure that you have a Braintrust account. If you do not, please [sign up](https://braintrust.dev).\n"]}, {"cell_type": "markdown", "metadata": {"id": "2tMYg0jyKelb"}, "source": ["## Setting up the environment\n", "\n", "The next few commands will install some libraries and include some helper code for the text2sql application. Feel free to copy/paste/tweak/reuse this code in your own tools.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "pGuPB2SqUWdZ"}, "outputs": [], "source": ["!pip install braintrust duckdb datasets openai pyarrow python-Levenshtein autoevals"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We're going to use a public dataset called [WikiSQL](https://github.com/salesforce/WikiSQL) that contains natural language questions and their corresponding SQL queries.\n"]}, {"cell_type": "markdown", "metadata": {"id": "fajc2XybK_gt"}, "source": ["## Exploring the data\n", "\n", "In this section, we'll take a look at the dataset and ground truth text/sql pairs to better understand the problem and data.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "Q_OdDHOhUk0K"}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "data = list(load_dataset(\"wikisql\")[\"test\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here's an example question:\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "GqCA2b4lVNEo"}, "outputs": [{"data": {"text/plain": ["'What club was in toronto 1995-96'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["idx = 1\n", "data[idx][\"question\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll use Arrow and DuckDB to help us explore the data and run SQL queries on it:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NCZ2D9_KWAuE"}, "outputs": [{"data": {"text/plain": ["┌──────────────────────┬─────────┬───────────────┬────────────────┬──────────────────┬──────────────────┐\n", "│        Player        │   No.   │  Nationality  │    Position    │ Years in Toronto │ School/Club Team │\n", "│       varchar        │ varchar │    varchar    │    varchar     │     varchar      │     varchar      │\n", "├──────────────────────┼─────────┼───────────────┼────────────────┼──────────────────┼──────────────────┤\n", "│ <PERSON><PERSON><PERSON><PERSON> │ 25      │ Serbia        │ Center         │ 1999-2000        │ Barton CC (KS)   │\n", "│ <PERSON>        │ 31      │ United States │ Guard          │ 1997-98          │ Michigan State   │\n", "│ <PERSON>   │ N/A     │ United States │ Forward        │ 2013-present     │ <PERSON><PERSON><PERSON>           │\n", "│ <PERSON>      │ 7, 21   │ United States │ Guard          │ 1995-96          │ Arkansas         │\n", "│ <PERSON>        │ 33, 34  │ United States │ Forward-Center │ 1995-98          │ Tennessee State  │\n", "│ <PERSON>           │ 9       │ United States │ Forward        │ 1998             │ Alabama          │\n", "│ <PERSON><PERSON><PERSON>           │ 5       │ United States │ Guard-Forward  │ 2003-06          │ Michigan         │\n", "│ <PERSON><PERSON>        │ 31      │ United States │ Guard          │ 2012-present     │ Washington       │\n", "└──────────────────────┴─────────┴───────────────┴────────────────┴──────────────────┴──────────────────┘"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import duckdb\n", "import pyarrow as pa\n", "\n", "\n", "def get_table(table):\n", "    rows = [\n", "        {h: row[i] for (i, h) in enumerate(table[\"header\"])} for row in table[\"rows\"]\n", "    ]\n", "\n", "    return pa.Table.from_pylist(rows)\n", "\n", "\n", "table = get_table(data[idx][\"table\"])\n", "duckdb.arrow(table).query(\"table\", 'SELECT * FROM \"table\"')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In WikiSQL, the queries are formatted as a series of projection and filter expressions. Although there is a `human_readable` field, it's not valid SQL!\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'human_readable': 'SELECT School/Club Team FROM table WHERE Years in Toronto = 1995-96',\n", " 'sel': 5,\n", " 'agg': 0,\n", " 'conds': {'column_index': [4],\n", "  'operator_index': [0],\n", "  'condition': ['1995-96']}}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data[idx][\"sql\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's define a `codegen_query` function that turns it into executable SQL.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8GDsB2taWDol"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"School/Club Team\" FROM \"table\" WHERE \"Years in Toronto\" ILIKE '1995-96'\n"]}], "source": ["AGG_OPS = [None, \"MAX\", \"MIN\", \"COUNT\", \"SUM\", \"AVG\"]\n", "COND_OPS = [\" ILIKE \", \">\", \"<\"]  # , \"OP\"]\n", "\n", "\n", "def esc_fn(s):\n", "    return f'''\"{s.replace('\"', '\"\"')}\"'''\n", "\n", "\n", "def esc_value(s):\n", "    if isinstance(s, str):\n", "        return s.replace(\"'\", \"''\")\n", "    else:\n", "        return s\n", "\n", "\n", "def codegen_query(query):\n", "    header = query[\"table\"][\"header\"]\n", "\n", "    projection = f\"{esc_fn(header[query['sql']['sel']])}\"\n", "\n", "    agg_op = AGG_OPS[query[\"sql\"][\"agg\"]]\n", "    if agg_op is not None:\n", "        projection = f\"{agg_op}({projection})\"\n", "\n", "    conds = query[\"sql\"][\"conds\"]\n", "\n", "    filters = \" and \".join(\n", "        [\n", "            f\"\"\"{esc_fn(header[field])}{COND_OPS[cond]}'{esc_value(value)}'\"\"\"\n", "            for (field, cond, value) in zip(\n", "                conds[\"column_index\"], conds[\"operator_index\"], conds[\"condition\"]\n", "            )\n", "        ]\n", "    )\n", "\n", "    if filters:\n", "        filters = f\" WHERE {filters}\"\n", "\n", "    return f'SELECT {projection} FROM \"table\"{filters}'\n", "\n", "\n", "gt_sql = codegen_query(data[idx])\n", "print(gt_sql)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we can run this SQL directly.\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "OkT_Ykt1WWvq"}, "outputs": [{"data": {"text/plain": ["┌──────────────────┐\n", "│ School/Club Team │\n", "│     varchar      │\n", "├──────────────────┤\n", "│ Arkansas         │\n", "└──────────────────┘"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["duckdb.arrow(table).query(\"table\", gt_sql)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "oaKzmZuST7CU"}, "outputs": [], "source": ["import duckdb\n", "import pyarrow as pa\n", "from datasets import load_dataset\n", "from Levenshtein import distance\n", "\n", "NUM_TEST_EXAMPLES = 10\n", "\n", "\n", "# Define some helper functions\n", "\n", "\n", "def green(s):\n", "    return \"\\x1b[32m\" + s + \"\\x1b[0m\"\n", "\n", "\n", "def run_query(sql, table_record):\n", "    table = get_table(table_record)  # noqa\n", "    rel_from_arrow = duckdb.arrow(table)\n", "\n", "    result = rel_from_arrow.query(\"table\", sql).fetchone()\n", "    if result and len(result) > 0:\n", "        return result[0]\n", "    return None\n", "\n", "\n", "def score(r1, r2):\n", "    if r1 is None and r2 is None:\n", "        return 1\n", "    if r1 is None or r2 is None:\n", "        return 0\n", "\n", "    r1, r2 = str(r1), str(r2)\n", "\n", "    total_len = max(len(r1), len(r2))\n", "    return 1 - distance(r1, r2) / total_len"]}, {"cell_type": "markdown", "metadata": {"id": "RKzPxqhNLMgV"}, "source": ["## Running your first experiment\n", "\n", "In this section, we'll create our first experiment and analyze the results in Braintrust.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Xv-jKnOGWa1T"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"School/Club Team\"\n", "FROM \"table\"\n", "WHERE \"Years in Toronto\" = '1995-96'\n"]}, {"data": {"text/plain": ["┌──────────────────┐\n", "│ School/Club Team │\n", "│     varchar      │\n", "├──────────────────┤\n", "│ Arkansas         │\n", "└──────────────────┘"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "from braintrust import wrap_openai\n", "from openai import OpenAI\n", "\n", "client = wrap_openai(\n", "    OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\", \"Your OPENAI_API_KEY here\"))\n", ")\n", "\n", "\n", "def text2sql(input):\n", "    table = input[\"table\"]\n", "    meta = \"\\n\".join(f'\"{h}\"' for h in table[\"header\"])\n", "\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"\n", "Print a SQL query (over a table named \"table\" quoted with double quotes) that answers the question below.\n", "\n", "You have the following columns:\n", "{meta}\n", "\n", "The user will provide a question. Reply with a valid ANSI SQL query that answers the question, and nothing else.\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Question: {input['question']}\",\n", "        },\n", "    ]\n", "\n", "    resp = client.chat.completions.create(\n", "        model=\"gpt-3.5-turbo\",\n", "        messages=messages,\n", "    )\n", "\n", "    sql_text = resp.choices[0].message.content\n", "    return sql_text.rstrip(\";\")\n", "\n", "\n", "output_sql = text2sql(data[idx])\n", "print(output_sql)\n", "\n", "duckdb.arrow(table).query(\"table\", output_sql)"]}, {"cell_type": "markdown", "metadata": {"id": "d1z_zEhOLYfy"}, "source": ["Exciting! Now that we've tested it out on an example, we can run an evaluation on a bigger dataset to understand how well the prompt works.\n", "\n", "## Running an eval\n", "\n", "To run an eval, we simply need to stitch together the pieces we've already created into the `Eval()` function, which takes:\n", "\n", "- The data you want to evaluate\n", "- A `task` function that, given some input, returns an output\n", "- One or more scoring functions that evaluate the output.\n", "\n", "Let's start by logging into Braintrust. You can technically skip this step if you've set `BRAINTRUST_API_KEY` in your environment.\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "fADUk1JpdEz5"}, "outputs": [], "source": ["import braintrust\n", "\n", "braintrust.login(\n", "    api_key=os.environ.get(\"BRAINTRUST_API_KEY\", \"Your BRAINTRUST_API_KEY here\")\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scoring functions\n", "\n", "Next, we need to figure out how we'll score the outputs. One way is to string compare the SQL queries. This is not a perfect signal, because two different query strings might return the correct result, but it is a useful signal about how different the generated query is from the ground truth.\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["Score(name='<PERSON><PERSON><PERSON><PERSON>', score=0.9113924050632911, metadata={}, error=None)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from autoevals import Levenshtein\n", "\n", "Levenshtein().eval(output=output_sql, expected=gt_sql)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A more robust way to test the queries is to run them on a database and compare the results. We'll use DuckDB for this. We'll define a scoring function that runs the generated SQL and compares the results to the ground truth.\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["Score(name='SQL Result', score=1.0, metadata={}, error=None)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from autoevals import Score\n", "\n", "\n", "@braintrust.traced\n", "def result_score(output, expected, input):\n", "    expected_answer = run_query(expected, input[\"table\"])\n", "\n", "    # These log statements allow us to see the expected and output values in the Braintrust UI\n", "    braintrust.current_span().log(expected=expected_answer)\n", "\n", "    try:\n", "        output_answer = run_query(output, input[\"table\"])\n", "    except Exception as e:\n", "        return Score(name=\"SQL Result\", score=0, metadata={\"message\": f\"Error: {e}\"})\n", "\n", "    braintrust.current_span().log(output=output_answer)\n", "\n", "    return Score(\n", "        name=\"SQL Result\",\n", "        score=Levenshtein()(output=output_answer, expected=expected_answer).score,\n", "    )\n", "\n", "\n", "result_score(output_sql, gt_sql, data[idx])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment text-2-sql-1706754968 is running at https://www.braintrust.dev/app/braintrust.dev/p/Text2SQL%20Cookbook/text-2-sql-1706754968\n", "Text2SQL Cookbook (data): 10it [00:00, 42711.85it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f828afe3edfe4ec3bcdaecdf3e75d6b9", "version_major": 2, "version_minor": 0}, "text/plain": ["Text2SQL Cookbook (tasks):   0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "See results for text-2-sql-1706754968 at https://www.braintrust.dev/app/braintrust.dev/p/Text2SQL%20Cookbook/text-2-sql-1706754968\n"]}], "source": ["from braintrust import EvalAsync\n", "\n", "await <PERSON><PERSON><PERSON>(\n", "    \"Text2SQL Cookbook\",\n", "    data=[\n", "        {\"input\": d, \"expected\": codegen_query(d), \"metadata\": {\"idx\": i}}\n", "        for (i, d) in enumerate(data[:NUM_TEST_EXAMPLES])\n", "    ],\n", "    task=text2sql,\n", "    scores=[<PERSON><PERSON><PERSON><PERSON>, result_score],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "m8IJNQtamRDH"}, "source": ["Once the eval completes, you can click on the link to see the results in the Braintrust UI.\n", "\n", "![Eval results](./assets/initial-results.png)\n", "\n", "Take a look at the failures. Feel free to explore individual examples, filter down to low `answer` scores, etc. You should notice that `idx=8` is one of the failures. Let's debug it and see if we can improve the prompt.\n", "\n", "![idx=4](./assets/idx-8.png)\n", "\n", "## Debugging a failure\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll first set `idx=8` and reproduce the failure.\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"id": "skiosYHPmsP8"}, "outputs": [], "source": ["idx = 8"]}, {"cell_type": "markdown", "metadata": {"id": "TlqC41AjnTHa"}, "source": ["Here is the ground truth:\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"id": "PN10oMOWnMaw"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What are the nationalities of the player picked from Thunder Bay Flyers (ushl)\n", "┌─────────┬──────────────────┬────────────┬────────────────┬──────────────────────┬────────────────────────────────────┐\n", "│  Pick   │      Player      │  Position  │  Nationality   │       NHL team       │      College/junior/club team      │\n", "│ varchar │     varchar      │  varchar   │    varchar     │       varchar        │              varchar               │\n", "├─────────┼──────────────────┼────────────┼────────────────┼──────────────────────┼────────────────────────────────────┤\n", "│ 27      │ <PERSON><PERSON><PERSON>   │ Defence    │ Canada         │ Florida Panthers     │ Saskatoon Blades (WHL)             │\n", "│ 28      │ <PERSON>  │ <PERSON> Wing  │ Sweden         │ Mighty Ducks of An…  │ HV71 (Sweden)                      │\n", "│ 29      │ <PERSON><PERSON> │ Defence    │ Czech Republic │ Ottawa Senators      │ HC České <PERSON> ( Czech Repu…  │\n", "│ 30      │ <PERSON><PERSON>      │ Defence    │ United States  │ Winnipeg Jets        │ Seattle Thunderbirds (WHL)         │\n", "│ 31      │ <PERSON>   │ <PERSON> Wing │ Canada         │ Florida Panthers     │ Spokane Chiefs (WHL)               │\n", "└─────────┴──────────────────┴────────────┴────────────────┴──────────────────────┴────────────────────────────────────┘\n", "\n", "SELECT \"Nationality\" FROM \"table\" WHERE \"College/junior/club team\" ILIKE 'Thunder Bay Flyers (USHL)'\n", "┌─────────────┐\n", "│ Nationality │\n", "│   varchar   │\n", "├─────────────┤\n", "│ Canada      │\n", "└─────────────┘\n", "\n"]}], "source": ["print(data[idx][\"question\"])\n", "\n", "table = get_table(data[idx][\"table\"])\n", "print(duckdb.arrow(table).query(\"table\", 'SELECT * FROM \"table\" LIMIT 5'))\n", "\n", "gt_sql = codegen_query(data[idx])\n", "print(gt_sql)\n", "\n", "print(duckdb.arrow(table).query(\"table\", gt_sql))"]}, {"cell_type": "markdown", "metadata": {"id": "bA-1WxsrnilJ"}, "source": ["And then what the model spits out:\n"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"id": "eRmflA8lnkJy"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT DISTINCT \"Nationality\"\n", "FROM \"table\"\n", "WHERE \"College/junior/club team\" = 'Thunder Bay Flyers (ushl)'\n"]}, {"data": {"text/plain": ["┌─────────────┐\n", "│ Nationality │\n", "│   varchar   │\n", "├─────────────┤\n", "│   0 rows    │\n", "└─────────────┘"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["output_sql = text2sql(data[idx])\n", "print(output_sql)\n", "duckdb.arrow(table).query(\"table\", output_sql)"]}, {"cell_type": "markdown", "metadata": {"id": "LXta8DuJnnuC"}, "source": ["Hmm, if only the model knew that `'ushl'` is actually capitalized in the data. Let's fix this by providing some sample data for each column:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Gpvv-xPAmQDQ"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT Nationality FROM \"table\" WHERE \"College/junior/club team\" = 'Thunder Bay Flyers (USHL)'\n"]}, {"data": {"text/plain": ["┌─────────────┐\n", "│ Nationality │\n", "│   varchar   │\n", "├─────────────┤\n", "│ Canada      │\n", "└─────────────┘"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["def text2sql(input):\n", "    table = input[\"table\"]\n", "    rows = [\n", "        {h: row[i] for (i, h) in enumerate(table[\"header\"])} for row in table[\"rows\"]\n", "    ]\n", "    meta = \"\\n\".join(f'\"{h}\": {[row[h] for row in rows[:10]]}' for h in table[\"header\"])\n", "\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": f\"\"\"\n", "Print a SQL query (over a table named \"table\" quoted with double quotes) that answers the question below.\n", "\n", "You have the following columns (each with some sample data). Make sure to use the correct\n", "column names for each data value:\n", "\n", "{meta}\n", "\n", "The user will provide a question. Reply with a valid ANSI SQL query that answers the question, and nothing else.\"\"\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Question: {input['question']}\",\n", "        },\n", "    ]\n", "\n", "    resp = client.chat.completions.create(\n", "        model=\"gpt-3.5-turbo\",\n", "        messages=messages,\n", "    )\n", "\n", "    sql_text = resp.choices[0].message.content\n", "    return sql_text.rstrip(\";\")\n", "\n", "\n", "output_sql = text2sql(data[idx])\n", "print(output_sql)\n", "\n", "duckdb.arrow(table).query(\"table\", output_sql)"]}, {"cell_type": "markdown", "metadata": {"id": "JO6-HGKGWfOI"}, "source": ["Ok great! Now let's re-run the loop with this new version of the code.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "E7o5OSZBWzjO"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment text-2-sql-1706755609 is running at https://www.braintrust.dev/app/braintrust.dev/p/Text2SQL%20Cookbook/text-2-sql-1706755609\n", "Text2SQL Cookbook (data): 10it [00:00, 22562.15it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9d1aa744ef6040e3b3bbf5e57be1fc5a", "version_major": 2, "version_minor": 0}, "text/plain": ["Text2SQL Cookbook (tasks):   0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "text-2-sql-1706755609 compared to text-2-sql-1706754968:\n", "63.82% (+10.33%) 'SQL Result'  score\t(2 improvements, 1 regressions)\n", "80.53% (+03.66%) 'Levenshtein' score\t(5 improvements, 3 regressions)\n", "\n", "1.22s (-16.20%) 'duration'\t(8 improvements, 2 regressions)\n", "\n", "See results for text-2-sql-1706755609 at https://www.braintrust.dev/app/braintrust.dev/p/Text2SQL%20Cookbook/text-2-sql-1706755609\n"]}], "source": ["await <PERSON><PERSON><PERSON>(\n", "    \"Text2SQL Cookbook\",\n", "    data=[\n", "        {\"input\": d, \"expected\": codegen_query(d), \"metadata\": {\"idx\": i}}\n", "        for (i, d) in enumerate(data[:NUM_TEST_EXAMPLES])\n", "    ],\n", "    task=text2sql,\n", "    scores=[<PERSON><PERSON><PERSON><PERSON>, result_score],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "1Zm138QEk-db"}, "source": ["![Second experiment](./assets/second-experiment.png)\n", "\n", "## Wrapping up\n", "\n", "Congrats 🎉. You've run your first couple of experiments. Now, return back to the tutorial docs to proceed to the next step where we'll analyze the experiments.\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 0}