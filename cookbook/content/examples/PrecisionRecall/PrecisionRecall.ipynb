{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating the precision and recall of an emotion classifier\n", "In this cookbook, we'll explore how to evaluate an LLM classifier in Braintrust using custom scoring functions that measure precision and recall. We'll use the [GoEmotions dataset](https://huggingface.co/datasets/google-research-datasets/go_emotions), which contains Reddit comments labeled with 28 different emotions. This dataset is interesting since each comment can be labeled with multiple emotions; for example, a single message might express both \"excitement\" and \"anger.\"\n", "\n", "We'll build two classifiers-a random baseline and an LLM-based approach using OpenAI's GPT-4o. By comparing their performance using custom scorers, we'll demonstrate how to effectively measure and then improve your LLM's accuracy on complex classification tasks.\n", "\n", "## Getting started\n", "\n", "To get started, you'll need [Braintrust](https://www.braintrust.dev/signup) and [OpenAI](https://platform.openai.com/) accounts, along with their corresponding API keys. Add your `BRAINTRUST_API_KEY` and `OPENAI_API_KEY` to your environment:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_BRAINTRUST_API_KEY\"\n", "export OPENAI_API_KEY=\"YOUR_OPENAI_API_KEY\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<Callout type=\"info\">\n", "Best practice is to export your API key as an environment variable. However, to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's start by installing the required Python dependencies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install braintrust openai datasets autoevals pydantic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we'll import all of the modules we need and initialize our OpenAI client. We're wrapping the client so that we have access to Braintrust features. "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import os\n", "import random\n", "from typing import List, Literal, Union, Set\n", "\n", "import autoevals\n", "from datasets import load_dataset\n", "import braintrust\n", "import openai\n", "from pydantic import BaseModel, Field, create_model\n", "\n", "# Uncomment if you want to hardcode your API keys\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_BRAINTRUST_API_KEY\"\n", "# os.environ[\"OPENAI_API_KEY\"] = \"YOUR_OPENAI_API_KEY\"\n", "\n", "openai_client = braintrust.wrap_openai(openai.OpenAI())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Type definitions and data models\n", "\n", "To ensure that the LLM classifier outputs only emotions predefined by the dataset, we'll leverage OpenAI's structured outputs feature by providing a Pydantic `DynamicModel` representing the classification output."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["EMOTIONS = [\n", "    \"admiration\",\n", "    \"amusement\",\n", "    \"anger\",\n", "    \"annoyance\",\n", "    \"approval\",\n", "    \"caring\",\n", "    \"confusion\",\n", "    \"curiosity\",\n", "    \"desire\",\n", "    \"disappointment\",\n", "    \"disapproval\",\n", "    \"disgust\",\n", "    \"embarrassment\",\n", "    \"excitement\",\n", "    \"fear\",\n", "    \"gratitude\",\n", "    \"grief\",\n", "    \"joy\",\n", "    \"love\",\n", "    \"nervousness\",\n", "    \"optimism\",\n", "    \"pride\",\n", "    \"realization\",\n", "    \"relief\",\n", "    \"remorse\",\n", "    \"sadness\",\n", "    \"surprise\",\n", "    \"neutral\",\n", "]\n", "\n", "EmotionType = Literal[tuple(EMOTIONS)]\n", "\n", "EmotionClassification = create_model(\n", "    \"EmotionClassification\", emotions=(List[EmotionType], ...)\n", ")\n", "\n", "\n", "def load_data(limit: int = 100):\n", "    ds = load_dataset(\"google-research-datasets/go_emotions\", \"raw\")\n", "    for i, item in list(enumerate(ds[\"train\"]))[:limit]:\n", "        actual_emotions = [emotion for emotion in EMOTIONS if item.get(emotion, 0) == 1]\n", "        yield {\n", "            \"input\": item[\"text\"],\n", "            \"expected\": actual_emotions,\n", "            \"metadata\": {\"subreddit\": item[\"subreddit\"], \"author\": item[\"author\"]},\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating the classifiers\n", "\n", "We'll implement two different approaches to emotion classification:\n", "\n", "1. A random classifier that assigns 1-3 emotions randomly from our predefined list. This random baseline helps us verify that our LLM classifier performs meaningfully better than chance predictions.\n", "\n", "2. An LLM-based classifier using GPT-4o that uses [structured outputs](https://platform.openai.com/docs/guides/structured-outputs) to ensure valid emotion labels."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def llm_classifier(text: str) -> EmotionClassification:\n", "    prompt = (\n", "        f\"Analyze the emotional content in this text and STRICTLY classify it using ONLY the following emotion labels:\\n\"\n", "        f\"{', '.join(EMOTIONS)}\\n\\n\"\n", "        f\"IMPORTANT: You must ONLY use emotions from the above list. Do not use any other emotion labels and DO NOT repeat emotions.\\n\\n\"\n", "        f\"Text: {text}\\n\\n\"\n", "        f\"Respond with a JSON object containing:\\n\"\n", "        f\"- emotions: array of emotions from the provided list only\\n\"\n", "        f\"Remember: Only use emotions from the provided list. If you see an emotion that isn't in the list, map it to the closest matching emotion from the list.\"\n", "    )\n", "\n", "    response = openai_client.beta.chat.completions.parse(\n", "        model=\"gpt-4o\",\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0,\n", "        response_format=EmotionClassification,\n", "    )\n", "\n", "    result = response.choices[0].message.content\n", "    return EmotionClassification.model_validate_json(result)\n", "\n", "\n", "def random_classifier(text: str) -> EmotionClassification:\n", "    num_emotions = random.randint(1, 3)\n", "    selected_emotions = random.sample(EMOTIONS, num_emotions)\n", "    return EmotionClassification(\n", "        emotions=selected_emotions,\n", "        confidence=random.random(),\n", "        rationale=\"Random selection\",\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing evaluation metrics \n", "\n", "Because each comment can express multiple emotions, we're going to use three metrics to assess the performance of our LLM classifier:\n", "\n", "`Precision` measures prediction accuracy by calculating the fraction of true positive predictions out of all positive predictions, expressed as (true positives)/(true positives + false positives). If we predict \"joy\" and \"anger\" for a comment that only expresses \"joy,\" we have one true positive and one false positive, so the precision is 0.5. Higher precision means fewer false positives.\n", "\n", "`Recall` measures the fraction of actual emotions that were correctly identified, expressed as (true positives)/(true positives + false negatives). If a comment expresses \"sadness\" and \"fear,\" but we only catch \"sadness,\" we have one true positive and one false negative, so the recall is 0.5. Higher recall means fewer missed emotions.\n", "\n", "`F1 Score` combines precision and recall into a single metric since improving one can hurt the other. It helps balance being too strict (high precision, low recall) and too lenient (high recall, low precision)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def emotion_precision(\n", "    output: EmotionClassification, expected: List[EmotionType]\n", ") -> float:\n", "    expected_set = set(expected)\n", "    output_set = set(output.emotions)\n", "    true_positives = len(output_set & expected_set)\n", "    false_positives = len(output_set - expected_set)\n", "    return (\n", "        true_positives / (true_positives + false_positives)\n", "        if (true_positives + false_positives) > 0\n", "        else 1.0\n", "    )\n", "\n", "\n", "def emotion_recall(output: EmotionClassification, expected: List[EmotionType]) -> float:\n", "    expected_set = set(expected)\n", "    output_set = set(output.emotions)\n", "    true_positives = len(output_set & expected_set)\n", "    false_negatives = len(expected_set - output_set)\n", "    return (\n", "        true_positives / (true_positives + false_negatives)\n", "        if (true_positives + false_negatives) > 0\n", "        else 1.0\n", "    )\n", "\n", "\n", "def emotion_f1(output: EmotionClassification, expected: List[EmotionType]) -> float:\n", "    prec = emotion_precision(output, expected)\n", "    rec = emotion_recall(output, expected)\n", "    return 2 * (prec * rec) / (prec + rec) if (prec + rec) > 0 else 0.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running evaluations\n", "\n", "Finally, let's set up our evaluation pipeline using Braintrust:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_evaluations(num_samples: int = 100):\n", "    data = list(load_data(limit=num_samples))\n", "\n", "    braintrust.Eval(\n", "        \"emotion-classification-cookbook-main\",\n", "        data=data,  # Return the preloaded data\n", "        task=random_classifier,\n", "        scores=[emotion_precision, emotion_recall, emotion_f1],\n", "        metadata={\"classifier_type\": \"random\"},\n", "        experiment_name=\"random-classifier\",\n", "    )\n", "\n", "    braintrust.Eval(\n", "        \"emotion-classification-cookbook-main\",\n", "        data=data,  # Return the preloaded data\n", "        task=llm_classifier,\n", "        scores=[emotion_precision, emotion_recall, emotion_f1],\n", "        metadata={\"classifier_type\": \"llm\", \"model\": \"gpt-4o\"},\n", "        experiment_name=\"llm-classifier\",\n", "    )\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    run_evaluations(num_samples=100)  # Adjust the number of samples as needed"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing the results\n", "\n", "Once you run the evaluations, you'll see the results in your Braintrust dashboard. The LLM classifier should significantly outperform the random baseline across all metrics.\n", "\n", "![results.png](assets/results.png)\n", "\n", "Key features to examine:\n", "- Compare precision and recall scores between our runs\n", "- Look at specific examples where the LLM fails \n", "- Analyze cases where multiple emotions are present\n", "\n", "One of the more common next steps is to answer questions like \"What is my model's precision on amusement?\" or \"What is my model's recall on anger?\". Braintrust makes this easy to do with our filtering features in the UI.\n", "\n", "Select **Filter**, **Output**, then **contains**, and enter the emotion you want to look at, such as \"amusement\" or \"anger\" in the input box. The precision and recall scores will then be specific to the selected class.\n", "\n", "![filter.png](assets/filters.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "There are several ways to improve this emotion classifier, including:\n", "- Experimenting with different prompts and instructions, or even a series of prompts. \n", "- Adding a `rationale` to the output for each emotion to help us identify the root cause of the classifier's failures and improve the prompts accordingly. \n", "- Trying other models like xAI's [Grok 2](https://x.ai/blog/grok-2) or OpenAI's [o1](https://openai.com/o1/). To learn more about comparing evals across multiple AI models, check out this [cookbook](https://www.braintrust.dev/docs/cookbook/recipes/ModelComparison).\n", "- Adding more sophisticated scoring functions or [LLM-based scoring functions](https://www.braintrust.dev/docs/guides/evals/write#score-using-ai) to evaluate something like \"anger\" recall."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}