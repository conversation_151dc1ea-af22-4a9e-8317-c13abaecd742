{"cells": [{"cell_type": "markdown", "metadata": {"id": "vigvBmpNxHvb"}, "source": ["# Coda RAG Demo\n", "\n", "Large language models have gotten extremely good at answering general questions but often struggle with specific domain knowledge. When building AI-powered help desks or knowledge bases, this limitation becomes apparent. Retrieval-augmented generation (RAG) addresses this challenge by incorporating relevant information from external documents into the model's context.\n", "\n", "In this cookbook, we'll build and evaluate an AI application that answers questions about [Coda's Help Desk](https://help.coda.io/en/) documentation. Using Braintrust, we'll compare baseline and RAG-enhanced responses against expected answers to quantitatively measure the improvement.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started\n", "\n", "To follow along, start by installing the required packages:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "rTIA4DVQw1K7", "scrolled": true}, "outputs": [], "source": ["pip install autoevals braintrust requests openai lancedb markdownify asyncio pyarrow\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, make sure you have a [Braintrust](https://www.braintrust.dev/signup) account, along with an [OpenAI API key](https://platform.openai.com/). To authenticate with Braintrust, export your `BRAINTRUST_API_KEY` as an environment variable:\n", "```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll import our modules and define constants:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import tempfile\n", "from typing import List\n", "\n", "import autoevals\n", "import braintrust\n", "import markdownify\n", "import lancedb\n", "import openai\n", "import requests\n", "import asyncio\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "# Model selection constants\n", "QA_GEN_MODEL = \"gpt-4o-mini\"\n", "QA_ANSWER_MODEL = \"gpt-4o-mini\"\n", "QA_GRADING_MODEL = \"gpt-4o-mini\"\n", "RELEVANCE_MODEL = \"gpt-4o-mini\"\n", "\n", "# Data constants\n", "NUM_SECTIONS = 20\n", "NUM_QA_PAIRS = 20  # Increase this number to test at a larger scale\n", "TOP_K = 2  # Number of relevant sections to retrieve\n", "\n", "# Uncomment the following line to hardcode your API key\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_API_KEY_HERE\""]}, {"cell_type": "markdown", "metadata": {"id": "c0hvsPRZLCUz"}, "source": ["## Download Markdown docs from Coda's Help Desk\n", "\n", "Let's start by downloading the Coda docs and splitting them into their constituent Markdown sections."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloaded 996 Markdown sections. Here are the first 3:\n", "\n", "Section 1:\n", "{'doc_id': '8179780', 'section_id': 0, 'markdown': \"Not all Coda docs are used in the same way. You'll inevitably have a few that you use every week, and some that you'll only use once. This is where starred docs can help you stay organized.\\n\\nStarring docs is a great way to mark docs of personal importance. After you star a doc, it will live in a section on your doc list called **[My Shortcuts](https://coda.io/shortcuts)**. All starred docs, even from multiple different workspaces, will live in this section.\\n\\nStarring docs only saves them to your personal My Shortcuts. It doesn’t affect the view for others in your workspace. If you’re wanting to shortcut docs not just for yourself but also for others in your team or workspace, you’ll [use pinning](https://help.coda.io/en/articles/2865511-starred-pinned-docs) instead.\"}\n", "\n", "Section 2:\n", "{'doc_id': '8179780', 'section_id': 1, 'markdown': '**Star your docs**\\n==================\\n\\nTo star a doc, hover over its name in the doc list and click the star icon. Alternatively, you can star a doc from within the doc itself. Hover over the doc title in the upper left corner, and click on the star.\\n\\nOnce you star a doc, you can access it quickly from the [My Shortcuts](https://coda.io/shortcuts) tab of your doc list.\\n\\n![](https://downloads.intercomcdn.com/i/o/793964361/55a80927217f85d68d44a3c3/Star+doc+to+my+shortcuts.gif)\\n\\nAnd, as your doc needs change, simply click the star again to un-star the doc and remove it from **My Shortcuts**.'}\n", "\n", "Section 3:\n", "{'doc_id': '8179780', 'section_id': 2, 'markdown': '**FAQs**\\n========\\n\\nWhen should I star a doc and when should I pin it?\\n--------------------------------------------------\\n\\nStarring docs is best for docs of *personal* importance. Starred docs appear in your **My Shortcuts**, but they aren’t starred for anyone else in your workspace. For instance, you may want to star your personal to-do list doc or any docs you use on a daily basis.\\n\\n[Pinning](https://help.coda.io/en/articles/2865511-starred-pinned-docs) is recommended when you want to flag or shortcut a doc for *everyone* in your workspace or folder. For instance, you likely want to pin your company wiki doc to your workspace. And you may want to pin your team task tracker doc to your team’s folder.\\n\\nCan I star docs for everyone?\\n-----------------------------\\n\\nStarring docs only applies to your own view and your own My Shortcuts. To pin docs (or templates) to your workspace or folder, [refer to this article](https://help.coda.io/en/articles/2865511-starred-pinned-docs).\\n\\n---'}\n"]}], "source": ["data = requests.get(\n", "    \"https://gist.githubusercontent.com/wong-codaio/b8ea0e087f800971ca5ec9eef617273e/raw/39f8bd2ebdecee485021e20f2c1d40fd649a4c77/articles.json\"\n", ").json()\n", "\n", "markdown_docs = [\n", "    {\"id\": row[\"id\"], \"markdown\": markdownify.markdownify(row[\"body\"])} for row in data\n", "]\n", "\n", "i = 0\n", "markdown_sections = []\n", "for markdown_doc in markdown_docs:\n", "    sections = re.split(r\"(.*\\n=+\\n)\", markdown_doc[\"markdown\"])\n", "    current_section = \"\"\n", "    for section in sections:\n", "        if not section.strip():\n", "            continue\n", "\n", "        if re.match(r\".*\\n=+\\n\", section):\n", "            current_section = section\n", "        else:\n", "            section = current_section + section\n", "            markdown_sections.append(\n", "                {\n", "                    \"doc_id\": markdown_doc[\"id\"],\n", "                    \"section_id\": i,\n", "                    \"markdown\": section.strip(),\n", "                }\n", "            )\n", "            current_section = \"\"\n", "            i += 1\n", "\n", "print(f\"Downloaded {len(markdown_sections)} Markdown sections. Here are the first 3:\")\n", "for i, section in enumerate(markdown_sections[:3]):\n", "    print(f\"\\nSection {i+1}:\\n{section}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use the Braintrust AI Proxy\n", "\n", "Let's initialize the OpenAI client using the [Braintrust proxy](/docs/guides/proxy). The Braintrust AI Proxy provides a single API to access OpenAI and other models. Because the proxy automatically caches and reuses results (when `temperature=0` or the `seed` parameter is set), we can re-evaluate prompts many times without incurring additional API costs."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["client = braintrust.wrap_openai(\n", "    openai.AsyncOpenAI(\n", "        api_key=os.environ.get(\"BRAINTRUST_API_KEY\"),\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "        default_headers={\"x-bt-use-cache\": \"always\"},\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate question-answer pairs\n", "\n", "Before we start evaluating some prompts, let's use the LLM to generate a bunch of question-answer pairs from the text at hand. We'll use these QA pairs as ground truth when grading our models later."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 320 QA pairs. Here are the first 10:\n", "{'input': 'What is the purpose of starring a doc in Coda?', 'expected': 'Starring a doc in Coda helps you mark documents of personal importance, making it easier to organize and access them quickly.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 0, 'answer_idx': 0, 'id': 0, 'split': 'train'}}\n", "{'input': 'Why would someone want to star a document in Coda?', 'expected': 'Starring a doc in Coda helps you mark documents of personal importance, making it easier to organize and access them quickly.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 0, 'answer_idx': 1, 'id': 1, 'split': 'test'}}\n", "{'input': 'Where do starred docs appear in Coda?', 'expected': 'Starred docs appear in a section called My Shortcuts on your doc list, allowing for quick access.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 1, 'answer_idx': 0, 'id': 2, 'split': 'train'}}\n", "{'input': 'After starring a document in Coda, where can I find it?', 'expected': 'Starred docs appear in a section called My Shortcuts on your doc list, allowing for quick access.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 1, 'answer_idx': 1, 'id': 3, 'split': 'test'}}\n", "{'input': 'Does starring a doc affect other users in the workspace?', 'expected': 'No, starring a doc only saves it to your personal My Shortcuts and does not affect the view for others in your workspace.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 2, 'answer_idx': 0, 'id': 4, 'split': 'train'}}\n", "{'input': 'Will my colleagues see the docs I star in Coda?', 'expected': 'No, starring a doc only saves it to your personal My Shortcuts and does not affect the view for others in your workspace.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 2, 'answer_idx': 1, 'id': 5, 'split': 'test'}}\n", "{'input': 'What should I use if I want to share a shortcut to a doc with my team?', 'expected': 'To create a shortcut for a document that your team can access, you should use the pinning feature instead of starring.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 3, 'answer_idx': 0, 'id': 6, 'split': 'train'}}\n", "{'input': 'How can I create a shortcut for a document that everyone in my workspace can access?', 'expected': 'To create a shortcut for a document that your team can access, you should use the pinning feature instead of starring.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 3, 'answer_idx': 1, 'id': 7, 'split': 'test'}}\n", "{'input': 'Can starred documents come from different workspaces in Coda?', 'expected': 'Yes, all starred docs, even from multiple different workspaces, will live in the My Shortcuts section.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 4, 'answer_idx': 0, 'id': 8, 'split': 'train'}}\n", "{'input': 'Is it possible to star docs from multiple workspaces?', 'expected': 'Yes, all starred docs, even from multiple different workspaces, will live in the My Shortcuts section.', 'metadata': {'document_id': '8179780', 'section_id': 0, 'question_idx': 4, 'answer_idx': 1, 'id': 9, 'split': 'test'}}\n"]}], "source": ["class QAPair(BaseModel):\n", "    questions: List[str] = Field(\n", "        ...,\n", "        description=\"List of questions, all with the same meaning but worded differently\",\n", "    )\n", "    answer: str = Field(..., description=\"Answer\")\n", "\n", "\n", "class QAPairs(BaseModel):\n", "    pairs: List[QAPair] = Field(..., description=\"List of question/answer pairs\")\n", "\n", "\n", "async def produce_candidate_questions(row):\n", "    response = await client.chat.completions.create(\n", "        model=QA_GEN_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"\"\"\\\n", "Please generate 8 question/answer pairs from the following text. For each question, suggest\n", "2 different ways of phrasing the question, and provide a unique answer.\n", " \n", "Content:\n", " \n", "{row['markdown']}\n", "\"\"\",\n", "            }\n", "        ],\n", "        functions=[\n", "            {\n", "                \"name\": \"propose_qa_pairs\",\n", "                \"description\": \"Propose some question/answer pairs for a given document\",\n", "                \"parameters\": QAPairs.model_json_schema(),\n", "            }\n", "        ],\n", "    )\n", "\n", "    pairs = QAPairs(**json.loads(response.choices[0].message.function_call.arguments))\n", "    return pairs.pairs\n", "\n", "\n", "# Create tasks for all API calls\n", "all_candidates_tasks = [\n", "    asyncio.create_task(produce_candidate_questions(a))\n", "    for a in markdown_sections[:NUM_SECTIONS]\n", "]\n", "\n", "\n", "all_candidates = [await f for f in all_candidates_tasks]\n", "\n", "data = []\n", "row_id = 0\n", "for row, doc_qa in zip(markdown_sections[:NUM_SECTIONS], all_candidates):\n", "    for i, qa in enumerate(doc_qa):\n", "        for j, q in enumerate(qa.questions):\n", "            data.append(\n", "                {\n", "                    \"input\": q,\n", "                    \"expected\": qa.answer,\n", "                    \"metadata\": {\n", "                        \"document_id\": row[\"doc_id\"],\n", "                        \"section_id\": row[\"section_id\"],\n", "                        \"question_idx\": i,\n", "                        \"answer_idx\": j,\n", "                        \"id\": row_id,\n", "                        \"split\": (\n", "                            \"test\" if j == len(qa.questions) - 1 and j > 0 else \"train\"\n", "                        ),\n", "                    },\n", "                }\n", "            )\n", "            row_id += 1\n", "\n", "print(f\"Generated {len(data)} QA pairs. Here are the first 10:\")\n", "for x in data[:10]:\n", "    print(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate a context-free prompt (no RAG)\n", "\n", "Let's evaluate a simple prompt that poses each question without providing context from the Markdown docs. We'll evaluate this naive approach using the [Factuality prompt](https://github.com/braintrustdata/autoevals/blob/main/templates/factuality.yaml) from the Braintrust [autoevals](/docs/reference/autoevals) library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def simple_qa(input):\n", "    completion = await client.chat.completions.create(\n", "        model=QA_ANSWER_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"\"\"\\\n", "Please answer the following question:\n", " \n", "Question: {input}\n", "\"\"\",\n", "            }\n", "        ],\n", "    )\n", "    return completion.choices[0].message.content\n", "\n", "\n", "await braintrust.<PERSON>(\n", "    name=\"Coda Help Desk Cookbook\",\n", "    experiment_name=\"No RAG\",\n", "    data=data[:NUM_QA_PAIRS],\n", "    task=simple_qa,\n", "    scores=[autoevals.Factuality(model=QA_GRADING_MODEL)],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze the evaluation in the UI\n", "\n", "The cell above will print a link to a Braintrust experiment. Pause and navigate to the UI to view our baseline eval.\n", "\n", "![Baseline eval](./assets/inspect.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Try using RAG to improve performance\n", "\n", "Let's see if RAG (retrieval-augmented generation) can improve our results on this task.\n", "\n", "First, we'll compute embeddings for each Markdown section using `text-embedding-ada-002` and create an index over the embeddings in [LanceDB](https://lancedb.com), a vector database. Then, for any given query, we can convert it to an embedding and efficiently find the most relevant context by searching in embedding space. We'll then provide the corresponding text as additional context in our prompt.\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["tempdir = tempfile.TemporaryDirectory()\n", "LANCE_DB_PATH = os.path.join(tempdir.name, \"docs-lancedb\")\n", "\n", "\n", "@braintrust.traced\n", "async def embed_text(text):\n", "    params = dict(input=text, model=\"text-embedding-ada-002\")\n", "    response = await client.embeddings.create(**params)\n", "    embedding = response.data[0].embedding\n", "\n", "    braintrust.current_span().log(\n", "        metrics={\n", "            \"tokens\": response.usage.total_tokens,\n", "            \"prompt_tokens\": response.usage.prompt_tokens,\n", "        },\n", "        metadata={\"model\": response.model},\n", "        input=text,\n", "        output=embedding,\n", "    )\n", "\n", "    return embedding\n", "\n", "\n", "embedding_tasks = [\n", "    asyncio.create_task(embed_text(row[\"markdown\"]))\n", "    for row in markdown_sections[:NUM_SECTIONS]\n", "]\n", "embeddings = [await f for f in embedding_tasks]\n", "\n", "db = lancedb.connect(LANCE_DB_PATH)\n", "\n", "try:\n", "    db.drop_table(\"sections\")\n", "except:\n", "    pass\n", "\n", "# Convert the data to a pandas DataFrame first\n", "import pandas as pd\n", "\n", "table_data = [\n", "    {\n", "        \"doc_id\": row[\"doc_id\"],\n", "        \"section_id\": row[\"section_id\"],\n", "        \"text\": row[\"markdown\"],\n", "        \"vector\": embedding,\n", "    }\n", "    for (row, embedding) in zip(markdown_sections[:NUM_SECTIONS], embeddings)\n", "]\n", "\n", "# Create table using the DataFrame approach\n", "table = db.create_table(\"sections\", data=pd.DataFrame(table_data))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use AI to judge relevance of retrieved documents\n", "\n", "Let's retrieve a few _more_ of the best-matching candidates from the vector database than we intend to use, then use the model from `RELEVANCE_MODEL` to score the relevance of each candidate to the input query. We'll use the `TOP_K` blurbs by relevance score in our QA prompt. Doing this should be a little more intelligent than just using the closest embeddings. "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["@braintrust.traced\n", "async def relevance_score(query, document):\n", "    response = await client.chat.completions.create(\n", "        model=RELEVANCE_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"\"\"\\\n", "Consider the following query and a document\n", " \n", "Query:\n", "{query}\n", " \n", "Document:\n", "{document}\n", " \n", " \n", "Please score the relevance of the document to a query, on a scale of 0 to 1.\n", "\"\"\",\n", "            }\n", "        ],\n", "        functions=[\n", "            {\n", "                \"name\": \"has_relevance\",\n", "                \"description\": \"Declare the relevance of a document to a query\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"score\": {\"type\": \"number\"},\n", "                    },\n", "                },\n", "            }\n", "        ],\n", "    )\n", "\n", "    arguments = response.choices[0].message.function_call.arguments\n", "    result = json.loads(arguments)\n", "\n", "    braintrust.current_span().log(\n", "        input={\"query\": query, \"document\": document},\n", "        output=result,\n", "    )\n", "\n", "    return result[\"score\"]\n", "\n", "\n", "async def retrieval_qa(input):\n", "    embedding = await embed_text(input)\n", "\n", "    with braintrust.current_span().start_span(\n", "        name=\"vector search\", input=input\n", "    ) as span:\n", "        result = table.search(embedding).limit(TOP_K + 3).to_arrow().to_pylist()\n", "        docs = [markdown_sections[i[\"section_id\"]][\"markdown\"] for i in result]\n", "\n", "        relevance_scores = []\n", "        for doc in docs:\n", "            relevance_scores.append(await relevance_score(input, doc))\n", "\n", "        span.log(\n", "            output=[\n", "                {\n", "                    \"doc\": markdown_sections[r[\"section_id\"]][\"markdown\"],\n", "                    \"distance\": r[\"_distance\"],\n", "                }\n", "                for r in result\n", "            ],\n", "            metadata={\"top_k\": TOP_K, \"retrieval\": result},\n", "            scores={\n", "                \"avg_relevance\": sum(relevance_scores) / len(relevance_scores),\n", "                \"min_relevance\": min(relevance_scores),\n", "                \"max_relevance\": max(relevance_scores),\n", "            },\n", "        )\n", "\n", "    context = \"\\n------\\n\".join(docs[:TOP_K])\n", "    completion = await client.chat.completions.create(\n", "        model=QA_ANSWER_MODEL,\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"\"\"\\\n", "Given the following context\n", " \n", "{context}\n", " \n", "Please answer the following question:\n", " \n", "Question: {input}\n", "\"\"\",\n", "            }\n", "        ],\n", "    )\n", "\n", "    return completion.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the RAG evaluation\n", "\n", "Now let's run our evaluation with RAG:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await braintrust.<PERSON>(\n", "    name=\"Coda Help Desk Cookbook\",\n", "    experiment_name=f\"RAG TopK={TOP_K}\",\n", "    data=data[:NUM_QA_PAIRS],\n", "    task=retrieval_qa,\n", "    scores=[autoevals.Factuality(model=QA_GRADING_MODEL)],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyzing the results\n", "\n", "![Experiment RAG](./assets/rag.png)\n", "\n", "Select the new experiment to analyze the results. You should notice several things:\n", "\n", "- Braintrust automatically compares the new experiment to your previous one\n", "- You should see an increase in scores with RAG\n", "- You can explore individual examples to see exactly which responses improved\n", "\n", "Try adjusting the constants set at the beginning of this tutorial, such as `NUM_QA_PAIRS`, to run your evaluation on a larger dataset and gain more confidence in your findings.\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "- Learn about [using functions to build a RAG agent](/docs/cookbook/recipes/ToolRAG).\n", "- Compare your [evals across different models](/docs/cookbook/recipes/ModelComparison).\n", "- If RAG is just one part of your agent, learn how to [evaluate a prompt chaining agent](docs/cookbook/recipes/PromptChaining).\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}