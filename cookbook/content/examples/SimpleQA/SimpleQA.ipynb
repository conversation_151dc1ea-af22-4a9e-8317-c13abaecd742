{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating SimpleQA\n", "\n", "We're going to evaluate a simple QA system in Braintrust using [SimpleQA](https://openai.com/index/introducing-simpleqa/), an open-source dataset from OpenAI. We'll also use [autoevals](https://github.com/braintrustdata/autoevals), our built-in library for evaluating AI model outputs. By the time you finish this example, you'll learn how to define and use custom evaluation metrics, compare evals that use different models, and analyze results in Braintrust."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "Before getting started, make sure you have a [Braintrust account](https://www.braintrust.dev/signup) and an API key for [OpenAI](https://platform.openai.com/). Make sure to plug the OpenAI key into your Braintrust account's [AI providers](https://www.braintrust.dev/app/settings?subroute=secrets) configuration and acquire a [BRAINTRUST_API_KEY](https://www.braintrust.dev/app/settings?subroute=api-keys). In this cookbook, we'll be comparing GPT-4o to Claude 3.5 Sonnet, so if you'd like to follow along, add an API key for [Anthropic](https://console.anthropic.com/) to your Braintrust account as well. Or, you can add an API key for any other AI provider you'd like and follow the same process. Lastly, add your `BRAINTRUST_API_KEY` to your Python environment, or just hardcode it into the code below.\n", "\n", "### Install dependencies\n", "\n", "Everything you need to run evals is readily available through Braintrust. We'll use the [AI proxy](https://www.braintrust.dev/docs/guides/proxy) to access multiple AI models without having to write model-specific code. Run the following command to install required libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install autoevals braintrust openai requests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparing the dataset\n", "\n", "We'll use a QA dataset available online. If the dataset URL isn't accessible, feel free to replace it with a local CSV file.\n", "\n", "First, we'll load in the dataset and print a confirmation statement to confirm we're ready for the next step. "]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 4326 rows from the dataset.\n"]}], "source": ["import csv\n", "import requests\n", "\n", "csv_data = []\n", "response = requests.get(\n", "    \"https://openaipublic.blob.core.windows.net/simple-evals/simple_qa_test_set.csv\"\n", ")\n", "reader = csv.<PERSON><PERSON><PERSON><PERSON><PERSON>(response.text.splitlines())\n", "csv_data = list(reader)\n", "print(f\"Loaded {len(csv_data)} rows from the dataset.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Parse and transform the dataset\n", "\n", "Next, we'll parse the raw CSV data into a Python list of dictionaries, ensuring that any metadata stored as strings is converted into usable Python objects. This transformation prepares the dataset for evaluation tasks. We'll print a few data points here as well to confirm everything looks as expected. "]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'metadata': {'topic': 'Science and technology',\n", "   'answer_type': 'Person',\n", "   'urls': ['https://en.wikipedia.org/wiki/IEEE_<PERSON>_<PERSON>_Award',\n", "    'https://ieeexplore.ieee.org/author/37271220500',\n", "    'https://en.wikipedia.org/wiki/IEEE_Frank_<PERSON>_Award',\n", "    'https://www.nxtbook.com/nxtbooks/ieee/awards_2010/index.php?startid=21#/p/20']},\n", "  'problem': 'Who received the IEEE Frank Rosenblatt Award in 2010?',\n", "  'answer': '<PERSON><PERSON><PERSON>'},\n", " {'metadata': {'topic': 'Science and technology',\n", "   'answer_type': 'Person',\n", "   'urls': ['https://en.wikipedia.org/wiki/The_Oceanography_Society',\n", "    'https://en.wikipedia.org/wiki/The_Oceanography_Society',\n", "    'https://tos.org/jer<PERSON>-medal',\n", "    'https://www.eurekalert.org/news-releases/490504']},\n", "  'problem': \"Who was awarded the Oceanography Society's Jerlov Award in 2018?\",\n", "  'answer': '<PERSON><PERSON>'},\n", " {'metadata': {'topic': 'Geography',\n", "   'answer_type': 'Place',\n", "   'urls': ['https://en.wikipedia.org/wiki/Radcliffe_College',\n", "    'https://en.wikipedia.org/wiki/Radcliffe_College',\n", "    'https://www.braingainmag.com/7-historic-liberal-arts-colleges-in-the-us.htm',\n", "    'https://thepeoplesarchive.dclibrary.org/repositories/2/resources/2228']},\n", "  'problem': \"What's the name of the women's liberal arts college in Cambridge, Massachusetts?\",\n", "  'answer': 'Radcliffe College'}]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_data = []\n", "for row in csv_data:\n", "    parsed_data.append(\n", "        {\n", "            **row,\n", "            \"metadata\": eval(row[\"metadata\"]),  # Single quoted python values\n", "        }\n", "    )\n", "\n", "parsed_data[:3]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Format the data\n", "\n", "Lastly, we need to format the data for Braintrust. To do this, we'll write a generator function that structures each row as a task with `input`, `expected`, and `metadata` fields.\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Let's format the data for braintrust (input, expected, metadata)\n", "def dataset():\n", "    # Feel free to use more of (or the entire) dataset\n", "    for row in parsed_data[:10]:\n", "        yield {\n", "            \"input\": row[\"problem\"],\n", "            \"expected\": row[\"answer\"],\n", "            \"metadata\": row[\"metadata\"],\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the model task\n", "\n", "Now that our data is ready, we'll generate responses to the QA tasks using an LLM call. You'll notice that in this step, we use the Braintrust proxy to access GPT-4o. You can substitute any model here by setting the `MODEL` variable, as long as you have the API key for that provider configured in your Braintrust organization. \n", "\n", "Here is the task definition:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from braintrust import wrap_openai\n", "from openai import OpenAI\n", "import os\n", "\n", "BRAINTRUST_API_KEY = os.environ.get(\n", "    \"BRAINTRUST_API_KEY\"\n", " )  # Or hardcode this to your API key\n", "\n", "# Use the Braintrust proxy\n", "client = OpenAI(\n", "    base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    api_key=BRAINTRUST_API_KEY,\n", ")\n", "\n", "# The task just uses the \"user\" message\n", "MODEL = \"gpt-4o\"\n", "\n", "\n", "def task(input):\n", "    return (\n", "        client.chat.completions.create(\n", "            model=MODEL,\n", "            messages=[{\"role\": \"user\", \"content\": input}],\n", "        )\n", "        .choices[0]\n", "        .message.content\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create a scoring function\n", "\n", "To assess the performance of our QA system, we'll define a custom LLM-as-a-judge scoring function using the LLMClassifier from `autoevals` as a starting point. This grader will classify responses as `CORRECT`, `INCORRECT`, or `NOT_ATTEMPTED` based on predefined rules.\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from autoevals import LLMClassifier\n", "\n", "grader = LLMClassifier(\n", "    name=\"Grader\",\n", "    prompt_template=\"\"\"\\\n", "Your job is to look at a question, a gold target, and a predicted answer, and then assign a grade of either [\"CORRECT\", \"INCORRECT\", \"NOT_ATTEMPTED\"].\n", "First, I will give examples of each grade, and then you will grade a new example.\n", "\n", "\n", "The following are examples of CORRECT predicted answers.\n", "'''\n", "Question: What are the names of <PERSON>'s children?\n", "Gold target: <PERSON><PERSON> and <PERSON>\n", "Predicted answer 1: sasha and malia obama\n", "Predicted answer 2: most people would say <PERSON><PERSON> and <PERSON>, but I'm not sure and would have to double check\n", "Predicted answer 3: <PERSON> has two daughters. Their names are <PERSON><PERSON> and <PERSON>, but they are commonly referred to as <PERSON><PERSON> and <PERSON>. <PERSON><PERSON> was born on July 4, 1998, and <PERSON> was born on June 10, 2001.\n", "'''\n", "These predicted answers are all CORRECT because:\n", "    - They fully contain the important information in the gold target.\n", "    - They do not contain any information that contradicts the gold target.\n", "    - Only semantic meaning matters; capitalization, punctuation, grammar, and order don't matter.\n", "    - Hedging and guessing are permissible, provided that the gold target is fully included and the response contains no incorrect information or contradictions.\n", "\n", "\n", "The following are examples of INCORRECT predicted answers.\n", "'''\n", "Question: What are the names of <PERSON>'s children?\n", "Gold target: <PERSON><PERSON> and <PERSON>\n", "Predicted answer 1: <PERSON><PERSON>.\n", "Predicted answer 2: <PERSON><PERSON>, <PERSON>, and <PERSON>.\n", "Predicted answer 3: <PERSON> does not have any children.\n", "Predicted answer 4: I think it's either <PERSON><PERSON> and <PERSON>. Or it could be <PERSON><PERSON> and <PERSON>. Or it could be <PERSON> and <PERSON><PERSON>.\n", "Predicted answer 4: While I don't know their exact names, I can tell you that <PERSON> has three children.\n", "Predicted answer 5: It's possible you may mean <PERSON> and <PERSON>. However, you should clarify further details with updated references if necessary. Is that the correct answer?\n", "Predicted answer 6: It may be the case that <PERSON>'s child is named <PERSON>. However, it's recommended to confirm the most accurate and updated information since this could change over time. This model may not always reflect the most current information.\n", "'''\n", "These predicted answers are all INCORRECT because:\n", "    - A factual statement in the answer contradicts the gold target. Incorrect statements that have some hedging (e.g., \"it is possible that\", \"although i'm not sure, i think\") are also considered incorrect.\n", "\n", "\n", "The following are examples of NOT_ATTEMPTED predicted answers.\n", "'''\n", "Question: What are the names of <PERSON>'s children?\n", "Gold target: <PERSON><PERSON> and <PERSON>\n", "Predicted answer 1: I don't know.\n", "Predicted answer 2: I need more context about which Obama you are talking about.\n", "Predicted answer 3: Without researching the web, I cannot answer this question. However, I can tell you that <PERSON> has two children.\n", "Predicted answer 4: <PERSON> has two children. I know that one of them is <PERSON><PERSON>, but I'm not sure about the other one.\n", "'''\n", "These predicted answers are all NOT_ATTEMPTED because:\n", "    - The important information in the gold target is not included in the answer.\n", "    - No statements in the answer contradict the gold target.\n", "\n", "\n", "Also note the following things:\n", "- For grading questions where the gold target is a number, the predicted answer needs to be correct to the last significant figure in the gold answer. For example, consider a question \"How many citations does the Transformer Paper have?\" with gold target \"120k\". \n", "    - Predicted answers \"120k\", \"124k\", and 115k\" are all CORRECT. \n", "    - Predicted answers \"100k\" and \"113k\" are INCORRECT. \n", "    - Predicted answers \"around 100k\" and \"more than 50k\" are considered NOT_ATTEMPTED because they neither confirm nor contradict the gold target.\n", "- The gold target may contain more information than the question. In such cases, the predicted answer only needs to contain the information that is in the question.\n", "    - For example, consider the question \"What episode did <PERSON> and <PERSON> get legally married in Grey's Anatomy?\" with gold target \"Season 7, Episode 20: White Wedding\". Either \"Season 7, Episode 20\" or \"White Wedding\" would be considered a CORRECT answer.\n", "- Do not punish predicted answers if they omit information that would be clearly inferred from the question.\n", "    - For example, consider the question \"What city is OpenAI headquartered in?\" and the gold target \"San Francisco, California\". The predicted answer \"San Francisco\" would be considered CORRECT, even though it does not include \"California\".\n", "    - Consider the question \"What award did A pretrainer's guide to training data: Measuring the effects of data age, domain coverage, quality, & toxicity win at NAACL '24?\", the gold target is \"Outstanding Paper Award\". The predicted answer \"Outstanding Paper\" would be considered CORRECT, because \"award\" is presumed in the question.\n", "    - For the question \"What is the height of <PERSON> in meters?\", the gold target is \"1.73 m\". The predicted answer \"1.75\" would be considered CORRECT, because meters is specified in the question.\n", "    - For the question \"What is the name of <PERSON>'s wife?\", the gold target is \"<PERSON>\". The predicted answer \"<PERSON>\" would be considered CORRECT, because the last name can be presumed.\n", "- Do not punish for typos in people's name if it's clearly the same name. \n", "    - For example, if the gold target is \"Hyung Won Chung\", you can consider the following predicted answers as correct: \"<PERSON>yoong Won Choong\", \"<PERSON>yung<PERSON> Chung\", or \"Hyun Won Chung\".\n", "\n", "\n", "Here is a new example. Simply reply with either CORRECT, INCORRECT, NOT ATTEMPTED. Don't apologize or correct yourself if there was a mistake; we are just trying to grade the answer.\n", "'''\n", "Question: {{input}}\n", "Gold target: {{expected}}\n", "Predicted answer: {{output}}\n", "'''\n", "\n", "Grade the predicted answer of this new question as one of:\n", "A: CORRECT\n", "B: INCORRECT\n", "C: NOT_ATTEMPTED\n", "\n", "Just return the letters \"A\", \"B\", or \"C\", with no text around it.\"\"\",\n", "    choice_scores={\"A\": 1, \"B\": 0, \"C\": 0.5},\n", "    use_cot=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the evaluation\n", "\n", "With the dataset, scoring function, and task defined, we're ready to run our eval:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from braintrust import EvalAsync\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "results = await <PERSON><PERSON><PERSON>(\n", "    \"SimpleQA\",\n", "    data=dataset,\n", "    task=task,\n", "    scores=[grader],\n", "    metadata={\n", "        \"model\": MODEL,\n", "    },\n", ")\n", "print(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze results\n", "\n", "Braintrust will print a summary of your eval, but to analyze the full results, you'll need to visit the Braintrust dashboard by opening the printed link, or navigating to Braintrust, selecting the **SimpleQA** project, and navigating to the **Evaluations** tab. \n", "\n", "![Eval in UI](assets/eval.png)\n", "\n", "If you look at the score distribution chart, you’ll notice that the Grader either gave a score of 100% or 0%, averaging out to 50% across the 10 datapoints. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing models\n", "\n", "Let's swap out the model and see if we get different results. Set the `MODEL` variable to `claude-3-5-sonnet-latest` and rerun the evaluation cell above. Now when you go to Braintrust, you can directly compare the results of the experiments. \n", "\n", "![Eval comparison](assets/eval-comparison.png)\n", "\n", "While the new model scored better on some of the datapoints, it regressed on others."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps \n", "\n", "From here, there are a few different things you could do to improve the score of your QA system. You could:\n", "* Switch out the model again and see if you get different results \n", "* Dig into the traces in Braintrust and examine if the scoring function is working as intended\n", "* Edit the scoring function\n", "* Run the experiment on a larger dataset\n", "\n", "The way we’ve set up the experiment here makes it easy to switch out the LLM and compare results across models, examine your evaluation more thoroughly in the UI, and add more data points to your evaluation dataset. Give it a try!"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}