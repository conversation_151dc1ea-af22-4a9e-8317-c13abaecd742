{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comparing AI models with Braintrust\n", "\n", "This tutorial will teach you how to use Braintrust to compare the same prompts across different AI models and parameters to help decide on choosing a model to run your AI apps.\n", "\n", "Before starting, please make sure that you have a Braintrust account. If you do not, please [sign up](https://www.braintrustdata.com). After this tutorial, feel free to dig deeper by visiting [the docs](http://www.braintrustdata.com/docs).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installing dependencies\n", "\n", "To see a list of dependencies, you can view the accompanying [package.json](https://github.com/braintrustdata/braintrust-cookbook/tree/main/examples/ModelComparison/package.json) file. Feel free to copy/paste snippets of this code to run in your environment, or use [tslab](https://github.com/yunabe/tslab) to run the tutorial in a Jupyter notebook.\n", "\n", "## Setting up the data\n", "\n", "For this example, we will use a small subset of data taken from the [google/boolq](https://huggingface.co/datasets/google/boolq) dataset. If you'd like, you can try datasets and prompts from any of the other [cookbooks](https://www.braintrustdata.com/docs/cookbook/) at Braintrust.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    input: \u001b[32m'do you have to have two license plates in ontario'\u001b[39m,\n", "    expected: \u001b[32m'true'\u001b[39m\n", "  },\n", "  {\n", "    input: \u001b[32m'are black beans the same as turtle beans'\u001b[39m,\n", "    expected: \u001b[32m'true'\u001b[39m\n", "  },\n", "  {\n", "    input: \u001b[32m'is a wooly mammoth the same as a mastodon'\u001b[39m,\n", "    expected: \u001b[32m'false'\u001b[39m\n", "  },\n", "  {\n", "    input: \u001b[32m'is carling black label a south african beer'\u001b[39m,\n", "    expected: \u001b[32m'false'\u001b[39m\n", "  },\n", "  {\n", "    input: \u001b[32m'were the world trade centers the tallest buildings in america'\u001b[39m,\n", "    expected: \u001b[32m'true'\u001b[39m\n", "  }\n", "]\n"]}], "source": ["// curl -X GET \"https://datasets-server.huggingface.co/rows?dataset=google%2Fboolq&config=default&split=train&offset=500&length=5\" > ./assets/dataset.json\n", "import dataset from \"./assets/dataset.json\";\n", "\n", "// labels these 1-3 so that they will be easier to recognize in the app\n", "const prompts = [\n", "  \"(1) - true or false\",\n", "  \"(2) - Answer using true or false only\",\n", "  \"(3) - Answer the following question as accurately as possible with the words 'true' or 'false' in lowercase only. Do not include any other words in the response\",\n", "];\n", "\n", "// extract question/answers from rows into input/expected\n", "const evalData = dataset.rows.map(({ row: { question, answer } }) => ({\n", "  input: question,\n", "  expected: `${answer}`,\n", "}));\n", "console.log(evalData);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running comparison evals across multiple models\n", "\n", "Let's set up some code to compare these prompts and inputs across 3 different models and different temperature values. For this cookbook we will be using [Braintrust's LLM proxy](https://www.braintrustdata.com/docs/guides/proxy) to access the API for different models.\n", "\n", "All we need to do is provide a `baseURL` to the proxy with the relevant API key that we want to access, and the use the `wrapOpenAI` function from braintrust which will help us capture helpful debugging information about each model's performance while keeping the same SDK interface across all models.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import { wrapOpenAI } from \"braintrust\";\n", "import { OpenAI } from \"openai\";\n", "\n", "async function callModel(\n", "  input: string,\n", "  {\n", "    model,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    temperature,\n", "    systemPrompt,\n", "  }: {\n", "    model: string;\n", "    api<PERSON>ey: string;\n", "    temperature: number;\n", "    systemPrompt: string;\n", "  }\n", ") {\n", "  const client = wrapOpenAI(\n", "    new OpenAI({\n", "      baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "      api<PERSON><PERSON>, // Can use OpenAI, Anthropic, Mistral etc. API keys here\n", "    })\n", "  );\n", "\n", "  const response = await client.chat.completions.create({\n", "    model: model,\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content: systemPrompt,\n", "      },\n", "      {\n", "        role: \"user\",\n", "        content: input,\n", "      },\n", "    ],\n", "    temperature,\n", "    seed: 123,\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then we will set up our eval data for each combination of model, prompt and temperature.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    model: \u001b[32m'claude-3-opus-20240229'\u001b[39m,\n", "    temperature: \u001b[33m0\u001b[39m,\n", "    prompt: \u001b[32m'(1) - true or false'\u001b[39m\n", "  },\n", "  {\n", "    model: \u001b[32m'claude-3-opus-20240229'\u001b[39m,\n", "    temperature: \u001b[33m0\u001b[39m,\n", "    prompt: \u001b[32m'(2) - Answer using true or false only'\u001b[39m\n", "  },\n", "  {\n", "    model: \u001b[32m'claude-3-opus-20240229'\u001b[39m,\n", "    temperature: \u001b[33m0\u001b[39m,\n", "    prompt: \u001b[32m\"(3) - Answer the following question as accurately as possible with the words 'true' or 'false' in lowercase only. Do not include any other words in the response\"\u001b[39m\n", "  },\n", "  {\n", "    model: \u001b[32m'claude-3-opus-20240229'\u001b[39m,\n", "    temperature: \u001b[33m0.25\u001b[39m,\n", "    prompt: \u001b[32m'(1) - true or false'\u001b[39m\n", "  },\n", "  {\n", "    model: \u001b[32m'claude-3-opus-20240229'\u001b[39m,\n", "    temperature: \u001b[33m0.25\u001b[39m,\n", "    prompt: \u001b[32m'(2) - Answer using true or false only'\u001b[39m\n", "  }\n", "]\n"]}], "source": ["const combinations: {\n", "  model: { name: string; apiKey: string };\n", "  temperature: number;\n", "  prompt: string;\n", "}[] = [];\n", "for (const model of [\n", "  {\n", "    name: \"claude-3-opus-20240229\",\n", "    api<PERSON>ey: process.env.ANTHROPIC_API_KEY ?? \"\",\n", "  },\n", "  {\n", "    name: \"claude-3-haiku-20240307\",\n", "    api<PERSON>ey: process.env.ANTHROPIC_API_KEY ?? \"\",\n", "  },\n", "  { name: \"gpt-4\", apiKey: process.env.OPENAI_API_KEY ?? \"\" },\n", "  { name: \"gpt-4o\", apiKey: process.env.OPENAI_API_KEY ?? \"\" },\n", "]) {\n", "  for (const temperature of [0, 0.25, 0.5, 0.75, 1]) {\n", "    for (const prompt of prompts) {\n", "      combinations.push({\n", "        model,\n", "        temperature,\n", "        prompt,\n", "      });\n", "    }\n", "  }\n", "}\n", "\n", "[process.env.ANTHROPIC_API_KEY, process.env.OPENAI_API_KEY].forEach(\n", "  (v, i) => !v && console.warn(i, \"API key not set\")\n", ");\n", "// don't log API keys\n", "console.log(\n", "  combinations.slice(0, 5).map(({ model: { name }, temperature, prompt }) => ({\n", "    model: name,\n", "    temperature,\n", "    prompt,\n", "  }))\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use the functions and data that we have set up to run some evals on Braintrust! We will be using two scorers for this eval:\n", "\n", "1. A simple exact match scorer that will compare the output from the LLM exactly with the expected value\n", "2. A Levenshtein scorer which will calculate the Levenshtein distance between the LLM output and our expected value\n", "\n", "We are also adding the model, temperature, and prompt into the metadata so that we can use those fields to help our visualization inside the braintrust app after the evals are finished running.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "import { <PERSON><PERSON><PERSON><PERSON> } from \"autoevals\";\n", "\n", "const exactMatch = (args: { input; output; expected? }) => {\n", "  return {\n", "    name: \"ExactMatch\",\n", "    score: args.output === args.expected ? 1 : 0,\n", "  };\n", "};\n", "\n", "await Promise.all(\n", "  combinations.map(async ({ model, temperature, prompt }) => {\n", "    Eval(\"Model comparison\", {\n", "      data: () =>\n", "        evalData.map(({ input, expected }) => ({\n", "          input,\n", "          expected,\n", "        })),\n", "      task: async (input) => {\n", "        return await callModel(input, {\n", "          model: model.name,\n", "          api<PERSON><PERSON>: model.api<PERSON><PERSON>,\n", "          temperature,\n", "          systemPrompt: prompt,\n", "        });\n", "      },\n", "      scores: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>],\n", "      metadata: {\n", "        model: model.name,\n", "        temperature,\n", "        prompt,\n", "      },\n", "    });\n", "  })\n", ");\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" ████████████████████████████████████████ | Model comparison                         | 100% | 5/5 datapoints\n", "\n", "=========================SUMMARY=========================\n", "main-1716504446-539a4a27 compared to main-1716504446-c81946d8:\n", "52.00% ''Levenshtein'' score    (0 improvements, 0 regressions)\n", "40.00% ''ExactMatch' ' score    (0 improvements, 0 regressions)\n", "\n", "5.06s 'duration'        (0 improvements, 0 regressions)\n", "\n", "See results for main-1716504446-539a4a27 at https://www.braintrust.dev/app/braintrustdata.com/p/Model%20comparison/experiments/main-1716504446-539a4a27\n", "\n", "\n", "=========================SUMMARY=========================\n", "main-1716504446-44ef0250 compared to main-1716504446-75fa02ea:\n", "0.00% ''ExactMatch' ' score     (0 improvements, 0 regressions)\n", "1.43% ''Levenshtein'' score     (0 improvements, 0 regressions)\n", "\n", "1.05s 'duration'        (0 improvements, 0 regressions)\n", "\n", "See results for main-1716504446-44ef0250 at https://www.braintrust.dev/app/braintrustdata.com/p/Model%20comparison/experiments/main-1716504446-44ef0250"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing\n", "\n", "Now we have successfully run our evals! Let's log onto [braintrust.dev](https://braintrust.dev) and take a look at the results.\n", "\n", "Click into the newly generated project called `Model comparison`, and check it out! You should notice a few things:\n", "\n", "![initial-chart](assets/initial-chart.png)\n", "\n", "- Each line represents a score over time, and each data point represents an experiment that was run.\n", "  - From the code, we ran 60 experiments (5 temperature values x 4 models x 3 prompts) so one line should consist of 60 dots, each with a different combination of temperature, model, and prompt.\n", "- Metadata fields are automatically populated as viable X axis values.\n", "- Metadata fields with numeric values are automatically populated as viable Y axis values.\n", "\n", "![initial-chart-temperature](assets/initial-chart-x-axis.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Diving in\n", "\n", "This chart allows us to also group data to allow us to compare experiment runs by model, prompt, and temperature.\n", "\n", "By selecting `X Axis prompt`, we can see pretty clearly that the longer prompt performed better than the shorter ones.\n", "\n", "![grouped-chart](assets/group-by-prompt.png)\n", "\n", "By selecting the `one color per model` and `X Axis model`, we can also visualize performance between different models. From this view we can see that the OpenAI models outperformed the Anthropic models.\n", "\n", "![grouped-chart](assets/group-by-model.png)\n", "\n", "Let's see if we can find any differences between the OpenAI models by selecting the `one color per model`, `one symbol per prompt`, and `X Axis temperature`.\n", "\n", "![grouped-chart](assets/grouped-chart.png)\n", "\n", "In this view, we can see that `gpt-4` performed better than `gpt-4o` at higher temperatures!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parting thoughts\n", "\n", "This is just the start of evaluating and improving your AI applications. From here, you should run more experiments with larger datasets, and also try out different prompts! Once you have run another set of experiments, come back to the chart and play with the different views and groupings. You can also add filtering to filter for experiments with specific scores and metadata to find even more insights.\n", "\n", "Happy evaluating!\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}