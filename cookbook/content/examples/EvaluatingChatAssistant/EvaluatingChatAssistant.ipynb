{"cells": [{"cell_type": "markdown", "id": "07c34ffc-a3a1-4915-b866-e32d97a98e7c", "metadata": {}, "source": ["## Evaluating a multi-turn chat assistant\n", "\n", "This tutorial will walk through using Braintrust to evaluate a conversational, multi-turn chat assistant.\n", "\n", "These types of chat bots have become important parts of applications, acting as customer service agents, sales representatives, or travel agents, to name a few. As an owner of such an application, it's important to be sure the bot provides value to the user.\n", "\n", "We will expand on this below, but the history and context of a conversation is crucial in being able to produce a good response. If you received a request to \"Make a dinner reservation at 7pm\" and you knew where, on what date, and for how many people, you could provide some assistance; otherwise, you'd need to ask for more information.\n", "\n", "Before starting, please make sure you have a Braintrust account. If you do not have one, you can [sign up here](https://www.braintrust.dev)."]}, {"cell_type": "markdown", "id": "58884f34-1fd4-4aeb-9d71-6bfc9a1b6925", "metadata": {}, "source": ["## Installing dependencies\n", "\n", "Begin by installing the necessary dependencies if you have not done so already."]}, {"cell_type": "code", "execution_count": null, "id": "e6f9be51-7191-4204-87be-1906df124ebd", "metadata": {}, "outputs": [], "source": ["pnpm install autoevals braintrust openai"]}, {"cell_type": "markdown", "id": "78277a53-7763-41cc-aa51-89354e729875", "metadata": {}, "source": ["## Inspecting the data\n", "\n", "Let's take a look at the small dataset prepared for this cookbook. You can find the full dataset in the accompanying [dataset.ts file](https://github.com/braintrustdata/braintrust-cookbook/tree/main/examples/EvaluatingChatAssistant/dataset.ts). The `assistant` turns were generated using `claude-3-5-sonnet-********`.\n", "\n", "Below is an example of a data point.\n", "- `chat_history` contains the history of the conversation between the user and the assistant\n", "- `input` is the last `user` turn that will be sent in the `messages` argument to the chat completion\n", "- `expected` is the output expected from the chat completion given the input"]}, {"cell_type": "code", "execution_count": 5, "id": "ef910f35-4b64-403b-b5fb-7c3a9ebde8c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  chat_history: [\n", "    {\n", "      role: \u001b[32m'user'\u001b[39m,\n", "      content: \u001b[32m\"when was the ballon d'or first awarded for female players?\"\u001b[39m\n", "    },\n", "    {\n", "      role: \u001b[32m'assistant'\u001b[39m,\n", "      content: \u001b[32m\"The Ballon d'Or for female players was first awarded in 2018. The inaugural winner was <PERSON>, a Norwegian striker who plays for Olympique Lyonnais.\"\u001b[39m\n", "    }\n", "  ],\n", "  input: \u001b[32m\"who won the men's trophy that year?\"\u001b[39m,\n", "  expected: \u001b[32m\"In 2018, the men's Ballon d'Or was awarded to <PERSON><PERSON>.\"\u001b[39m\n", "}\n"]}], "source": ["import dataset, { ChatTurn } from \"./assets/dataset\";\n", "\n", "console.log(dataset[0]);"]}, {"cell_type": "markdown", "id": "2eab9290-1b54-470a-967a-21f01f2de32c", "metadata": {}, "source": ["From looking at this one example, we can see why the history is necessary to provide a helpful response.\n", "\n", "If you were asked \"Who won the men's trophy that year?\" you would wonder *What trophy? Which year?* But if you were also given the `chat_history`, you would be able to answer the question (maybe after some quick research)."]}, {"cell_type": "markdown", "id": "77ae5ffe-c66d-4bbd-8b99-08a09af5900d", "metadata": {}, "source": ["## Running experiments\n", "\n", "The key to running evals on a multi-turn conversation is to include the history of the chat in the chat completion request.\n", "\n", "### Assistant with no chat history\n", "\n", "To start, let's see how the prompt performs when no chat history is provided. We'll create a simple task function that returns the output from a chat completion."]}, {"cell_type": "code", "execution_count": 6, "id": "9eeb7c5f-f502-4f7e-b958-981dea2b62e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  input: \u001b[32m\"who won the men's trophy that year?\"\u001b[39m,\n", "  expected: \u001b[32m\"In 2018, the men's Ballon d'Or was awarded to <PERSON><PERSON>.\"\u001b[39m\n", "}\n"]}], "source": ["import { wrapOpenAI } from \"braintrust\";\n", "import { OpenAI } from \"openai\";\n", "\n", "const experimentData = dataset.map((data) => ({\n", "  input: data.input,\n", "  expected: data.expected,\n", "}));\n", "console.log(experimentData[0]);\n", "\n", "async function runTask(input: string) {\n", "  const client = wrapOpenAI(\n", "    new OpenAI({\n", "      baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "      apiKey: process.env.OPENAI_API_KEY ?? \"\", // Can use OpenAI, Anthropic, Mistral, etc. API keys here\n", "    }),\n", "  );\n", "\n", "  const response = await client.chat.completions.create({\n", "    model: \"gpt-4o\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content:\n", "          \"You are a helpful and polite assistant who knows about sports.\",\n", "      },\n", "      {\n", "        role: \"user\",\n", "        content: input,\n", "      },\n", "    ],\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}"]}, {"cell_type": "markdown", "id": "53783a49-1c41-4668-92e3-142887534c00", "metadata": {}, "source": ["#### Scoring and running the eval\n", "\n", "We'll use the `Factuality` scoring function from the [autoevals library](https://www.braintrust.dev/docs/reference/autoevals) to check how the output of the chat completion compares factually to the expected value.\n", "\n", "We will also utilize [trials](https://www.braintrust.dev/docs/guides/evals/write#trials) by including the `trialCount` parameter in the `Eval` call. We expect the output of the chat completion to be non-deterministic, so running each input multiple times will give us a better sense of the \"average\" output."]}, {"cell_type": "code", "execution_count": null, "id": "a6842138-4cf1-4210-8dfa-777a8833c9d0", "metadata": {}, "outputs": [], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "import Factuality from \"autoevals\";\n", "\n", "<PERSON><PERSON>(\"Chat assistant\", {\n", "  experimentName: \"gpt-4o assistant - no history\",\n", "  data: () => experimentData,\n", "  task: runTask,\n", "  scores: [Factuality],\n", "  trialCount: 3,\n", "  metadata: {\n", "    model: \"gpt-4o\",\n", "    prompt: \"You are a helpful and polite assistant who knows about sports.\",\n", "  },\n", "});"]}, {"cell_type": "code", "execution_count": null, "id": "565596a5-37bc-427d-81af-4a8fe5c84310", "metadata": {}, "outputs": [], "source": ["Experiment gpt - 4o assistant - no history is running at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20no%20history\n", " ████████████████████████████████████████ | Chat assistant[experimentName = gpt - 4o... | 100 % | 15 / 15 datapoints\n", "\n", "\n", "=========================SUMMARY=========================\n", "61.33% 'Factuality' score       (0 improvements, 0 regressions)\n", "\n", "4.12s 'duration'        (0 improvements, 0 regressions)\n", "0.01$ 'estimated_cost'  (0 improvements, 0 regressions)\n", "\n", "See results for gpt-4o assistant - no history at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20no%20history"]}, {"cell_type": "markdown", "id": "4e9fd431-815e-4d46-aca2-38835b7e81ca", "metadata": {}, "source": ["61.33% Factuality score? Given what we discussed earlier about chat history being important in producing a good response, that's surprisingly high. Let's log onto [braintrust.dev](https://www.braintrust.dev) and take a look at how we got that score.\n", "\n", "#### Interpreting the results\n", "\n", "![no-history-trace](./assets/no-history-trace.png)\n", "\n", "If we look at the score distribution chart, we can see ten of the fifteen examples scored at least 60%, with over half even scoring 100%. If we look into one of the examples with 100% score, we see the output of the chat completion request is asking for more context as we would expect:\n", "\n", "`Could you please specify which athlete or player you're referring to? There are many professional athletes, and I'll need a bit more information to provide an accurate answer.`\n", "\n", "This aligns with our expectation, so let's now look at how the score was determined.\n", "\n", "![no-history-score](./assets/no-history-score.png)\n", "\n", "Click into the scoring trace, we see the chain of thought reasoning used to settle on the score. The model chose `(E) The answers differ, but these differences don't matter from the perspective of factuality.` which is *technically* correct, but we want to penalize the chat completion for not being able to produce a good response."]}, {"cell_type": "markdown", "id": "fb0e8c07-334a-4a64-9289-f1ef71112ab9", "metadata": {}, "source": ["#### Improve scoring with a custom scorer\n", "\n", "While Factuality is a good general purpose scorer, for our use case option (E) is not well aligned with our expectations. The best way to work around this is to customize the scoring function so that it produces a lower score for asking for more context or specificity."]}, {"cell_type": "code", "execution_count": 7, "id": "f0b7e4a1-7680-4d36-8c64-336763de09c6", "metadata": {}, "outputs": [], "source": ["import { LLMClassifierFromSpec, Score } from \"autoevals\";\n", "\n", "function Factual(args: {\n", "  input: string;\n", "  output: string;\n", "  expected: string;\n", "}): Score | Promise<Score> {\n", "  const factualityScorer = LLMClassifierFromSpec(\"Factuality\", {\n", "    prompt: `You are comparing a submitted answer to an expert answer on a given question. Here is the data:\n", "              [BEGIN DATA]\n", "              ************\n", "              [Question]: {{{input}}}\n", "              ************\n", "              [Expert]: {{{expected}}}\n", "              ************\n", "              [Submission]: {{{output}}}\n", "              ************\n", "              [END DATA]\n", "\n", "              Compare the factual content of the submitted answer with the expert answer. Ignore any differences in style, grammar, or punctuation.\n", "              The submitted answer may either be a subset or superset of the expert answer, or it may conflict with it. Determine which case applies. Answer the question by selecting one of the following options:\n", "              (A) The submitted answer is a subset of the expert answer and is fully consistent with it.\n", "              (B) The submitted answer is a superset of the expert answer and is fully consistent with it.\n", "              (C) The submitted answer contains all the same details as the expert answer.\n", "              (D) There is a disagreement between the submitted answer and the expert answer.\n", "              (E) The answers differ, but these differences don't matter from the perspective of factuality.\n", "              (F) The submitted answer asks for more context, specifics or clarification but provides factual information consistent with the expert answer.\n", "              (G) The submitted answer asks for more context, specifics or clarification but does not provide factual information consistent with the expert answer.`,\n", "    choice_scores: {\n", "      A: 0.4,\n", "      B: 0.6,\n", "      C: 1,\n", "      D: 0,\n", "      E: 1,\n", "      F: 0.2,\n", "      G: 0,\n", "    },\n", "  });\n", "  return factualityScorer(args);\n", "}"]}, {"cell_type": "markdown", "id": "13b60ee7-267c-4e75-9a1c-e0b9299f1c31", "metadata": {}, "source": ["You can see the built-in Factuality prompt [here](https://github.com/braintrustdata/autoevals/blob/main/templates/factuality.yaml). For our customized scorer, we've added two score choices to that prompt:\n", "```\n", "- (F) The submitted answer asks for more context, specifics or clarification but provides factual information consistent with the expert answer.\n", "- (G) The submitted answer asks for more context, specifics or clarification but does not provide factual information consistent with the expert answer.\n", "```\n", "These will score (F) = 0.2 and (G) = 0 so the model gets some credit if there was any context it was able to gather from the user's input.\n", "\n", "We can then use this spec and the `LLMClassifierFromSpec` function to create our customer scorer to use in the eval function.\n", "\n", "Read more about [defining your own scorers](https://www.braintrust.dev/docs/guides/evals/write#define-your-own-scorers) in the documentation."]}, {"cell_type": "markdown", "id": "f49fb6a3-e8b1-4379-b7b3-899ca4b7936a", "metadata": {}, "source": ["#### Re-running the eval\n", "\n", "Let's now use this updated scorer and run the experiment again."]}, {"cell_type": "code", "execution_count": null, "id": "e35c48a2-a8ae-4e3c-97e7-b64b29e6164d", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["<PERSON><PERSON>(\"Chat assistant\", {\n", "  experimentName: \"gpt-4o assistant - no history\",\n", "  data: () =>\n", "    dataset.map((data) => ({ input: data.input, expected: data.expected })),\n", "  task: runTask,\n", "  scores: [Factual],\n", "  trialCount: 3,\n", "  metadata: {\n", "    model: \"gpt-4o\",\n", "    prompt: \"You are a helpful and polite assistant who knows about sports.\",\n", "  },\n", "});"]}, {"cell_type": "code", "execution_count": null, "id": "8a573e07-782f-4a91-8345-a991665d50f9", "metadata": {}, "outputs": [], "source": ["Experiment gpt - 4o assistant - no history - 934e5ca2 is running at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20no%20history-934e5ca2\n", " ████████████████████████████████████████ | Chat assistant[experimentName = gpt - 4o... | 100 % | 15 / 15 datapoints\n", "\n", "\n", "=========================SUMMARY=========================\n", "gpt-4o assistant - no history-934e5ca2 compared to gpt-4o assistant - no history:\n", "6.67% (-54.67%) 'Factuality' score      (0 improvements, 5 regressions)\n", "\n", "4.77s 'duration'        (2 improvements, 3 regressions)\n", "0.01$ 'estimated_cost'  (2 improvements, 3 regressions)\n", "\n", "See results for gpt-4o assistant - no history-934e5ca2 at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20no%20history-934e5ca2"]}, {"cell_type": "markdown", "id": "e0543874-8f94-4fad-a312-1021838da808", "metadata": {}, "source": ["6.67% as a score aligns much better with what we expected. Let's look again into the results of this experiment.\n", "\n", "#### Interpreting the results\n", "\n", "![no-history-custom-score](./assets/no-history-custom-score.png)\n", "\n", "In the table we can see the `output` fields in which the chat completion responses are requesting more context. In one of the experiment that had a non-zero score, we can see that the model asked for some clarification, but was able to understand from the question that the user was inquiring about a controversial World Series. Nice!\n", "\n", "![no-history-custom-score-cot](./assets/no-history-custom-score-cot.png)\n", "\n", "Looking into how the score was determined, we can see that the factual information aligned with the expert answer but the submitted answer still asks for more context, resulting in a score of 20% which is what we expect."]}, {"cell_type": "markdown", "id": "fa126329-71c3-4e3a-be75-97337cdb6d20", "metadata": {}, "source": ["### Assistant with chat history\n", "\n", "Now let's shift and see how providing the chat history improves the experiment."]}, {"cell_type": "markdown", "id": "9892f41e-34ef-40c0-9b38-646d4a480f8e", "metadata": {}, "source": ["#### Update the data, task function and scorer function\n", "\n", "We need to edit the inputs to the `Eval` function so we can pass the chat history to the chat completion request."]}, {"cell_type": "code", "execution_count": 8, "id": "c872e61e-5da9-415f-83dc-728d8643dc5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  input: {\n", "    input: \u001b[32m\"who won the men's trophy that year?\"\u001b[39m,\n", "    chat_history: [ \u001b[36m[Object]\u001b[39m, \u001b[36m[Object]\u001b[39m ]\n", "  },\n", "  expected: \u001b[32m\"In 2018, the men's Ballon d'Or was awarded to <PERSON><PERSON>.\"\u001b[39m\n", "}\n"]}], "source": ["const experimentData = dataset.map((data) => ({\n", "  input: { input: data.input, chat_history: data.chat_history },\n", "  expected: data.expected,\n", "}));\n", "console.log(experimentData[0]);\n", "\n", "async function runTask({\n", "  input,\n", "  chat_history,\n", "}: {\n", "  input: string;\n", "  chat_history: ChatTurn[];\n", "}) {\n", "  const client = wrapOpenAI(\n", "    new OpenAI({\n", "      baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "      apiKey: process.env.OPENAI_API_KEY ?? \"\", // Can use OpenAI, Anthropic, Mistral, etc. API keys here\n", "    }),\n", "  );\n", "\n", "  const response = await client.chat.completions.create({\n", "    model: \"gpt-4o\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content:\n", "          \"You are a helpful and polite assistant who knows about sports.\",\n", "      },\n", "      ...chat_history,\n", "      {\n", "        role: \"user\",\n", "        content: input,\n", "      },\n", "    ],\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}\n", "\n", "function Factual(args: {\n", "  input: {\n", "    input: string;\n", "    chat_history: ChatTurn[];\n", "  };\n", "  output: string;\n", "  expected: string;\n", "}): Score | Promise<Score> {\n", "  const factualityScorer = LLMClassifierFromSpec(\"Factuality\", {\n", "    prompt: `You are comparing a submitted answer to an expert answer on a given question. Here is the data:\n", "              [BEGIN DATA]\n", "              ************\n", "              [Question]: {{{input}}}\n", "              ************\n", "              [Expert]: {{{expected}}}\n", "              ************\n", "              [Submission]: {{{output}}}\n", "              ************\n", "              [END DATA]\n", "\n", "              Compare the factual content of the submitted answer with the expert answer. Ignore any differences in style, grammar, or punctuation.\n", "              The submitted answer may either be a subset or superset of the expert answer, or it may conflict with it. Determine which case applies. Answer the question by selecting one of the following options:\n", "              (A) The submitted answer is a subset of the expert answer and is fully consistent with it.\n", "              (B) The submitted answer is a superset of the expert answer and is fully consistent with it.\n", "              (C) The submitted answer contains all the same details as the expert answer.\n", "              (D) There is a disagreement between the submitted answer and the expert answer.\n", "              (E) The answers differ, but these differences don't matter from the perspective of factuality.\n", "              (F) The submitted answer asks for more context, specifics or clarification but provides factual information consistent with the expert answer.\n", "              (G) The submitted answer asks for more context, specifics or clarification but does not provide factual information consistent with the expert answer.`,\n", "    choice_scores: {\n", "      A: 0.4,\n", "      B: 0.6,\n", "      C: 1,\n", "      D: 0,\n", "      E: 1,\n", "      F: 0.2,\n", "      G: 0,\n", "    },\n", "  });\n", "  return factualityScorer(args);\n", "}"]}, {"cell_type": "markdown", "id": "58e1ae58-4022-459f-a10a-71c5a49023a9", "metadata": {}, "source": ["We update the parameter to the task function to accept both the `input` string and the `chat_history` array and add the `chat_history` into the messages array in the chat completion request, done here using the spread `...` syntax.\n", "\n", "We also need to update the `experimentData` and `Factual` function parameters to align with these changes."]}, {"cell_type": "markdown", "id": "48347599-378f-4f87-a683-1cb95b2fc753", "metadata": {}, "source": ["#### Running the eval\n", "\n", "Use the updated variables and functions to run a new eval."]}, {"cell_type": "code", "execution_count": null, "id": "a15b432d-1a05-4d57-ba53-a4d571755281", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>(\"Chat assistant\", {\n", "  experimentName: \"gpt-4o assistant\",\n", "  data: () => experimentData,\n", "  task: runTask,\n", "  scores: [Factual],\n", "  trialCount: 3,\n", "  metadata: {\n", "    model: \"gpt-4o\",\n", "    prompt: \"You are a helpful and polite assistant who knows about sports.\",\n", "  },\n", "});"]}, {"cell_type": "code", "execution_count": null, "id": "f9cf8ff4-82cf-4cd3-8ea3-be4c397404e1", "metadata": {}, "outputs": [], "source": ["Experiment gpt - 4o assistant is running at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant\n", " ████████████████████████████████████████ | Chat assistant[experimentName = gpt - 4o... | 100 % | 15 / 15 datapoints\n", "\n", "\n", "=========================SUMMARY=========================\n", "gpt-4o assistant compared to gpt-4o assistant - no history-934e5ca2:\n", "60.00% 'Factuality' score       (0 improvements, 0 regressions)\n", "\n", "4.34s 'duration'        (0 improvements, 0 regressions)\n", "0.01$ 'estimated_cost'  (0 improvements, 0 regressions)\n", "\n", "See results for gpt-4o assistant at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant"]}, {"cell_type": "markdown", "id": "0e8ed26b-5eb3-4dd3-9904-40bb2b923c10", "metadata": {}, "source": ["60% score is a definite improvement from 4%.\n", "\n", "You'll notice that it says there were 0 improvements and 0 regressions compared to the last experiment `gpt-4o assistant - no history-934e5ca2` we ran. This is because by default, Braintrust uses the `input` field to match rows across experiments. From the dashboard, we can customize the comparison key ([see docs](https://www.braintrust.dev/docs/guides/evals/interpret#customizing-the-comparison-key)) by going to the [project configuration page](https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/configuration).\n", "\n", "#### Update experiment comparison for diff mode\n", "\n", "Let's go back to the dashboard.\n", "\n", "For this cookbook, we can use the `expected` field as the comparison key because this field is unique in our small dataset.\n", "\n", "In the Configuration tab, go to the bottom of the page to update the comparison key:\n", "\n", "![comparison-key](./assets/comparison-key.png)"]}, {"cell_type": "markdown", "id": "ef20ddb5-029f-4212-a06a-a4aa05d461bd", "metadata": {}, "source": ["#### Interpreting the results\n", "\n", "Turn on diff mode using the toggle on the upper right of the table.\n", "\n", "![experiment-diff](./assets/experiment-diff.png)\n", "\n", "Since we updated the comparison key, we can now see the improvements in the Factuality score between the experiment run with chat history and the most recent one run without for each of the examples. If we also click into a trace, we can see the change in input parameters that we made above where it went from a `string` to an object with `input` and `chat_history` fields.\n", "\n", "All of our rows scored 60% in this experiment. If we look into each trace, this means the submitted answer includes all the details from the expert answer with some additional information.\n", "\n", "60% is an improvement from the previous run, but we can do better. Since it seems like the chat completion is always returning more than necessary, let's see if we can tweak our prompt to have the output be more concise."]}, {"cell_type": "markdown", "id": "2e6af882-b02c-4373-a28d-adf80b051f17", "metadata": {}, "source": ["#### Improving the result\n", "\n", "Let's update the system prompt used in the chat completion request."]}, {"cell_type": "code", "execution_count": 9, "id": "cf32ec26-72b6-45f8-86b5-6d95f27e73f0", "metadata": {}, "outputs": [], "source": ["async function runTask({\n", "  input,\n", "  chat_history,\n", "}: {\n", "  input: string;\n", "  chat_history: ChatTurn[];\n", "}) {\n", "  const client = wrapOpenAI(\n", "    new OpenAI({\n", "      baseURL: \"https://api.braintrust.dev/v1/proxy\",\n", "      apiKey: process.env.OPENAI_API_KEY ?? \"\", // Can use OpenAI, Anthropic, Mistral etc. API keys here\n", "    }),\n", "  );\n", "\n", "  const response = await client.chat.completions.create({\n", "    model: \"gpt-4o\",\n", "    messages: [\n", "      {\n", "        role: \"system\",\n", "        content:\n", "          \"You are a helpful, polite assistant who knows about sports. Only answer the question; don't add additional information outside of what was asked.\",\n", "      },\n", "      ...chat_history,\n", "      {\n", "        role: \"user\",\n", "        content: input,\n", "      },\n", "    ],\n", "  });\n", "  return response.choices[0].message.content || \"\";\n", "}"]}, {"cell_type": "markdown", "id": "1ba8b405-019b-4917-b5c0-328a50e0f203", "metadata": {}, "source": ["In the task function, we'll update the `system` message to specify the output should be precise and then run the eval again."]}, {"cell_type": "code", "execution_count": null, "id": "77471866-6008-4c33-98dc-4c3c102437f7", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>(\"Chat assistant\", {\n", "  experimentName: \"gpt-4o assistant - concise\",\n", "  data: () => experimentData,\n", "  task: runTask,\n", "  scores: [Factual],\n", "  trialCount: 3,\n", "  metadata: {\n", "    model: \"gpt-4o\",\n", "    prompt:\n", "      \"You are a helpful, polite assistant who knows about sports. Only answer the question; don't add additional information outside of what was asked.\",\n", "  },\n", "});\n"]}, {"cell_type": "code", "execution_count": null, "id": "adbd187e-80d2-45fb-9e4b-99210f81fcf0", "metadata": {}, "outputs": [], "source": ["Experiment gpt - 4o assistant - concise is running at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20concise\n", " ████████████████████████████████████████ | Chat assistant[experimentName = gpt - 4o... | 100 % | 15 / 15 datapoints\n", "\n", "\n", "=========================SUMMARY=========================\n", "gpt-4o assistant - concise compared to gpt-4o assistant:\n", "86.67% (+26.67%) 'Factuality' score     (4 improvements, 0 regressions)\n", "\n", "1.89s 'duration'        (5 improvements, 0 regressions)\n", "0.01$ 'estimated_cost'  (4 improvements, 1 regressions)\n", "\n", "See results for gpt-4o assistant - concise at https://www.braintrust.dev/app/braintrustdata.com/p/Chat%20assistant/experiments/gpt-4o%20assistant%20-%20concise"]}, {"cell_type": "markdown", "id": "1f5a05c8-128e-4a7d-ae9c-d833c513a5c2", "metadata": {}, "source": ["Let's go into the dashboard and see the new experiment.\n", "\n", "![concise-diff](./assets/concise-diff.png)\n", "\n", "Success! We got a 27 percentage point increase in factuality, up to an average score of 87% for this experiment with our updated prompt."]}, {"cell_type": "markdown", "id": "5b7d34a7-e406-4380-bc4c-5b4a027bc2c5", "metadata": {}, "source": ["### Conclusion\n", "\n", "We've seen in this cookbook how to evaluate a chat assistant and visualized how the chat history effects the output of the chat completion. Along the way, we also utilized some other functionality such as updating the comparison key in the diff view and creating a custom scoring function.\n", "\n", "Try seeing how you can improve the outputs and scores even further!"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}