{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generating release notes automatically with hill climbing to improve quality\n", "\n", "This tutorial walks through how to automatically generate release notes for a repository using\n", "the Github API and an LLM. Automatically generated release notes are tough to evaluate,\n", "and you often don't have pre-existing benchmark data to evaluate them on.\n", "\n", "To work around this, we'll use [hill climbing](https://braintrust.dev/docs/guides/evals#hill-climbing) to iterate on our prompt, comparing new results to previous experiments to see if we're making progress.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installing dependencies\n", "\n", "To see a list of dependencies, you can view the accompanying [package.json](https://github.com/braintrustdata/braintrust-cookbook/tree/main/examples/Github-Issues/package.json) file. Feel free to copy/paste snippets of this code to run in your environment, or use [tslab](https://github.com/yunabe/tslab) to run the tutorial in a Jupyter notebook.\n", "\n", "## Downloading the data\n", "\n", "We'll start by downloading some commit data from Github using the `octokit` SDK. We'll use the [Braintrust SDK](https://github.com/braintrustdata/braintrust-sdk) from November 2023 through January 2024.\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["const START_DATE = \"2023-11-26\";\n", "const END_DATE = \"2024-01-27\";\n", "const REPO_OWNER = \"braintrustdata\";\n", "const REPO_NAME = \"braintrust-sdk\";\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 78 commits\n"]}], "source": ["import { Octokit } from \"@octokit/rest\";\n", "import { GetResponseTypeFromEndpointMethod } from \"@octokit/types\";\n", "\n", "type CommitsResponse = GetResponseTypeFromEndpointMethod<\n", "  typeof octokit.rest.repos.listCommits\n", ">;\n", "type Commit = CommitsResponse[\"data\"][number];\n", "\n", "// Octokit.js\n", "// https://github.com/octokit/core.js#readme\n", "const octokit: Octokit = new Octokit({\n", "  auth: process.env.GITHUB_ACCESS_TOKEN || \"Your Github Access Token\",\n", "});\n", "\n", "const commits: CommitsResponse = await octokit.rest.repos.listCommits({\n", "  owner: REPO_OWNER,\n", "  repo: REPO_NAME,\n", "  since: START_DATE,\n", "  until: END_DATE,\n", "  per_page: 1000,\n", "});\n", "\n", "console.log(\"Retrieved\", commits.data.length, \"commits\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome, now let's bucket the commits into weeks.\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-11-26 7\n", "2023-12-03 14\n", "2023-12-10 3\n", "2023-12-17 23\n", "2023-12-24 2\n", "2023-12-31 8\n", "2024-01-07 8\n", "2024-01-14 3\n", "2024-01-21 10\n"]}], "source": ["import moment from \"moment\";\n", "\n", "interface CommitInfo {\n", "  url: string;\n", "  html_url: string;\n", "  sha: string;\n", "  commit: {\n", "    author: {\n", "      name?: string;\n", "      email?: string;\n", "      date?: string;\n", "    };\n", "    message: string;\n", "  };\n", "}\n", "\n", "const weeks: Record<string, CommitInfo[]> = {};\n", "for (const commit of commits.data) {\n", "  const week = moment(commit.commit.author.date, \"YYYY-MM-DD\")\n", "    .startOf(\"week\")\n", "    .format(\"YYYY-MM-DD\");\n", "  weeks[week] = (weeks[week] || []).concat([\n", "    // Simplify the commit data structure\n", "    {\n", "      sha: commit.sha,\n", "      url: commit.url,\n", "      html_url: commit.html_url,\n", "      commit: {\n", "        author: commit.commit.author,\n", "        message: commit.commit.message,\n", "      },\n", "    },\n", "  ]);\n", "}\n", "\n", "const sortedWeeks = Object.keys(weeks).sort((a, b) =>\n", "  moment(a).diff(moment(b))\n", ");\n", "for (const week of sortedWeeks) {\n", "  console.log(week, weeks[week].length);\n", "  weeks[week].sort((a, b) =>\n", "    moment(a.commit.author.date).diff(moment(b.commit.author.date))\n", "  );\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating release notes\n", "\n", "Awesome! It looks like we have 9 solid weeks of data to work with. Let's take a look at the first week of data.\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----- 86316b6622c23ef4f702289b8ada30ab50417f2d -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/86316b6622c23ef4f702289b8ada30ab50417f2d\n", "2023-11-28T06:57:57Z\n", "Show --verbose warning at the end of the error list (#50)\n", "\n", "Users were reporting that the `--verbose` flag is lost if it's at the\n", "beginning of the list of errors. This change simply prints the\n", "clarification at the end (and adds it to python)\n", "\n", "\n", "----- 1ea8e1bb3de83cf0021af6488d06710aa6835d7b -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/1ea8e1bb3de83cf0021af6488d06710aa6835d7b\n", "2023-11-28T18:48:56Z\n", "Bump autoevals and version\n", "\n", "\n", "----- 322aba85bbf0b75948cc97ef750d405710a8c9f1 -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/322aba85bbf0b75948cc97ef750d405710a8c9f1\n", "2023-11-29T23:04:36Z\n", "Small fixes (#51)\n", "\n", "* Change built-in examples to use Eval framework\n", "* Use `evaluator` instead of `_evals[evalName]` to access metadata. The\n", "latter is not set if you're running <PERSON><PERSON> directly in a script.\n", "\n", "\n", "----- ad0b18fd250e8e2b0e78f8405b4323a4abb3f7ce -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/ad0b18fd250e8e2b0e78f8405b4323a4abb3f7ce\n", "2023-11-30T17:32:02Z\n", "Bump autoevals\n", "\n", "\n", "----- 98de10b6e8b44e13f65010cbf170f2b448728c46 -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/98de10b6e8b44e13f65010cbf170f2b448728c46\n", "2023-12-01T17:51:31Z\n", "Python eval framework: parallelize non-async components. (#53)\n", "\n", "Fixes BRA-661\n", "\n", "\n", "----- a1032508521f4967a5d1cdf9d1330afce97b7a4e -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/a1032508521f4967a5d1cdf9d1330afce97b7a4e\n", "2023-12-01T19:59:04Z\n", "Bump version\n", "\n", "\n", "----- 14599fe1d9c66e058095b318cb2c8361867eff76 -----\n", "https://github.com/braintrustdata/braintrust-sdk/commit/14599fe1d9c66e058095b318cb2c8361867eff76\n", "2023-12-01T21:01:39Z\n", "Bump autoevals\n", "\n", "\n"]}], "source": ["const firstWeek = weeks[sortedWeeks[0]];\n", "for (const commit of firstWeek) {\n", "  console.log(\"-----\", commit.sha, \"-----\");\n", "  console.log(commit.html_url);\n", "  console.log(commit.commit.author.date);\n", "  console.log(commit.commit.message);\n", "  console.log(\"\\n\");\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Building the prompt\n", "\n", "Next, we'll try to generate release notes using `gpt-3.5-turbo` and a relatively simple prompt.\n", "\n", "We'll start by initializing an OpenAI client and wrapping it with some Braintrust instrumentation. `wrapOpenAI`\n", "is initially a no-op, but later on when we use Braintrust, it will help us capture helpful debugging information about the model's performance.\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import { wrapOpenAI } from \"braintrust\";\n", "import { OpenAI } from \"openai\";\n", "\n", "const client = wrapOpenAI(\n", "  new OpenAI({\n", "    apiKey: process.env.OPENAI_API_KEY || \"Your OpenAI API Key\",\n", "  })\n", ");\n", "\n", "const MODEL: string = \"gpt-3.5-turbo\";\n", "const SEED = 123;\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Release Notes:\n", "\n", "- Show --verbose warning at the end of the error list (#50):\n", "  - Users were reporting that the `--verbose` flag is lost if it's at the\n", "    beginning of the list of errors. This change simply prints the\n", "    clarification at the end (and adds it to python).\n", "\n", "- Small fixes (#51):\n", "  - Change built-in examples to use Eval framework\n", "  - Use `evaluator` instead of `_evals[evalName]` to access metadata. The\n", "    latter is not set if you're running <PERSON><PERSON> directly in a script.\n", "\n", "- Python eval framework: parallelize non-async components. (#53):\n", "  - Fixes BRA-661\n"]}], "source": ["import { ChatCompletionMessageParam } from \"openai/resources\";\n", "import { traced } from \"braintrust\";\n", "\n", "function serializeCommit(info: CommitInfo): string {\n", "  return `SHA: ${info.sha}\n", "AUTHOR: ${info.commit.author.name} <${info.commit.author.email}>\n", "DATE: ${info.commit.author.date}\n", "MESSAGE: ${info.commit.message}`;\n", "}\n", "\n", "function generatePrompt(commits: CommitInfo[]): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `You are an expert technical writer who generates release notes for the Braintrust SDK.\n", "You will be provided a list of commits, including their message, author, and date, and you will generate\n", "a full list of release notes, in markdown list format, across the commits. You should include the important\n", "details, but if a commit is not relevant to the release notes, you can skip it.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content:\n", "        \"Commits: \\n\" + commits.map((c) => serializeCommit(c)).join(\"\\n\\n\"),\n", "    },\n", "  ];\n", "}\n", "\n", "async function generateReleaseNotes(input: CommitInfo[]) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await client.chat.completions.create({\n", "        model: MODEL,\n", "        messages: generate<PERSON>rompt(input),\n", "        seed: SEED,\n", "      });\n", "      return response.choices[0].message.content;\n", "    },\n", "    {\n", "      name: \"generateReleaseNotes\",\n", "    }\n", "  );\n", "}\n", "\n", "const releaseNotes = await generateReleaseNotes(firstWeek);\n", "console.log(releaseNotes);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the initial prompt\n", "\n", "Interesting, at a glance, it looks like the model is doing a decent job, but it's missing some key details like the version updates. Before we go any further, let's benchmark its performance\n", "by writing an eval.\n", "\n", "### Building a scorer\n", "\n", "Let's start by implementing a scorer that can assess how well the new release notes capture the list of commits. To make the scoring function job's easy, we'll do a few tricks:\n", "\n", "- Use gpt-4 instead of gpt-3.5-turbo\n", "- Only present it the commit summaries, without the SHAs or author info, to reduce noise.\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: '<PERSON><PERSON>',\n", "  score: 0.5,\n", "  metadata: {\n", "    rationale: \"The release notes cover the changes in commits 'Show --verbose warning at the end of the error list (#50)', 'Small fixes (#51)', and 'Python eval framework: parallelize non-async components. (#53)'.\\n\" +\n", "      \"The release notes do not mention the changes in the commits 'Bump autoevals and version', 'Bump autoevals', 'Bump version', and 'Bump autoevals'.\\n\" +\n", "      'Therefore, the release notes capture some, but not all, of the changes.',\n", "    choice: 'b'\n", "  },\n", "  error: undefined\n", "}\n"]}], "source": ["import { LLMClassifierFromTemplate, Scorer, Score } from \"autoevals\";\n", "\n", "const GRADER: string = \"gpt-4\";\n", "\n", "const promptTemplate = `You are a technical writer who helps assess how effectively a product team generates\n", "release notes based on git commits. You will look at the commit messages and determine if the release\n", "notes sufficiently cover the changes.\n", " \n", "Messages:\n", "\n", "{{input}}\n", "\n", "\n", "Release Notes:\n", "\n", "{{output}}\n", "\n", "Assess the quality of the release notes by selecting one of the following options. As you think through\n", "the changes, list out which messages are not included in the release notes or info that is made up.\n", "\n", "a) The release notes are excellent and cover all the changes.\n", "b) The release notes capture some, but not all, of the changes.\n", "c) The release notes include changes that are not in the commit messages.\n", "d) The release notes are not useful and do not cover any changes.`;\n", "\n", "const evaluator: Scorer<any, { input: string; output: string }> =\n", "  LLMClassifierFromTemplate<{ input: string }>({\n", "    name: \"<PERSON><PERSON>\",\n", "    promptTemplate,\n", "    choiceScores: { a: 1, b: 0.5, c: 0.25, d: 0 },\n", "    useCoT: true,\n", "    model: GRADER,\n", "  });\n", "\n", "async function comprehensiveness({\n", "  input,\n", "  output,\n", "}: {\n", "  input: CommitInfo[];\n", "  output: string;\n", "}): Promise<Score> {\n", "  return evaluator({\n", "    input: input.map((c) => \"-----\\n\" + c.commit.message).join(\"\\n\\n\"),\n", "    output,\n", "  });\n", "}\n", "\n", "await comprehensiveness({ input: firstWeek, output: releaseNotes });\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also score the output's writing quality. We want to make sure the release notes are well-written, concise, and do not contain repetitive content.\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: 'WritingQuality',\n", "  score: 1,\n", "  metadata: {\n", "    rationale: 'The release notes are formatted correctly, using markdown for code and issue references.\\n' +\n", "      'There is no superfluous wording or repeated information in the release notes.\\n' +\n", "      'The content of the release notes is relevant to the software and describes changes made in the update.\\n' +\n", "      'Each change is explained clearly and concisely, making it easy for users to understand what has been updated or fixed.',\n", "    choice: 'a'\n", "  },\n", "  error: undefined\n", "}\n"]}], "source": ["const promptTemplate = `You are a technical writer who helps assess the writing quality of release notes.\n", " \n", "Release Notes:\n", "\n", "{{output}}\n", "\n", "Assess the quality of the release notes by selecting one of the following options. As you think through\n", "the changes, list out which messages are not included in the release notes or info that is made up.\n", "\n", "a) The release notes are clear and concise.\n", "b) The release notes are not formatted as markdown/html, but otherwise are well written.\n", "c) The release notes contain superfluous wording, for example statements like \"let me know if you have any questions\".\n", "d) The release notes contain repeated information.\n", "e) The release notes are off-topic to Braintrust's software and do not contain relevant information.`;\n", "\n", "const evaluator: Scorer<any, { output: string }> = LLMClassifierFromTemplate({\n", "  name: \"WritingQuality\",\n", "  promptTemplate,\n", "  choiceScores: { a: 1, b: 0.75, c: 0.5, d: 0.25, e: 0 },\n", "  useCoT: true,\n", "  model: GRADER,\n", "});\n", "\n", "async function writingQuality({ output }: { output: string }): Promise<Score> {\n", "  return evaluator({\n", "    output,\n", "  });\n", "}\n", "\n", "await writingQuality({ output: releaseNotes });\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027712',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027712',\n", "  comparisonExperimentName: undefined,\n", "  scores: undefined,\n", "  metrics: undefined\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Release Notes Cookbook                   |   9% | 9/100 datapoints\n"]}], "source": ["import { <PERSON><PERSON> } from \"braintrust\";\n", "\n", "let lastExperiment = await <PERSON>l<CommitInfo[], string, unknown>(\n", "  \"Release Notes Cookbook\",\n", "  {\n", "    data: Object.entries(weeks).map(([week, commits]) => ({\n", "      input: commits,\n", "      metadata: { week },\n", "    })),\n", "    task: generateReleaseNotes,\n", "    scores: [comprehensiveness, writingQuality],\n", "  }\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Wow! We're doing a great job with writing quality, but scored lower on comprehensiveness.\n", "\n", "![Initial experiment](./assets/initial-experiment.png)\n", "\n", "Braintrust makes it easy to see concrete examples of the failure cases. For example this grader mentions the new lazy login behavior is missing from the release notes:\n", "\n", "![Reason](./assets/debug-reasons-1.png)\n", "\n", "and if we click into the model's output, we can see that it's indeed missing:\n", "\n", "![Output](./assets/debug-output-1.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improving the prompt\n", "\n", "Let's see if we can improve the model's performance by tweaking the prompt. Perhaps we were too eager about excluding irrelevant details in the original prompt. Let's tweak the wording to make sure it's comprehensive.\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Release Notes:\n", "\n", "- Show --verbose warning at the end of the error list  \n", "  Users were reporting that the `--verbose` flag is lost if it's at the beginning of the list of errors. This change simply prints the clarification at the end (and adds it to python)\n", "\n", "- Bump autoevals and version\n", "\n", "- Small fixes  \n", "  - Change built-in examples to use Eval framework  \n", "  - Use `evaluator` instead of `_evals[evalName]` to access metadata. The latter is not set if you're running Evals directly in a script.\n", "\n", "- Bump autoevals\n", "\n", "- Python eval framework: parallelize non-async components  \n", "  Fixes BRA-661\n", "\n", "- Bump version\n", "\n", "- Bump autoevals\n"]}], "source": ["function generatePrompt(commits: CommitInfo[]): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `You are an expert technical writer who generates release notes for the Braintrust SDK.\n", "You will be provided a list of commits, including their message, author, and date, and you will generate\n", "a full list of release notes, in markdown list format, across the commits. You should make sure to include\n", "some information about each commit, without the commit sha, url, or author info.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content:\n", "        \"Commits: \\n\" + commits.map((c) => serializeCommit(c)).join(\"\\n\\n\"),\n", "    },\n", "  ];\n", "}\n", "\n", "async function generateReleaseNotes(input: CommitInfo[]) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await client.chat.completions.create({\n", "        model: MODEL,\n", "        messages: generate<PERSON>rompt(input),\n", "        seed: SEED,\n", "      });\n", "      return response.choices[0].message.content;\n", "    },\n", "    {\n", "      name: \"generateReleaseNotes\",\n", "    }\n", "  );\n", "}\n", "\n", "await generateReleaseNotes(firstWeek);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hill climbing\n", "\n", "We'll use [hill climbing](https://www.braintrust.dev/docs/guides/evals#hill-climbing) to automatically use data from the previous experiment to compare to this one. Hill climbing is inspired by, but not exactly the same as, the term used in [numerical optimization](https://en.wikipedia.org/wiki/Hill_climbing). In the context of Braintrust, hill climbing is a way to iteratively improve a model's performance by comparing new experiments to previous ones. This is especially useful when you don't have a pre-existing benchmark to evaluate against.\n", "\n", "Both the `Comprehensiveness` and `WritingQuality` scores evaluate the `output` against the `input`, without considering a comparison point. To take advantage of hill climbing, we'll add another scorer, `Summary`, which will compare the `output` against the `data` from the previous experiment. To learn more about the `Summary` scorer, check out its [prompt](https://github.com/braintrustdata/autoevals/blob/main/templates/summary.yaml).\n", "\n", "To enable hill climbing, we just need to use `BaseExperiment()` as the `data` argument to `Eval()`. The `name` argument is optional, but since we know the exact experiment to compare to, we'll specify it. If you don't specify a name, Braintrust will automatically use the most recent ancestor on your main branch or the last experiment by timestamp as the comparison point.\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027732',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027732',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027712',\n", "  scores: {\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 0.75,\n", "      diff: -0.25,\n", "      improvements: 0,\n", "      regressions: 3\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.8611111111111112,\n", "      diff: 0.13888888888888895,\n", "      improvements: 4,\n", "      regressions: 2\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3663333257039388,\n", "      unit: 's',\n", "      diff: -0.006666713290744364,\n", "      improvements: 7,\n", "      regressions: 2\n", "    }\n", "  }\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Release Notes Cookbook                   |   9% | 9/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027732',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027732',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027712',\n", "  scores: {\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 0.75,\n", "      diff: -0.25,\n", "      improvements: 0,\n", "      regressions: 3\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.8611111111111112,\n", "      diff: 0.13888888888888895,\n", "      improvements: 4,\n", "      regressions: 2\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3663333257039388,\n", "      unit: 's',\n", "      diff: -0.006666713290744364,\n", "      improvements: 7,\n", "      regressions: 2\n", "    }\n", "  }\n", "}\n"]}], "source": ["import { BaseExperiment } from \"braintrust\";\n", "import { Summary } from \"autoevals\";\n", "\n", "async function releaseSummary({\n", "  input,\n", "  output,\n", "  expected,\n", "}: {\n", "  input: CommitInfo[];\n", "  output: string;\n", "  expected: string;\n", "}): Promise<Score> {\n", "  return Summary({\n", "    input: input.map((c) => \"-----\\n\" + c.commit.message).join(\"\\n\\n\"),\n", "    output,\n", "    expected,\n", "    model: GRADER,\n", "    useCoT: true,\n", "  });\n", "}\n", "\n", "lastExperiment = await <PERSON><PERSON><CommitInfo[], string, unknown>(\n", "  \"Release Notes Cookbook\",\n", "  {\n", "    data: BaseExperiment({ name: lastExperiment.experimentName }),\n", "    task: generateReleaseNotes,\n", "    scores: [comprehensiveness, writingQuality, releaseSummary],\n", "  }\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While we were able to boost the comprehensiveness score to 86%, it looks like we dropped the writing quality score by 25%.\n", "\n", "![Hill climbing experiment](./assets/second-experiment.png)\n", "\n", "Digging into a few examples, it appears that we're mentioning version bumps multiple times.\n", "\n", "![Reason](./assets/debug-reasons-2.png)\n", "\n", "![Output](./assets/debug-output-2.png)\n", "\n", "## Iterating further on the prompt\n", "\n", "Let's try to address this explicitly by tweaking the prompt. We'll continue to hill climb.\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Release Notes:\n", "\n", "- Show --verbose warning at the end of the error list (#50): Users were reporting that the `--verbose` flag is lost if it's at the beginning of the list of errors. This change simply prints the clarification at the end (and adds it to python).\n", "\n", "- Small fixes (#51): \n", "  - Change built-in examples to use Eval framework.\n", "  - Use `evaluator` instead of `_evals[evalName]` to access metadata. The latter is not set if you're running Evals directly in a script.\n", "\n", "- Python eval framework: parallelize non-async components. (#53): Fixes BRA-661.\n", "\n", "Please note that there were multiple version bumps and autoevals bumps.\n"]}], "source": ["import { ChatCompletionMessageParam } from \"openai/resources\";\n", "import { traced } from \"braintrust\";\n", "\n", "function generatePrompt(commits: CommitInfo[]): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `You are an expert technical writer who generates release notes for the Braintrust SDK.\n", "You will be provided a list of commits, including their message, author, and date, and you will generate\n", "a full list of release notes, in markdown list format, across the commits. You should make sure to include\n", "some information about each commit, without the commit sha, url, or author info. However, do not mention\n", "version bumps multiple times. If there are multiple version bumps, only mention the latest one.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content:\n", "        \"Commits: \\n\" + commits.map((c) => serializeCommit(c)).join(\"\\n\\n\"),\n", "    },\n", "  ];\n", "}\n", "\n", "async function generateReleaseNotes(input: CommitInfo[]) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await client.chat.completions.create({\n", "        model: MODEL,\n", "        messages: generate<PERSON>rompt(input),\n", "        seed: SEED,\n", "      });\n", "      return response.choices[0].message.content;\n", "    },\n", "    {\n", "      name: \"generateReleaseNotes\",\n", "    }\n", "  );\n", "}\n", "\n", "const releaseNotes = await generateReleaseNotes(firstWeek);\n", "console.log(releaseNotes);\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027750',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027750',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027732',\n", "  scores: {\n", "    Summary: {\n", "      name: '<PERSON><PERSON><PERSON>',\n", "      score: 0.4444444444444444,\n", "      diff: -0.2222222222222222,\n", "      improvements: 0,\n", "      regressions: 2\n", "    },\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 0.9166666666666666,\n", "      diff: 0.16666666666666663,\n", "      improvements: 2,\n", "      regressions: 0\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.7222222222222222,\n", "      diff: -0.13888888888888895,\n", "      improvements: 1,\n", "      regressions: 3\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3829999499850803,\n", "      unit: 's',\n", "      diff: 0.016666624281141518,\n", "      improvements: 6,\n", "      regressions: 3\n", "    }\n", "  }\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Release Notes Cookbook                   |   9% | 9/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027750',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027750',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027732',\n", "  scores: {\n", "    Summary: {\n", "      name: '<PERSON><PERSON><PERSON>',\n", "      score: 0.4444444444444444,\n", "      diff: -0.2222222222222222,\n", "      improvements: 0,\n", "      regressions: 2\n", "    },\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 0.9166666666666666,\n", "      diff: 0.16666666666666663,\n", "      improvements: 2,\n", "      regressions: 0\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.7222222222222222,\n", "      diff: -0.13888888888888895,\n", "      improvements: 1,\n", "      regressions: 3\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3829999499850803,\n", "      unit: 's',\n", "      diff: 0.016666624281141518,\n", "      improvements: 6,\n", "      regressions: 3\n", "    }\n", "  }\n", "}\n"]}], "source": ["lastExperiment = await <PERSON><PERSON><CommitInfo[], string, unknown>(\n", "  \"Release Notes Cookbook\",\n", "  {\n", "    data: BaseExperiment({ name: lastExperiment.experimentName }),\n", "    task: generateReleaseNotes,\n", "    scores: [comprehensiveness, writingQuality, releaseSummary],\n", "  }\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Sometimes hill climbing is not a linear process. It looks like while we've improved the writing quality, we've now dropped the comprehensiveness score as well as\n", "overall summary quality.\n", "\n", "![Hill climbing experiment](./assets/third-experiment.png)\n", "\n", "## Upgrading the model\n", "\n", "Let's try upgrading the model to `gpt-4-1106-turbo` and see if that helps. Perhaps we're hitting the limits of `gpt-3.5-turbo`.\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["async function generateReleaseNotes(input: CommitInfo[]) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await client.chat.completions.create({\n", "        model: \"gpt-4-1106-preview\",\n", "        messages: generate<PERSON>rompt(input),\n", "        seed: SEED,\n", "      });\n", "      return response.choices[0].message.content;\n", "    },\n", "    {\n", "      name: \"generateReleaseNotes\",\n", "    }\n", "  );\n", "}\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027779',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027779',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027750',\n", "  scores: {\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 1,\n", "      diff: 0.08333333333333337,\n", "      improvements: 1,\n", "      regressions: 0\n", "    },\n", "    Summary: {\n", "      name: '<PERSON><PERSON><PERSON>',\n", "      score: 0.7777777777777778,\n", "      diff: 0.33333333333333337,\n", "      improvements: 4,\n", "      regressions: 1\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.8333333333333334,\n", "      diff: 0.11111111111111116,\n", "      improvements: 5,\n", "      regressions: 2\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3962223529815674,\n", "      unit: 's',\n", "      diff: 0.013222402996487082,\n", "      improvements: 3,\n", "      regressions: 6\n", "    }\n", "  }\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": [" ████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ | Release Notes Cookbook                   |   9% | 9/100 datapoints\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{\n", "  projectName: 'Release Notes Cookbook',\n", "  experimentName: 'pr-hill-climbing-1707027779',\n", "  projectUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook',\n", "  experimentUrl: 'https://www.braintrust.dev/app/braintrust.dev/p/Release%20Notes%20Cookbook/pr-hill-climbing-1707027779',\n", "  comparisonExperimentName: 'pr-hill-climbing-1707027750',\n", "  scores: {\n", "    WritingQuality: {\n", "      name: 'WritingQuality',\n", "      score: 1,\n", "      diff: 0.08333333333333337,\n", "      improvements: 1,\n", "      regressions: 0\n", "    },\n", "    Summary: {\n", "      name: '<PERSON><PERSON><PERSON>',\n", "      score: 0.7777777777777778,\n", "      diff: 0.33333333333333337,\n", "      improvements: 4,\n", "      regressions: 1\n", "    },\n", "    Comprehensiveness: {\n", "      name: '<PERSON><PERSON>',\n", "      score: 0.8333333333333334,\n", "      diff: 0.11111111111111116,\n", "      improvements: 5,\n", "      regressions: 2\n", "    }\n", "  },\n", "  metrics: {\n", "    duration: {\n", "      name: 'duration',\n", "      metric: 0.3962223529815674,\n", "      unit: 's',\n", "      diff: 0.013222402996487082,\n", "      improvements: 3,\n", "      regressions: 6\n", "    }\n", "  }\n", "}\n"]}], "source": ["lastExperiment = await <PERSON><PERSON><CommitInfo[], string, unknown>(\n", "  \"Release Notes Cookbook\",\n", "  {\n", "    data: BaseExperiment({ name: lastExperiment.experimentName }),\n", "    task: generateReleaseNotes,\n", "    scores: [comprehensiveness, writingQuality, releaseSummary],\n", "  }\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Wow, nice! It looks like we've made an improvement across the board.\n", "\n", "![Hill climbing experiment](./assets/fourth-experiment.png)\n", "\n", "As a next step, we should dig into the example where we produced a worse summary than before, and hypothesize how to improve it.\n", "\n", "![Output vs Expected](./assets/output_v_expected.png)\n", "\n", "Happy evaluating!\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}