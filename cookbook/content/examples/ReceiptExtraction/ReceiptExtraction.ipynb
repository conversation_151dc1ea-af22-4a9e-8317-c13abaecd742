{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Receipt Extraction\n", "\n", "Document extraction is a use case that is [near and dear to my heart](https://www.youtube.com/watch?v=hoBtFhZRxzw). The last time I dug deeply into it, there were not nearly as many models\n", "capable of solving for it as there are today. In honor of Pixtral and LLaMa3.2, I thought it would be fun to revisit it with the classic SROIE dataset.\n", "\n", "The results are fascinating:\n", "\n", "- GPT-4o-mini performs the best, even better than GPT-4o\n", "- Pixtral 12B is almost as good as LLaMa 3.2 90B\n", "- The LLaMa models are almost 3x faster than the alternatives\n", "\n", "![Scatter plot](./assets/Scatter-Plot.png)\n", "\n", "Let's jump right in!\n", "\n", "## Install dependencies\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install autoevals braintrust requests openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup LLM clients\n", "\n", "We'll use OpenAI's GPT-4o, LLaMa 3.2 11B and 90B, and Pixtral 12B with a bunch of test cases from SROIE and see how they perform. You can access each of these models\n", "behind the vanilla OpenAI client using Braintrust's proxy.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import braintrust\n", "import openai\n", "\n", "client = braintrust.wrap_openai(\n", "    openai.AsyncOpenAI(\n", "        api_key=os.environ[\"BRAINTRUST_API_KEY\"],\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Downloading the data and sanity testing it\n", "\n", "The [zzzDavid/ICDAR-2019-SROIE](https://github.com/zzzDavid/ICDAR-2019-SROIE/tree/master) repo has neatly organized the data for us. The files are enumerated in a 3 digit convention and for each image (e.g. 002.jpg), there is a corresponding\n", "file (e.g. 002.json) with the key value pairs. There are a few different ways we could test the models:\n", "\n", "- Ask each model to extract values for specific keys\n", "- Ask each model to generate a value for each of a set of keys\n", "- Ask the model to extract all keys and values from the receipt\n", "\n", "To keep things simple, we'll go with the first option, but it would be interesting to do each and see how that affects the results.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'company': 'INDAH GIFT & HOME DECO',\n", " 'date': '19/10/2018',\n", " 'address': '27, <PERSON><PERSON><PERSON> DEDAP 13, TAMAN JOHOR JAYA, 81100 JOHOR BAHRU, JOHOR.',\n", " 'total': '60.30'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "\n", "indices = [str(i).zfill(3) for i in range(100)]\n", "\n", "\n", "def load_receipt(index):\n", "    img_path = f\"https://raw.githubusercontent.com/zzzDavid/ICDAR-2019-SROIE/refs/heads/master/data/img/{index}.jpg\"\n", "    json_path = f\"https://raw.githubusercontent.com/zzzDavid/ICDAR-2019-SROIE/refs/heads/master/data/key/{index}.json\"\n", "\n", "    json_response = requests.get(json_path).json()\n", "    return json_response, img_path\n", "\n", "\n", "fields, img_path = load_receipt(\"001\")\n", "fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<img src=\"https://raw.githubusercontent.com/zzzDavid/ICDAR-2019-SROIE/refs/heads/master/data/img/001.jpg\" />\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running model:  gpt-4o\n", "INDAH GIFT & HOME DECO\n", "\n", "\n", "Running model:  gpt-4o-mini\n", "INDAH GIFT & HOME DECO\n", "\n", "\n", "Running model:  meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo\n", "60.30\n", "\n", "\n", "Running model:  meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo\n", "INDAH GIFT & HOME DECO\n", "\n", "\n", "Running model:  pixtral-12b-2409\n", "tan woon yann\n", "\n", "\n"]}], "source": ["MODELS = [\n", "    \"gpt-4o\",\n", "    \"gpt-4o-mini\",\n", "    \"meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo\",\n", "    \"meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo\",\n", "    \"pixtral-12b-2409\",\n", "]\n", "\n", "SYSTEM_PROMPT = \"\"\"Extract the field '{key}' from the provided receipt. Return the extracted\n", "value, and nothing else. For example, if the field is 'Total' and the value is '100',\n", "you should just return '100'. If the field is not present, return null.\n", "\n", "Do not decorate the output with any explanation, or markdown. Just return the extracted value.\n", "\"\"\"\n", "\n", "\n", "@braintrust.traced\n", "async def extract_value(model, key, img_path):\n", "    response = await client.chat.completions.create(\n", "        model=model,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": SYSTEM_PROMPT.format(key=key)},\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [{\"type\": \"image_url\", \"image_url\": {\"url\": img_path}}],\n", "            },\n", "        ],\n", "        temperature=0,\n", "    )\n", "    return response.choices[0].message.content.strip()\n", "\n", "\n", "for model in MODELS:\n", "    print(\"Running model: \", model)\n", "    print(await extract_value(model, \"company\", img_path))\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the evaluation\n", "\n", "Now that we were able to perform a basic sanity test, let's run an evaluation! We'll use the `Levenshtein` and `Factuality` scorers to assess performance.\n", "`<PERSON><PERSON>htein` is heuristic and will tell us how closely the actual and expected strings match. Assuming some of the models will occasionally spit out superfluous\n", "explanation text, `Factuality`, which is LLM based, should be able to still give us an accuracy measurement.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Receipt Extraction - gpt-4o is running at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20gpt-4o\n", "Receipt Extraction [experiment_name=Receipt Extraction - gpt-4o] (data): 400it [00:00, 421962.17it/s]\n", "Receipt Extraction [experiment_name=Receipt Extraction - gpt-4o] (tasks): 100%|██████████| 400/400 [00:42<00:00,  9.48it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "84.40% 'Factuality'  score\n", "84.93% 'Levenshtein' score\n", "\n", "1223tok prompt_tokens\n", "12.06tok completion_tokens\n", "1235.06tok total_tokens\n", "\n", "See results for Receipt Extraction - gpt-4o at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20gpt-4o\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Experiment Receipt Extraction - gpt-4o-mini is running at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20gpt-4o-mini\n", "Receipt Extraction [experiment_name=Receipt Extraction - gpt-4o-mini] (data): 400it [00:00, 76419.86it/s]\n", "Receipt Extraction [experiment_name=Receipt Extraction - gpt-4o-mini] (tasks): 100%|██████████| 400/400 [00:41<00:00,  9.63it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Receipt Extraction - gpt-4o-mini compared to Receipt Extraction - gpt-4o:\n", "86.81% (+01.88%) 'Levenshtein' score\t(85 improvements, 48 regressions)\n", "81.40% (-03.00%) 'Factuality'  score\t(34 improvements, 42 regressions)\n", "\n", "38052.40tok (+3682940.00%) 'prompt_tokens'    \t(0 improvements, 400 regressions)\n", "12.31tok (+25.25%) 'completion_tokens'\t(62 improvements, 49 regressions)\n", "38064.71tok (+3682965.25%) 'total_tokens'     \t(0 improvements, 400 regressions)\n", "\n", "See results for Receipt Extraction - gpt-4o-mini at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20gpt-4o-mini\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Experiment Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo is running at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20meta-llama%2FLlama-3.2-11B-Vision-Instruct-Turbo\n", "Receipt Extraction [experiment_name=Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo] (data): 400it [00:00, 73234.17it/s]\n", "Receipt Extraction [experiment_name=Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo] (tasks): 100%|██████████| 400/400 [00:26<00:00, 15.01it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo compared to Receipt Extraction - gpt-4o-mini:\n", "52.78% (-34.04%) 'Levenshtein' score\t(41 improvements, 235 regressions)\n", "56.10% (-25.30%) 'Factuality'  score\t(38 improvements, 162 regressions)\n", "\n", "89tok (-3796340.00%) 'prompt_tokens'    \t(400 improvements, 0 regressions)\n", "11.31tok (-100.50%) 'completion_tokens'\t(125 improvements, 268 regressions)\n", "100.31tok (-3796440.50%) 'total_tokens'     \t(400 improvements, 0 regressions)\n", "\n", "See results for Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20meta-llama%2FLlama-3.2-11B-Vision-Instruct-Turbo\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Experiment Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo is running at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20meta-llama%2FLlama-3.2-90B-Vision-Instruct-Turbo\n", "Receipt Extraction [experiment_name=Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo] (data): 400it [00:00, 59897.24it/s]\n", "Receipt Extraction [experiment_name=Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo] (tasks): 100%|██████████| 400/400 [00:36<00:00, 10.90it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo compared to Receipt Extraction - meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo:\n", "77.52% (+24.74%) 'Levenshtein' score\t(212 improvements, 40 regressions)\n", "79.10% (+23.00%) 'Factuality'  score\t(154 improvements, 35 regressions)\n", "\n", "89tok (-) 'prompt_tokens'    \t(0 improvements, 0 regressions)\n", "14.45tok (+313.75%) 'completion_tokens'\t(75 improvements, 157 regressions)\n", "103.45tok (+313.75%) 'total_tokens'     \t(75 improvements, 157 regressions)\n", "\n", "See results for Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20meta-llama%2FLlama-3.2-90B-Vision-Instruct-Turbo\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Experiment Receipt Extraction - pixtral-12b-2409 is running at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20pixtral-12b-2409\n", "Receipt Extraction [experiment_name=Receipt Extraction - pixtral-12b-2409] (data): 400it [00:00, 125474.65it/s]\n", "Receipt Extraction [experiment_name=Receipt Extraction - pixtral-12b-2409] (tasks): 100%|██████████| 400/400 [00:50<00:00,  7.88it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Receipt Extraction - pixtral-12b-2409 compared to Receipt Extraction - meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo:\n", "66.75% (-12.35%) 'Factuality'  score\t(47 improvements, 98 regressions)\n", "73.56% (-03.96%) 'Levenshtein' score\t(72 improvements, 145 regressions)\n", "\n", "2364.51tok (+227551.00%) 'prompt_tokens'    \t(0 improvements, 400 regressions)\n", "19.22tok (+477.50%) 'completion_tokens'\t(121 improvements, 252 regressions)\n", "2383.73tok (+228028.50%) 'total_tokens'     \t(0 improvements, 400 regressions)\n", "\n", "See results for Receipt Extraction - pixtral-12b-2409 at https://www.braintrust.dev/app/braintrustdata.com/p/Receipt%20Extraction/experiments/Receipt%20Extraction%20-%20pixtral-12b-2409\n"]}], "source": ["from braintrust import EvalAsync\n", "\n", "from autoevals import Factuality, Levenshtein\n", "\n", "NUM_RECEIPTS = 100\n", "\n", "data = [\n", "    {\n", "        \"input\": {\n", "            \"key\": key,\n", "            \"img_path\": img_path,\n", "        },\n", "        \"expected\": value,\n", "        \"metadata\": {\n", "            \"idx\": idx,\n", "        },\n", "    }\n", "    for idx, (fields, img_path) in [\n", "        (idx, load_receipt(idx)) for idx in indices[:NUM_RECEIPTS]\n", "    ]\n", "    for key, value in fields.items()\n", "]\n", "\n", "for model in MODELS:\n", "\n", "    async def task(input):\n", "        return await extract_value(model, input[\"key\"], input[\"img_path\"])\n", "\n", "    await <PERSON><PERSON><PERSON>(\n", "        \"Receipt Extraction\",\n", "        data=data,\n", "        task=task,\n", "        scores=[<PERSON><PERSON><PERSON><PERSON>, <PERSON>actual<PERSON>],\n", "        experiment_name=f\"Receipt Extraction - {model}\",\n", "        metadata={\"model\": model},\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing the results\n", "\n", "Now that we have a bunch of results, let's take a look at some of the insights. If you click into the project in Braintrust, and then \"Group by\" model, you'll see the following:\n", "\n", "![grouped-by-model](./assets/Overview.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A few quick takeaways:\n", "\n", "- it looks like `gpt-4o-mini` performs the best -- even slightly better than `gpt-4o`.\n", "- Pixtral, a 12B model, performs significantly better than LLaMa 3.2 11B and almost as well as 90B.\n", "- Both LLaMa models (for these tests, hosted on [Together](https://together.xyz)), are dramatically faster -- almost 3x -- than GPT-4o, GPT-4o-mini, and Pixtral.\n", "\n", "Let's dig into these individual results in some more depth.\n", "\n", "### GPT-4o-mini vs GPT-4o\n", "\n", "If you click into the gpt-4o experiment and compare it to gpt-4o-mini, you can drill down into the individual improvements and regressions.\n", "\n", "![Regressions](./assets/GPT-4o-vs-4o-mini.gif)\n", "\n", "There are several different types of regressions, one of which appears to be that `gpt-4o` returns information in a different case than `gpt-4o-mini`. That may or\n", "may not be important for this use case, but if not, we could adjust our scoring functions to lowercase everything before comparing.\n", "\n", "![Casing](./assets/casing.png)\n", "\n", "### <PERSON><PERSON><PERSON> vs. LLaMa 3.2\n", "\n", "To compare Pixtral to LLaMa 3.2, you can do a multi-way comparison where the baseline is Pixtral.\n", "\n", "![Pixtral vs. LLaMa 3.2](./assets/pixtral-llama.png)\n", "\n", "If you filter to results where the `Levenshtein` score is 100%, and then drag to filter the score buckets where `Levenshtein` is less than 100% for LLaMa models, you'll\n", "see that 109 out of the 400 total tests match. That means that around 25% of the results had a perfect (100%) score for Pixtral and a lower score for LLaMa models.\n", "\n", "![Pixtral filter](./assets/Pixtral-Filter.png)\n", "\n", "It's useful to eyeball a few of these, where you'll see that many of the answers are just straight up incorrect for LLaMa 3.2 models.\n", "\n", "![Incorrect](./assets/Regression-example.png)\n", "\n", "### Speed vs. quality trade-off\n", "\n", "Back on the experiments page, it can be useful to view a scatterplot of score vs. duration to understand the trade-off between accuracy and speed.\n", "\n", "![Scatter plot](./assets/Scatter-Plot.png)\n", "\n", "The LLaMa 3.2 models are significantly faster—almost 3x—without sacrificing much accuracy. For certain use cases, this can be a significant factor to consider.\n", "\n", "## Where to go from here\n", "\n", "Now that we have some baseline evals in place, you can start to think about how to either iterate on these models to improve performance, or expand the testing to get a\n", "more comprehensive benchmark:\n", "\n", "- Try tweaking the prompt, perhaps with some few-shot examples, and see if that affects absolute and relative performance\n", "- Add a few more models into the mix and see how they perform\n", "- Dig into a few regressions and tweak the scoring methods to better reflect the actual use case\n", "\n", "To get started with this use case in Braintrust, you can [sign up for a free account](https://www.braintrust.dev/signup) and start with this Notebook. Happy evaluating!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}