{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Evaluating video QA\n", "\n", "Large language models have gotten extremely good at interpreting text, but understanding and answering questions about video content is a newer area of focus. It's especially difficult for domain-specific tasks, like sports broadcasts or educational videos, where specific visual details can completely change the answer.\n", "\n", "In this cookbook, we'll explore how to evaluate an LLM-based video question-answering (Video QA) system using the [MMVU dataset](https://mmvu-benchmark.github.io/). The MMVU dataset includes multi-disciplinary videos paired with questions and ground-truth answers, spanning many different topics.\n", "\n", "By the end, you'll have a repeatable workflow for quantitatively evaluating video QA performance, which you can adapt to different datasets or use cases.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started\n", "\n", "To follow along, start by installing the required packages:\n", "```bash\n", "pip install opencv-python requests datasets braintrust autoevals openai     \n", "```\n", "\n", "Next, make sure you have a [Braintrust](https://www.braintrust.dev/signup) account, along with an [OpenAI API key](https://platform.openai.com/). To authenticate with Braintrust, export your `BRAINTRUST_API_KEY` as an environment variable:\n", "```bash\n", "export BRAINTRUST_API_KEY=\"YOUR_API_KEY_HERE\"\n", "```\n", "<Callout type=\"info\">\n", "Exporting your API key is a best practice, but to make it easier to follow along with this cookbook, you can also hardcode it into the code below.\n", "</Callout>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll import our modules, define constants, and initialize the OpenAI client using the Braintrust proxy:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "import base64\n", "from typing import List, Dict, Any, Optional\n", "\n", "import cv2\n", "import requests\n", "from requests.adapters import HTTPAdapter\n", "from urllib3.util.retry import Retry\n", "\n", "from datasets import load_dataset\n", "\n", "import braintrust\n", "import autoevals\n", "from openai import OpenAI\n", "\n", "\n", "NUM_FRAMES = 32\n", "TARGET_DIMENSIONS = (512, 512)\n", "JPEG_QUALITY = 80\n", "\n", "RETRY_TOTAL = 3\n", "RETRY_BACKOFF = 0.5\n", "STATUS_FORCELIST = [502, 503, 504]\n", "\n", "# Uncomment the following line to hardcode your API key\n", "# os.environ[\"BRAINTRUST_API_KEY\"] = \"YOUR_API_KEY_HERE\"\n", "\n", "client = braintrust.wrap_openai(\n", "    OpenAI(\n", "        api_key=os.environ[\"BRAINTRUST_API_KEY\"],\n", "        base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extracting frames as base64\n", "\n", "To give the LLM visual context, we'll extract up to `NUM_FRAMES` frames from each video, resize them to `TARGET_DIMENSIONS`, and encode each frame as a base64 string. This lets us include key snapshots of the video in the prompt:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def extract_frames_b64(video_path: str) -> List[str]:\n", "    base64_frames = []\n", "    count = 0\n", "    video_capture = cv2.VideoCapture(video_path)\n", "\n", "    try:\n", "        while video_capture.isOpened() and count < NUM_FRAMES:\n", "            ret, frame = video_capture.read()\n", "            if not ret:\n", "                break\n", "\n", "            frame = cv2.resize(frame, TARGET_DIMENSIONS)\n", "            success, encoded_img = cv2.imencode(\n", "                \".jpg\", frame, [int(cv2.IMWRITE_JPEG_QUALITY), JPEG_QUALITY]\n", "            )\n", "            if success:\n", "                b64_str = base64.b64encode(encoded_img).decode(\"utf-8\")\n", "                base64_frames.append(b64_str)\n", "            count += 1\n", "    finally:\n", "        # Ensure the capture is always released\n", "        video_capture.release()\n", "\n", "    return base64_frames"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Downloading or reading raw video data\n", "\n", "Storing the raw video file as an attachment in Braintrust can simplify debugging by allowing you to easily reference the original source. The helper function `get_video_data` retrieves a video file either from a local path or URL:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_video_data(video_path: str, session: requests.Session) -> Optional[bytes]:\n", "    try:\n", "        if video_path.startswith(\"http\"):\n", "            response = session.get(video_path, timeout=10)\n", "            response.raise_for_status()\n", "            return response.content\n", "        else:\n", "            with open(video_path, \"rb\") as f:\n", "                return f.read()\n", "    except Exception as e:\n", "        print(f\"Error retrieving video data from {video_path}: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading the data\n", "\n", "We'll work with the first 20 samples from the MMVU validation split. Each sample contains a video, a question, and an expected answer. We'll convert the video frames to base64, attach the raw video bytes, and include the question-answer pair:\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def load_data_subset() -> List[Dict[str, Any]]:\n", "    ds = load_dataset(\"yale-nlp/MMVU\", split=\"validation[:20]\")\n", "\n", "    session = requests.Session()\n", "    retry = Retry(\n", "        total=RETRY_TOTAL,\n", "        backoff_factor=RETRY_BACKOFF,\n", "        status_forcelist=STATUS_FORCELIST,\n", "    )\n", "    adapter = HTTPAdapter(max_retries=retry)\n", "    session.mount(\"http://\", adapter)\n", "    session.mount(\"https://\", adapter)\n", "\n", "    data_list = []\n", "    for row in ds:\n", "        question_type = row[\"question_type\"]\n", "        video_path = row[\"video\"]\n", "\n", "        frames_b64 = extract_frames_b64(video_path)\n", "        raw_video = get_video_data(video_path, session)\n", "\n", "        choices_data = (\n", "            row.get(\"choices\") if question_type == \"multiple-choice\" else None\n", "        )\n", "\n", "        data_list.append(\n", "            {\n", "                \"input\": {\n", "                    \"frames_b64\": frames_b64,\n", "                    \"question\": row[\"question\"],\n", "                    \"question_type\": question_type,\n", "                    \"choices\": choices_data,\n", "                    \"video_attachment\": braintrust.Attachment(\n", "                        filename=os.path.basename(video_path),\n", "                        content_type=\"video/mp4\",\n", "                        data=raw_video,\n", "                    ),\n", "                },\n", "                \"expected\": {\"answer\": row[\"answer\"]},\n", "                \"metadata\": {\n", "                    \"subject\": row[\"metadata\"][\"subject\"],\n", "                    \"textbook\": row[\"metadata\"][\"textbook\"],\n", "                    \"question_type\": question_type,\n", "                },\n", "            }\n", "        )\n", "\n", "    session.close()\n", "    return data_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![attachments](./assets/attachments.gif)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the Braintrust UI, you'll be able to see the raw video attachment, the base64 frames, and a preview of the analyzed frames. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prompting the LLM   \n", "\n", "Next, we'll define a `video_qa` function to prompt the LLM for answers. It constructs a prompt with the base64-encoded frames, the question, and, for multiple-choice questions, the available options:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def video_qa(input_dict: Dict[str, Any]) -> str:\n", "    frames_b64 = input_dict[\"frames_b64\"]\n", "    question = input_dict[\"question\"]\n", "    question_type = input_dict.get(\"question_type\", \"open-ended\")\n", "    choices_data = input_dict.get(\"choices\")\n", "\n", "    content_blocks = [\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\"url\": f\"data:image/jpeg;base64,{b64}\", \"detail\": \"low\"},\n", "        }\n", "        for b64 in frames_b64\n", "    ]\n", "\n", "    if question_type == \"multiple-choice\" and choices_data:\n", "        if isinstance(choices_data, dict):\n", "            options_text = \"\\n\".join(\n", "                f\"{key}: {value}\" for key, value in choices_data.items()\n", "            )\n", "        else:\n", "            options_text = \"\\n\".join(\n", "                f\"{chr(65 + i)}: {option}\" for i, option in enumerate(choices_data)\n", "            )\n", "        prompt_text = (\n", "            f\"You just saw {NUM_FRAMES} frames from a video. Based on what you see, \"\n", "            f\"answer the following question: {question}.\\n\\n\"\n", "            f\"Here are your options:\\n{options_text}\\n\"\n", "            \"Choose the correct option in the format 'answer: X'. If uncertain, guess. You MUST pick something.\"\n", "        )\n", "    else:\n", "        prompt_text = (\n", "            f\"You just saw {NUM_FRAMES} frames from a video. \"\n", "            f\"Answer the following question: {question}.\\n\"\n", "            \"If uncertain, guess. Provide the best possible answer. You MUST answer to the best of your ability.\"\n", "        )\n", "\n", "    content_blocks.append({\"type\": \"text\", \"text\": prompt_text})\n", "\n", "    messages = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": (\n", "                        \"You are a helpful assistant. Provide an answer even if you are uncertain.\"\n", "                    ),\n", "                }\n", "            ],\n", "        },\n", "        {\"role\": \"user\", \"content\": content_blocks},\n", "    ]\n", "\n", "    response = client.chat.completions.create(model=\"gpt-4o\", messages=messages)\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the model's answers\n", "\n", "To evaluate the model's answers, we'll define a function called `evaluator` that uses the `LLMClassifier` from the [autoevals](https://github.com/braintrustdata/autoevals?tab=readme-ov-file#custom-evaluation-prompts) library as a starting point. This scorer compares the model's output with the expected answer, assigning 1 if they match and 0 otherwise."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["evaluator = autoevals.LLMClassifier(\n", "    name=\"evaluator\",\n", "    prompt_template=(\n", "        \"You are a judge evaluating a model's ability to answer a question \"\n", "        f\"based on {NUM_FRAMES} frames in a video.\\n\\n\"\n", "        \"Model's answer:\\n{{output}}\\n\\n\"\n", "        \"Expected answer:\\n{{expected.answer}}\\n\\n\"\n", "        \"Is the model's answer correct? (Y/N)? Only Y or N.\"\n", "    ),\n", "    choice_scores={\"Y\": 1, \"N\": 0},\n", "    use_cot=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running the evaluation\n", "\n", "Now that we have the three required components (a dataset, task, and prompt), we can run the eval. It loads data using`load_data_subset`, uses `video_qa` to get answers from the LLM, and scores each response with `evaluator`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["await braintrust.<PERSON>l<PERSON>(\n", "    \"mmvu_eval_32images\",\n", "    data=load_data_subset,\n", "    task=video_qa,\n", "    scores=[evaluator],\n", "    metadata={\"model\": \"gpt-4o\"},\n", "    experiment_name=\"mmvu_eval_32images\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing results\n", "\n", "After running the evaluation, head over to **Evaluations** in the Braintrust UI to see your results. Select your most recent experiment to review the video frames included in the prompt, the model's answer for each sample, and the scoring by our LLM-based judge. We also attached metadata like `subject` and `question_type`, which you can use to filter in the Braintrust UI. This makes it easy to see whether the model underperforms on a certain type of question or domain. If you discover specific weaknesses, consider refining your prompt with more context or switching models.\n", "\n", "![Filtering](./assets/filters.gif)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "* Learn more about the [MMVU dataset](https://mmvu-benchmark.github.io/)\n", "* Add [custom scorers](/docs/guides/functions/scorers#custom-scorers) to get more granular feedback (like partial credit, or domain-specific checks)\n", "* Check out our [prompt chaining agents cookbook](/docs/cookbook/recipes/PromptChaining) if you're building complex AI systems where video classification is just one component\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}