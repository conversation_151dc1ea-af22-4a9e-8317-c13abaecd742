{"name": "realtime", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@pinecone-database/pinecone": "^4.0.0", "dotenv": "^16.4.5", "leaflet": "^1.9.4", "lucide-react": "^0.464.0", "marked": "^15.0.3", "next": "14.2.14", "openai": "^4.76.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.1", "sass": "^1.78.0", "save": "^2.9.0", "web-vitals": "^2.1.4", "ws": "^8.18.0", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/leaflet": "^1.9.12", "@types/node": "^16.18.108", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "eslint": "^8", "eslint-config-next": "14.2.14", "typescript": "^5"}}