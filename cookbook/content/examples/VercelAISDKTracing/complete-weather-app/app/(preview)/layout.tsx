import "./globals.css";
import { Metada<PERSON> } from "next";
import { Toaster } from "sonner";

export const metadata: Metadata = {
  metadataBase: new URL("https://ai-sdk-preview-roundtrips.vercel.app"),
  title: "Vercel AI SDK Tracing with Braintrust",
  description: "A simple chatbot app which shows how to send traces from the Vercel AI SDK to Braintrust",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Toaster position="top-center" richColors />
        {children}
      </body>
    </html>
  );
}
