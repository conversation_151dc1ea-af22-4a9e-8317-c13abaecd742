{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generating beautiful HTML components\n", "\n", "In this example, we'll build an app that automatically generates HTML components, evaluates them, and captures user feedback. We'll use the feedback and evaluations to build up a dataset\n", "that we'll use as a basis for further improvements.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The generator\n", "\n", "We'll start by using a very simple prompt to generate HTML components using `gpt-3.5-turbo`.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we'll initialize an openai client and wrap it with Braintrust's helper. This is a no-op until we start using\n", "the client within code that is instrumented by Braintrust.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import { OpenAI } from \"openai\";\n", "import { wrapOpenAI } from \"braintrust\";\n", "\n", "const openai = wrapOpenAI(\n", "  new OpenAI({\n", "    apiKey: process.env.OPENAI_API_KEY || \"Your OPENAI_API_KEY\",\n", "  })\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This code generates a basic prompt:\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"role\": \"system\",\n", "    \"content\": \"You are a skilled design engineer\\nwho can convert ambiguously worded ideas into beautiful, crisp HTML and CSS.\\nYour designs value simplicity, conciseness, clarity, and functionality over\\ncomplexity.\\n\\nYou generate pure HTML with inline CSS, so that your designs can be rendered\\ndirectly as plain HTML. Only generate components, not full HTML pages. Do not\\ncreate background colors.\\n\\nUsers will send you a description of a design, and you must reply with HTML,\\nand nothing else. Your reply will be directly copied and rendered into a browser,\\nso do not include any text. If you would like to explain your reasoning, feel free\\nto do so in HTML comments.\"\n", "  },\n", "  {\n", "    \"role\": \"user\",\n", "    \"content\": \"A login form for a B2B SaaS product.\"\n", "  }\n", "]\n"]}], "source": ["import { ChatCompletionMessageParam } from \"openai/resources\";\n", "\n", "function generateMessages(input: string): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `You are a skilled design engineer\n", "who can convert ambiguously worded ideas into beautiful, crisp HTML and CSS.\n", "Your designs value simplicity, conciseness, clarity, and functionality over\n", "complexity.\n", "\n", "You generate pure HTML with inline CSS, so that your designs can be rendered\n", "directly as plain HTML. Only generate components, not full HTML pages. Do not\n", "create background colors.\n", "\n", "Users will send you a description of a design, and you must reply with HTML,\n", "and nothing else. Your reply will be directly copied and rendered into a browser,\n", "so do not include any text. If you would like to explain your reasoning, feel free\n", "to do so in HTML comments.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content: input,\n", "    },\n", "  ];\n", "}\n", "\n", "JSON.stringify(\n", "  generateMessages(\"A login form for a B2B SaaS product.\"),\n", "  null,\n", "  2\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's run this using `gpt-3.5-turbo`. We'll also do a few things that help us log & evaluate this function later:\n", "\n", "- Wrap the execution in a `traced` call, which will enable Braintrust to log the inputs and outputs of the function when we run it in production or in evals\n", "- Make its signature accept a single `input` value, which Braintrust's `Eval` function expects\n", "- Use a `seed` so that this test is reproduceable\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import { traced } from \"braintrust\";\n", "async function generateComponent(input: string) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await openai.chat.completions.create({\n", "        model: \"gpt-3.5-turbo\",\n", "        messages: generateMessages(input),\n", "        seed: 101,\n", "      });\n", "      const output = response.choices[0].message.content;\n", "      span.log({ input, output });\n", "      return output;\n", "    },\n", "    {\n", "      name: \"generateComponent\",\n", "    }\n", "  );\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Examples\n", "\n", "Let's look at a few examples!\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<div style=\"display: flex; justify-content: center; align-items: center; height: 100vh;\">\n", "  <div style=\"width: 300px; padding: 20px; border: 1px solid #ccc; border-radius: 5px;\">\n", "    <h2 style=\"text-align: center;\">Reset Password</h2>\n", "    <form style=\"display: flex; flex-direction: column;\">\n", "      <label for=\"email\">Email:</label>\n", "      <input type=\"email\" id=\"email\" name=\"email\" placeholder=\"Enter your email\" style=\"margin-bottom: 10px; padding: 5px;\">\n", "      <button type=\"submit\" style=\"background-color: #4CAF50; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;\">Reset Password</button>\n", "    </form>\n", "  </div>\n", "</div>\n"]}], "source": ["await generateComponent(\"Do a reset password form inside a card.\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To make this easier to validate, we'll use [puppeteer](https://pptr.dev/) to render the HTML as a screenshot.\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<div style=\"display: flex; justify-content: center; align-items: center; height: 100vh;\">\n", "  <div style=\"width: 300px; padding: 20px; border: 1px solid #ccc; border-radius: 5px;\">\n", "    <h2 style=\"text-align: center;\">Reset Password</h2>\n", "    <form style=\"display: flex; flex-direction: column;\">\n", "      <label for=\"email\">Email:</label>\n", "      <input type=\"email\" id=\"email\" name=\"email\" placeholder=\"Enter your email\" style=\"margin-bottom: 10px; padding: 5px;\">\n", "      <button type=\"submit\" style=\"background-color: #4CAF50; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;\">Reset Password</button>\n", "    </form>\n", "  </div>\n", "</div>\n"]}], "source": ["import puppeteer from \"puppeteer\";\n", "import * as tslab from \"tslab\";\n", "\n", "async function takeFullPageScreenshotAsUInt8Array(htmlContent) {\n", "  const browser = await puppeteer.launch({ headless: \"new\" });\n", "  const page = await browser.newPage();\n", "  await page.setContent(htmlContent);\n", "\n", "  const screenshotBuffer = await page.screenshot();\n", "  const uint8Array = new Uint8Array(screenshotBuffer);\n", "\n", "  await browser.close();\n", "  return uint8Array;\n", "}\n", "\n", "async function displayComponent(input: string) {\n", "  const html = await generateComponent(input);\n", "  const img = await takeFullPageScreenshotAsUInt8Array(html);\n", "  tslab.display.png(img);\n", "  console.log(html);\n", "}\n", "\n", "await displayComponent(\"Do a reset password form inside a card.\");\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<!DOCTYPE html>\n", "<html>\n", "\n", "<head>\n", "    <style>\n", "        .profile {\n", "            display: flex;\n", "            flex-direction: column;\n", "            align-items: center;\n", "        }\n", "\n", "        .profile-img {\n", "            width: 150px;\n", "            height: 150px;\n", "            border-radius: 50%;\n", "            margin-bottom: 20px;\n", "        }\n", "\n", "        .profile-name {\n", "            font-size: 24px;\n", "            font-weight: bold;\n", "            margin-bottom: 10px;\n", "        }\n", "\n", "        .profile-bio {\n", "            font-size: 18px;\n", "            text-align: center;\n", "        }\n", "\n", "        .profile-stats {\n", "            display: flex;\n", "            justify-content: space-between;\n", "            width: 200px;\n", "            margin-top: 20px;\n", "        }\n", "\n", "        .profile-stats-item {\n", "            display: flex;\n", "            flex-direction: column;\n", "            align-items: center;\n", "        }\n", "\n", "        .profile-stats-item-value {\n", "            font-size: 20px;\n", "            font-weight: bold;\n", "            margin-bottom: 5px;\n", "        }\n", "\n", "        .profile-stats-item-label {\n", "            font-size: 16px;\n", "        }\n", "    </style>\n", "</head>\n", "\n", "<body>\n", "    <div class=\"profile\">\n", "        <img class=\"profile-img\" src=\"profile-picture.jpg\" alt=\"Profile Picture\">\n", "        <div class=\"profile-name\"><PERSON></div>\n", "        <div class=\"profile-bio\">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla ut turpis\n", "            hend<PERSON><PERSON>, ullam<PERSON>per velit in, iaculis arcu.</div>\n", "        <div class=\"profile-stats\">\n", "            <div class=\"profile-stats-item\">\n", "                <div class=\"profile-stats-item-value\">500</div>\n", "                <div class=\"profile-stats-item-label\">Followers</div>\n", "            </div>\n", "            <div class=\"profile-stats-item\">\n", "                <div class=\"profile-stats-item-value\">250</div>\n", "                <div class=\"profile-stats-item-label\">Following</div>\n", "            </div>\n", "            <div class=\"profile-stats-item\">\n", "                <div class=\"profile-stats-item-value\">1000</div>\n", "                <div class=\"profile-stats-item-label\">Posts</div>\n", "            </div>\n", "        </div>\n", "    </div>\n", "</body>\n", "\n", "</html>\n"]}], "source": ["await displayComponent(\"Create a profile page for a social network.\");\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "<style>\n", "    /* Overall styling */\n", "    body {\n", "        font-family: <PERSON><PERSON>, sans-serif;\n", "        color: #fff;\n", "        background-color: #000;\n", "    }\n", "\n", "    /* Header styling */\n", "    .header {\n", "        background-color: #333;\n", "        padding: 20px;\n", "        text-align: center;\n", "    }\n", "\n", "    .header h1 {\n", "        margin: 0;\n", "        font-size: 24px;\n", "    }\n", "\n", "    /* Logs viewer styling */\n", "    .logs-viewer {\n", "        padding: 20px;\n", "    }\n", "\n", "    .log-entry {\n", "        margin-bottom: 10px;\n", "    }\n", "\n", "    .log-entry .timestamp {\n", "        color: #ccc;\n", "        font-size: 14px;\n", "        margin-right: 10px;\n", "    }\n", "\n", "    .log-entry .message {\n", "        font-size: 16px;\n", "    }\n", "</style>\n", "</head>\n", "<body>\n", "    <!-- Header -->\n", "    <div class=\"header\">\n", "        <h1>Logs Viewer</h1>\n", "    </div>\n", "    \n", "    <!-- Logs Viewer -->\n", "    <div class=\"logs-viewer\">\n", "        <div class=\"log-entry\">\n", "            <span class=\"timestamp\">12:30 PM</span>\n", "            <span class=\"message\">Info: Cloud instance created successfully</span>\n", "        </div>\n", "        <div class=\"log-entry\">\n", "            <span class=\"timestamp\">12:45 PM</span>\n", "            <span class=\"message\">Warning: High CPU utilization on instance #123</span>\n", "        </div>\n", "        <div class=\"log-entry\">\n", "            <span class=\"timestamp\">01:00 PM</span>\n", "            <span class=\"message\">Error: Connection lost to the database server</span>\n", "        </div>\n", "        <!-- Add more log entries here -->\n", "    </div>\n", "</body>\n", "</html>\n"]}], "source": ["await displayComponent(\n", "  \"Logs viewer for a cloud infrastructure management tool. Heavy use of dark mode.\"\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scoring the results\n", "\n", "It looks like in a few of these examples, the model is generating a full HTML page, instead of a component as we requested. This is something we can evaluate, to ensure that it does not happen!\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mtrue\u001b[39m\n"]}], "source": ["const containsHTML = (s) => /<(html|body)>/i.test(s);\n", "containsHTML(\n", "  await generateComponent(\n", "    \"Logs viewer for a cloud infrastructure management tool. Heavy use of dark mode.\"\n", "  )\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's update our function to compute this score. Let's also keep track of requests and their ids, so that we can provide user feedback. Normally you would store these in a database, but for demo purposes, a global dictionary should suffice.\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["// Normally you would store these in a database, but for this demo we'll just use a global variable.\n", "let requests = {};\n", "\n", "async function generateComponent(input: string) {\n", "  return traced(\n", "    async (span) => {\n", "      const response = await openai.chat.completions.create({\n", "        model: \"gpt-3.5-turbo\",\n", "        messages: generateMessages(input),\n", "        seed: 101,\n", "      });\n", "      const output = response.choices[0].message.content;\n", "      requests[input] = span.id;\n", "      span.log({\n", "        input,\n", "        output,\n", "        scores: { isComponent: containsHTML(output) ? 0 : 1 },\n", "      });\n", "      return output;\n", "    },\n", "    {\n", "      name: \"generateComponent\",\n", "    }\n", "  );\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging results\n", "\n", "To enable logging to Braintrust, we just need to initialize a logger. By default, a logger is automatically marked as the current, global logger, and once initialized will be picked up by `traced`.\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import { initLogger } from \"braintrust\";\n", "\n", "const logger = initLogger({\n", "  projectName: \"Component generator\",\n", "  apiKey: process.env.BRAINTRUST_API_KEY || \"Your BRAINTRUST_API_KEY\",\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we'll run the `generateComponent` function on a few examples, and see what the results look like in Braintrust.\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Logged 3 requests to Braintrust.\n"]}], "source": ["const inputs = [\n", "  \"A login form for a B2B SaaS product.\",\n", "  \"Create a profile page for a social network.\",\n", "  \"Logs viewer for a cloud infrastructure management tool. Heavy use of dark mode.\",\n", "];\n", "\n", "for (const input of inputs) {\n", "  await generateComponent(input);\n", "}\n", "\n", "console.log(`Logged ${inputs.length} requests to Braintrust.`);\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Viewing the logs in Braintrust\n", "\n", "Once this runs, you should be able to see the raw inputs and outputs, along with their scores in the project.\n", "\n", "![component_generator_logs.png](./assets/component-generator-logs.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Capturing user feedback\n", "\n", "Let's also track user ratings for these components. Separate from whether or not they're formatted as HTML, it'll be useful to track whether users like the design.\n", "\n", "To do this, [configure a new score in the project](https://www.braintrust.dev/docs/guides/human-review#configuring-human-review). Let's call it \"User preference\" and make it a 👍/👎.\n", "\n", "![Score configuration](./assets/score-config.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once you create a human review score, you can evaluate results directly in the Braintrust UI, or capture end-user feedback. Here, we'll pretend to capture end-user feedback. Personally, I liked the login form and logs viewer, but not the profile page. Let's record feedback accordingly.\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["// Along with scores, you can optionally log user feedback as comments, for additional color.\n", "logger.logFeedback({\n", "  id: requests[\"A login form for a B2B SaaS product.\"],\n", "  scores: { \"User preference\": 1 },\n", "  comment: \"Clean, simple\",\n", "});\n", "logger.logFeedback({\n", "  id: requests[\"Create a profile page for a social network.\"],\n", "  scores: { \"User preference\": 0 },\n", "});\n", "logger.logFeedback({\n", "  id: requests[\n", "    \"Logs viewer for a cloud infrastructure management tool. Heavy use of dark mode.\"\n", "  ],\n", "  scores: { \"User preference\": 1 },\n", "  comment:\n", "    \"No frills! Would have been nice to have borders around the entries.\",\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As users provide feedback, you'll see the updates they make in each log entry.\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["![Feedback log](./assets/feedback-comments.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating a dataset\n", "\n", "Now that we've collected some interesting examples from users, let's collect them into a dataset, and see if we can improve the `isComponent` score.\n", "\n", "In the Braintrust UI, select the examples, and add them to a new dataset called \"Interesting cases\".\n", "\n", "![Interesting cases](./assets/create-new-dataset.png)\n", "\n", "Once you create the dataset, it should look something like this:\n", "\n", "![Dataset](./assets/dataset.png)\n", "\n", "## Evaluating\n", "\n", "Now that we have a dataset, let's evaluate the `isComponent` function on it. We'll use the `Eval` function, which takes a dataset and a function, and evaluates the function on each example in the dataset.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import { Eval, initDataset } from \"braintrust\";\n", "\n", "await <PERSON><PERSON>(\"Component generator\", {\n", "  data: async () => {\n", "    const dataset = initDataset(\"Component generator\", {\n", "      dataset: \"Interesting cases\",\n", "    });\n", "    const records = [];\n", "    for await (const { input } of dataset.fetch()) {\n", "      records.push({ input });\n", "    }\n", "    return records;\n", "  },\n", "  task: generateComponent,\n", "  // We do not need to add any additional scores, because our\n", "  // generateComponent() function already computes `isComponent`\n", "  scores: [],\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once the eval runs, you'll see a summary which includes a link to the experiment. As expected, only one of the three outputs contains HTML, so the score is 33.3%. Let's also label user preference for this experiment, so we can track aesthetic taste manually. For simplicity's sake, we'll use the same labeling as before.\n", "\n", "![Initial experiment](./assets/initial-experiment.png)\n", "\n", "### Improving the prompt\n", "\n", "Next, let's try to tweak the prompt to stop rendering full HTML pages.\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    \"role\": \"system\",\n", "    \"content\": \"You are a skilled design engineer\\nwho can convert ambiguously worded ideas into beautiful, crisp HTML and CSS.\\nYour designs value simplicity, conciseness, clarity, and functionality over\\ncomplexity.\\n\\nYou generate pure HTML with inline CSS, so that your designs can be rendered\\ndirectly as plain HTML. Only generate components, not full HTML pages. If you\\nneed to add CSS, you can use the \\\"style\\\" property of an HTML tag. You cannot use\\nglobal CSS in a <style> tag.\\n\\nUsers will send you a description of a design, and you must reply with HTML,\\nand nothing else. Your reply will be directly copied and rendered into a browser,\\nso do not include any text. If you would like to explain your reasoning, feel free\\nto do so in HTML comments.\"\n", "  },\n", "  {\n", "    \"role\": \"user\",\n", "    \"content\": \"A login form for a B2B SaaS product.\"\n", "  }\n", "]\n"]}], "source": ["function generateMessages(input: string): ChatCompletionMessageParam[] {\n", "  return [\n", "    {\n", "      role: \"system\",\n", "      content: `You are a skilled design engineer\n", "who can convert ambiguously worded ideas into beautiful, crisp HTML and CSS.\n", "Your designs value simplicity, conciseness, clarity, and functionality over\n", "complexity.\n", "\n", "You generate pure HTML with inline CSS, so that your designs can be rendered\n", "directly as plain HTML. Only generate components, not full HTML pages. If you\n", "need to add CSS, you can use the \"style\" property of an HTML tag. You cannot use\n", "global CSS in a <style> tag.\n", "\n", "Users will send you a description of a design, and you must reply with HTML,\n", "and nothing else. Your reply will be directly copied and rendered into a browser,\n", "so do not include any text. If you would like to explain your reasoning, feel free\n", "to do so in HTML comments.`,\n", "    },\n", "    {\n", "      role: \"user\",\n", "      content: input,\n", "    },\n", "  ];\n", "}\n", "\n", "JSON.stringify(\n", "  generateMessages(\"A login form for a B2B SaaS product.\"),\n", "  null,\n", "  2\n", ");\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "<div>\n", "  <div style=\"background-color: #252525; color: #FFFFFF; padding: 10px;\">\n", "    <h1 style=\"margin: 0;\">Logs Viewer</h1>\n", "  </div>\n", "  <div style=\"background-color: #343434; color: #FFFFFF; padding: 10px;\">\n", "    <pre style=\"margin: 0;\">[Timestamp] [Service Name] [Log Level] [Message]</pre>\n", "    <pre style=\"margin: 0;\">[Timestamp] [Service Name] [Log Level] [Message]</pre>\n", "    <pre style=\"margin: 0;\">[Timestamp] [Service Name] [Log Level] [Message]</pre>\n", "    <!-- Repeat as needed for more logs -->\n", "  </div>\n", "</div>\n"]}], "source": ["await displayComponent(\n", "  \"Logs viewer for a cloud infrastructure management tool. Heavy use of dark mode.\"\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Nice, it looks like it no longer generates an `html` tag. Let's re-run the `Eval` (copy/pasted below for convenience).\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import { Eval, initDataset } from \"braintrust\";\n", "\n", "await <PERSON><PERSON>(\"Component generator\", {\n", "  data: async () => {\n", "    const dataset = initDataset(\"Component generator\", {\n", "      dataset: \"Interesting cases\",\n", "    });\n", "    const records = [];\n", "    for await (const { input } of dataset.fetch()) {\n", "      records.push({ input });\n", "    }\n", "    return records;\n", "  },\n", "  task: generateComponent,\n", "  scores: [], // We do not need to add any additional scores, because our generateComponent() function already computes isComponent\n", "});\n", "console.log(\"Done!\");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Nice! We are now generating components without the `<html>` tag.\n", "\n", "![Next experiment](./assets/next-experiment.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Where to go from here\n", "\n", "Now that we've run another experiment, a good next step would be to rate the new components and make sure we did not suffer a serious aesthetic regression. You can also collect more user examples, add them to the dataset, and re-evaluate to better assess how well your application works. Happy evaluating!\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}