{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AI Search Evals\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This guide demonstrates how we developed Braintrust's AI-powered search bar, harnessing the power of Braintrust's evaluation workflow along the way. If you've used Braintrust before, you may be familiar with the project page, which serves as a home base for collections of eval experiments:\n", "\n", "![Braintrust Project Page](./assets/project-page-sql.png)\n", "\n", "To find a particular experiment, you can type filter and sort queries into the search bar, using standard SQL syntax. But SQL can be finicky -- it's very easy to run into syntax errors like single quotes instead of double, incorrect JSON extraction syntax, or typos. Users would prefer to just type in an intuitive search like `experiments run on git commit 2a43fd1` or `score under 0.5` and see a corresponding SQL query appear automatically. Let's achieve this using AI, with assistance from Braintrust's eval framework.\n", "\n", "We'll start by installing some packages and setting up our OpenAI client.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install -U Levenshtein autoevals braintrust chevron duckdb openai pydantic"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "import braintrust\n", "import openai\n", "\n", "PROJECT_NAME = \"AI Search Cookbook\"\n", "\n", "# We use the Braintrust proxy here to get access to caching, but this is totally optional!\n", "openai_opts = dict(\n", "    base_url=\"https://api.braintrust.dev/v1/proxy\",\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\", \"YOUR_OPENAI_API_KEY\"),\n", ")\n", "client = braintrust.wrap_openai(openai.AsyncOpenAI(default_headers={\"x-bt-use-cache\": \"always\"}, **openai_opts))\n", "\n", "braintrust.login(api_key=os.environ.get(\"BRAINTRUST_API_KEY\", \"YOUR_BRAINTRUST_API_KEY\"))\n", "dataset = braintrust.init_dataset(PROJECT_NAME, \"AI Search Cookbook Data\", use_output=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the data and render the templates\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When we ask GPT to translate a search query, we have to account for multiple output options: (1) a SQL filter, (2) a SQL sort, (3) both of the above, or (4) an unsuccessful translation (e.g. for a nonsensical user input). We'll use [function calling](https://platform.openai.com/docs/guides/function-calling) to robustly handle each distinct scenario, with the following output format:\n", "\n", "- `match`: Whether or not the model was able to translate the search into a valid SQL filter/sort.\n", "- `filter`: A `WHERE` clause.\n", "- `sort`: An `ORDER BY` clause.\n", "- `explanation`: Explanation for the choices above -- this is useful for debugging and evaluation.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from typing import Literal, Optional, Union\n", "\n", "from pydantic import BaseModel, Field, create_model\n", "\n", "\n", "@dataclasses.dataclass\n", "class FunctionCallOutput:\n", "    match: Optional[bool] = None\n", "    filter: Optional[str] = None\n", "    sort: Optional[str] = None\n", "    explanation: Optional[str] = None\n", "    error: Optional[str] = None\n", "\n", "\n", "class Match(BaseModel):\n", "    type: Literal[\"MATCH\"] = \"MATCH\"\n", "    explanation: str = Field(\n", "        ..., description=\"Explanation of why I called the MATCH function\"\n", "    )\n", "\n", "\n", "class SQL(BaseModel):\n", "    type: Literal[\"SQL\"] = \"SQL\"\n", "    filter: Optional[str] = Field(..., description=\"SQL filter clause\")\n", "    sort: Optional[str] = Field(..., description=\"SQL sort clause\")\n", "    explanation: str = Field(\n", "        ...,\n", "        description=\"Explanation of why I called the SQL function and how I chose the filter and/or sort clauses\",\n", "    )\n", "\n", "\n", "class Query(BaseModel):\n", "    value: Union[Match, SQL] = Field(\n", "        ...,\n", "    )\n", "\n", "\n", "def function_choices():\n", "    return [\n", "        {\n", "            \"name\": \"QUERY\",\n", "            \"description\": \"Break down the query either into a MATCH or SQL call\",\n", "            \"parameters\": Query.model_json_schema(),\n", "        },\n", "    ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare prompts for evaluation in Braintrust\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's evaluate two different prompts: a shorter prompt with a brief explanation of the problem statement and description of the experiment schema, and a longer prompt that additionally contains a feed of example cases to guide the model. There's nothing special about either of these prompts, and that's OK -- we can iterate and improve the prompts when we use Braintrust to drill down into the results.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "SHORT_PROMPT_FILE = \"./assets/short_prompt.tmpl\"\n", "LONG_PROMPT_FILE = \"./assets/long_prompt.tmpl\"\n", "FEW_SHOT_EXAMPLES_FILE = \"./assets/few_shot.json\"\n", "\n", "with open(SHORT_PROMPT_FILE) as f:\n", "    short_prompt = f.read()\n", "\n", "with open(LONG_PROMPT_FILE) as f:\n", "    long_prompt = f.read()\n", "\n", "with open(FEW_SHOT_EXAMPLES_FILE, \"r\") as f:\n", "    few_shot_examples = json.load(f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["One detail worth mentioning: each prompt contains a stub for dynamic insertion of the data schema. This is motivated by the need to handle semantic searches like `more than 40 examples` or `score < 0.5` that don't directly reference a column in the base table. We need to tell the model how the data is structured and what each fields actually _means_. We'll construct a descriptive schema using [pydantic](https://docs.pydantic.dev/latest/) and paste it into each prompt to provide the model with this information.\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from typing import Any, Callable, Dict, List\n", "\n", "import chevron\n", "\n", "\n", "class ExperimentGitState(BaseModel):\n", "    commit: str = Field(\n", "        ...,\n", "        description=\"Git commit hash. Any prefix of this hash at least 7 characters long should be considered an exact match, so use a substring filter rather than string equality to check the commit, e.g. `(source->>'commit') ILIKE '{COMMIT}%'`\",\n", "    )\n", "    branch: str = Field(..., description=\"Git branch name\")\n", "    tag: Optional[str] = Field(..., description=\"Git commit tag\")\n", "    commit_time: int = Field(..., description=\"Git commit timestamp\")\n", "    author_name: str = Field(..., description=\"Author of git commit\")\n", "    author_email: str = Field(..., description=\"Email address of git commit author\")\n", "    commit_message: str = Field(..., description=\"Git commit message\")\n", "    dirty: Optional[bool] = Field(\n", "        ...,\n", "        description=\"Whether the git state was dirty when the experiment was run. If false, the git state was clean\",\n", "    )\n", "\n", "\n", "class Experiment(BaseModel):\n", "    id: str = Field(..., description=\"Experiment ID, unique\")\n", "    name: str = Field(..., description=\"Name of the experiment\")\n", "    last_updated: int = Field(\n", "        ...,\n", "        description=\"Timestamp marking when the experiment was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.\",\n", "    )\n", "    creator: Dict[str, str] = <PERSON>(..., description=\"Information about the experiment creator\")\n", "    source: ExperimentGitState = Field(..., description=\"Git state that the experiment was run on\")\n", "    metadata: Dict[str, Any] = Field(\n", "        ...,\n", "        description=\"Custom metadata provided by the user. Ignore this field unless the query mentions metadata or refers to a metadata key specifically\",\n", "    )\n", "\n", "\n", "def build_experiment_schema(score_fields: List[str]):\n", "    ExperimentWithScoreFields = create_model(\n", "        \"Experiment\",\n", "        __base__=Experiment,\n", "        **{field: (Optional[float], ...) for field in score_fields},\n", "    )\n", "    return json.dumps(ExperimentWithScoreFields.model_json_schema())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our prompts are ready! Before we run our evals, we just need to load some sample data and define our scoring functions.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load sample data\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's load our examples. Each example case contains `input` (the search query) and `expected` (function call output).\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "@dataclasses.dataclass\n", "class Example:\n", "    input: str\n", "    expected: FunctionCallOutput\n", "    metadata: Optional[Dict[str, Any]] = None\n", "\n", "\n", "EXAMPLES_FILE = \"./assets/examples.json\"\n", "with open(EXAMPLES_FILE) as f:\n", "    examples_json = json.load(f)\n", "\n", "templates = [\n", "    Example(input=e[\"input\"], expected=FunctionCallOutput(**e[\"expected\"])) for e in examples_json[\"examples\"]\n", "]\n", "\n", "# Each example contains a few dynamic fields that depends on the experiments\n", "# we're searching over. For simplicity, we'll hard-code these fields here.\n", "SCORE_FIELDS = [\"avg_sql_score\", \"avg_factuality_score\"]\n", "\n", "\n", "def render_example(example: Example, args: Dict[str, Any]) -> Example:\n", "    render_optional = lambda template: (chevron.render(template, args, warn=True) if template is not None else None)\n", "    return Example(\n", "        input=render_optional(example.input),\n", "        expected=FunctionCallOutput(\n", "            match=example.expected.match,\n", "            filter=render_optional(example.expected.filter),\n", "            sort=render_optional(example.expected.sort),\n", "            explanation=render_optional(example.expected.explanation),\n", "        ),\n", "    )\n", "\n", "\n", "examples = [render_example(t, {\"score_fields\": SCORE_FIELDS}) for t in templates]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's also split the examples into a training set and test set. For now, this won't matter, but later on when we fine-tune the model, we'll want to use the test set to evaluate the model's performance.\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["for i, e in enumerate(examples):\n", "    if i < 0.8 * len(examples):\n", "        e.metadata = {\"split\": \"train\"}\n", "    else:\n", "        e.metadata = {\"split\": \"test\"}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Insert our examples into a Braintrust dataset so we can introspect and reuse the data later.\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 45 records. Here are the first 2...\n", "{'id': '05e44f2c-da5c-4f5e-a253-d6ce1d081ca4', 'span_id': 'c2329825-10d3-462f-890b-ef54323f8060', 'root_span_id': 'c2329825-10d3-462f-890b-ef54323f8060', '_xact_id': '1000192628646491178', 'created': '2024-03-04T08:08:12.977238Z', 'project_id': '61ce386b-1dac-4027-980f-2f3baf32c9f4', 'dataset_id': 'cbb856d4-b2d9-41ea-a5a7-ba5b78be6959', 'input': 'name is foo', 'expected': {'sort': None, 'error': None, 'match': False, 'filter': \"name = 'foo'\", 'explanation': 'I interpret the query as a string equality filter on the \"name\" column. The query does not have any sort semantics, so there is no sort.'}, 'metadata': {'split': 'train'}, 'tags': None}\n", "{'id': '0d127613-505c-404c-8140-2c287313b682', 'span_id': '1e72c902-fe72-4438-adf4-19950f8a2c57', 'root_span_id': '1e72c902-fe72-4438-adf4-19950f8a2c57', '_xact_id': '1000192628646491178', 'created': '2024-03-04T08:08:12.981295Z', 'project_id': '61ce386b-1dac-4027-980f-2f3baf32c9f4', 'dataset_id': 'cbb856d4-b2d9-41ea-a5a7-ba5b78be6959', 'input': \"'highest score'\", 'expected': {'sort': None, 'error': None, 'match': True, 'filter': None, 'explanation': 'According to directive 2, a query entirely wrapped in quotes should use the MATCH function.'}, 'metadata': {'split': 'train'}, 'tags': None}\n"]}], "source": ["for example in examples:\n", "    dataset.insert(\n", "        input=example.input, expected=example.expected, metadata=example.metadata\n", "    )\n", "dataset.flush()\n", "\n", "records = list(dataset)\n", "print(f\"Generated {len(records)} records. Here are the first 2...\")\n", "for record in records[:2]:\n", "    print(record)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define scoring functions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["How do we score our outputs against the ground truth queries? We can't rely on an exact text match, since there are multiple correct ways to translate a SQL query. Instead, we'll use two approximate scoring methods: (1) `SQLScorer`, which roundtrips each query through `json_serialize_sql` to normalize before attempting a direct comparison, and (2) `AutoScorer`, which delegates the scoring task to `gpt-4`.\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import duckdb\n", "from braintrust import current_span, traced\n", "from Levenshtein import distance\n", "\n", "from autoevals import Score, Scorer, Sql\n", "\n", "EXPERIMENTS_TABLE = \"./assets/experiments.parquet\"\n", "SUMMARY_TABLE = \"./assets/experiments_summary.parquet\"\n", "duckdb.sql(f\"DROP TABLE IF EXISTS experiments; CREATE TABLE experiments AS SELECT * FROM '{EXPERIMENTS_TABLE}'\")\n", "duckdb.sql(\n", "    f\"DROP TABLE IF EXISTS experiments_summary; CREATE TABLE experiments_summary AS SELECT * FROM '{SUMMARY_TABLE}'\"\n", ")\n", "\n", "\n", "def _test_clause(*, filter=None, sort=None) -> bool:\n", "    clause = f\"\"\"\n", "        SELECT\n", "          experiments.id AS id,\n", "          experiments.name,\n", "          experiments_summary.last_updated,\n", "          experiments.user AS creator,\n", "          experiments.repo_info AS source,\n", "          experiments_summary.* EXCLUDE (experiment_id, last_updated),\n", "        FROM experiments\n", "        LEFT JOIN experiments_summary ON experiments.id = experiments_summary.experiment_id\n", "        {'WHERE ' + filter if filter else ''}\n", "        {'ORDER BY ' + sort if sort else ''}\n", "    \"\"\"\n", "    current_span().log(metadata=dict(test_clause=clause))\n", "    try:\n", "        duckdb.sql(clause).fetchall()\n", "        return True\n", "    except Exception:\n", "        return False\n", "\n", "\n", "def _single_quote(s):\n", "    return f\"\"\"'{s.replace(\"'\", \"''\")}'\"\"\"\n", "\n", "\n", "def _roundtrip_filter(s):\n", "    return duckdb.sql(\n", "        f\"\"\"\n", "        SELECT json_deserialize_sql(json_serialize_sql({_single_quote(f\"SELECT 1 WHERE {s}\")}))\n", "    \"\"\"\n", "    ).fetchall()[0][0]\n", "\n", "\n", "def _roundtrip_sort(s):\n", "    return duckdb.sql(\n", "        f\"\"\"\n", "        SELECT json_deserialize_sql(json_serialize_sql({_single_quote(f\"SELECT 1 ORDER BY {s}\")}))\n", "    \"\"\"\n", "    ).fetchall()[0][0]\n", "\n", "\n", "def score_clause(\n", "    output: Optional[str],\n", "    expected: Optional[str],\n", "    roundtrip: Callable[[str], str],\n", "    test_clause: Callable[[str], bool],\n", ") -> float:\n", "    exact_match = 1 if output == expected else 0\n", "    current_span().log(scores=dict(exact_match=exact_match))\n", "    if exact_match:\n", "        return 1\n", "\n", "    roundtrip_match = 0\n", "    try:\n", "        if roundtrip(output) == roundtrip(expected):\n", "            roundtrip_match = 1\n", "    except Exception as e:\n", "        current_span().log(metadata=dict(roundtrip_error=str(e)))\n", "        \n", "    current_span().log(scores=dict(roundtrip_match=roundtrip_match))\n", "    if roundtrip_match:\n", "        return 1\n", "\n", "    # If the queries aren't equivalent after roundtripping, it's not immediately clear\n", "    # whether they are semantically equivalent. Let's at least check that the generated\n", "    # clause is valid SQL by running the `test_clause` function defined above, which\n", "    # runs a test query against our sample data.\n", "    valid_clause_score = 1 if test_clause(output) else 0\n", "    current_span().log(scores=dict(valid_clause=valid_clause_score))\n", "    if valid_clause_score == 0:\n", "        return 0\n", "\n", "    max_len = max(len(clause) for clause in [output, expected])\n", "    if max_len == 0:\n", "        current_span().log(metadata=dict(error=\"Bad example: empty clause\"))\n", "        return 0\n", "    return 1 - (distance(output, expected) / max_len)\n", "\n", "\n", "class SQLScorer(Scorer):\n", "    \"\"\"SQLScorer uses DuckDB's `json_serialize_sql` function to determine whether\n", "    the model's chosen filter/sort clause(s) are equivalent to the expected\n", "    outputs. If not, we assign partial credit to each clause depending on\n", "    (1) whether the clause is valid SQL, as determined by running it against\n", "    the actual data and seeing if it errors, and (2) a distance-wise comparison\n", "    to the expected text.\n", "    \"\"\"\n", "\n", "    def _run_eval_sync(\n", "        self,\n", "        output,\n", "        expected=None,\n", "        **kwargs,\n", "    ):\n", "        if expected is None:\n", "            raise ValueError(\"SQLScorer requires an expected value\")\n", "\n", "        name = \"SQLScorer\"\n", "        expected = FunctionCallOutput(**expected)\n", "\n", "        function_choice_score = 1 if output.match == expected.match else 0\n", "        current_span().log(scores=dict(function_choice=function_choice_score))\n", "        if function_choice_score == 0:\n", "            return Score(name=name, score=0)\n", "        if expected.match:\n", "            return Score(name=name, score=1)\n", "\n", "        filter_score = None\n", "        if output.filter and expected.filter:\n", "            with current_span().start_span(\"SimpleFilter\") as span:\n", "                filter_score = score_clause(\n", "                    output.filter,\n", "                    expected.filter,\n", "                    _roundtrip_filter,\n", "                    lambda s: _test_clause(filter=s),\n", "                )\n", "        elif output.filter or expected.filter:\n", "            filter_score = 0\n", "        current_span().log(scores=dict(filter=filter_score))\n", "\n", "        sort_score = None\n", "        if output.sort and expected.sort:\n", "            with current_span().start_span(\"SimpleSort\") as span:\n", "                sort_score = score_clause(\n", "                    output.sort,\n", "                    expected.sort,\n", "                    _roundtrip_sort,\n", "                    lambda s: _test_clause(sort=s),\n", "                )\n", "        elif output.sort or expected.sort:\n", "            sort_score = 0\n", "        current_span().log(scores=dict(sort=sort_score))\n", "\n", "        scores = [s for s in [filter_score, sort_score] if s is not None]\n", "        if len(scores) == 0:\n", "            return Score(\n", "                name=name,\n", "                score=0,\n", "                error=\"Bad example: no filter or sort for SQL function call\",\n", "            )\n", "        return Score(name=name, score=sum(scores) / len(scores))\n", "\n", "\n", "@traced(\"auto_score_filter\")\n", "def auto_score_filter(openai_opts, **kwargs):\n", "    return Sql(**openai_opts)(**kwargs)\n", "\n", "\n", "@traced(\"auto_score_sort\")\n", "def auto_score_sort(openai_opts, **kwargs):\n", "    return Sql(**openai_opts)(**kwargs)\n", "\n", "\n", "class AutoScorer(Scorer):\n", "    \"\"\"AutoScorer uses the `Sql` scorer from the autoevals library to auto-score\n", "    the model's chosen filter/sort clause(s) against the expected outputs\n", "    using an LLM.\n", "    \"\"\"\n", "\n", "    def __init__(self, **openai_opts):\n", "        self.openai_opts = openai_opts\n", "\n", "    def _run_eval_sync(\n", "        self,\n", "        output,\n", "        expected=None,\n", "        **kwargs,\n", "    ):\n", "        if expected is None:\n", "            raise ValueError(\"AutoScorer requires an expected value\")\n", "        input = kwargs.get(\"input\")\n", "        if input is None or not isinstance(input, str):\n", "            raise ValueError(\"AutoScorer requires an input value of type str\")\n", "\n", "        name = \"AutoScorer\"\n", "        expected = FunctionCallOutput(**expected)\n", "\n", "        function_choice_score = 1 if output.match == expected.match else 0\n", "        current_span().log(scores=dict(function_choice=function_choice_score))\n", "        if function_choice_score == 0:\n", "            return Score(name=name, score=0)\n", "        if expected.match:\n", "            return Score(name=name, score=1)\n", "\n", "        filter_score = None\n", "        if output.filter and expected.filter:\n", "            result = auto_score_filter(\n", "                openai_opts=self.openai_opts,\n", "                input=input,\n", "                output=output.filter,\n", "                expected=expected.filter,\n", "            )\n", "            filter_score = result.score or 0\n", "        elif output.filter or expected.filter:\n", "            filter_score = 0\n", "        current_span().log(scores=dict(filter=filter_score))\n", "\n", "        sort_score = None\n", "        if output.sort and expected.sort:\n", "            result = auto_score_sort(\n", "                openai_opts=self.openai_opts,\n", "                input=input,\n", "                output=output.sort,\n", "                expected=expected.sort,\n", "            )\n", "            sort_score = result.score or 0\n", "        elif output.sort or expected.sort:\n", "            sort_score = 0\n", "        current_span().log(scores=dict(sort=sort_score))\n", "\n", "        scores = [s for s in [filter_score, sort_score] if s is not None]\n", "        if len(scores) == 0:\n", "            return Score(\n", "                name=name,\n", "                score=0,\n", "                error=\"Bad example: no filter or sort for SQL function call\",\n", "            )\n", "        return Score(name=name, score=sum(scores) / len(scores))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run the evals!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We'll use the Braintrust `Eval` framework to set up our experiments according to the prompts, dataset, and scoring functions defined above.\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def build_completion_kwargs(\n", "    *,\n", "    query: str,\n", "    model: str,\n", "    prompt: str,\n", "    score_fields: List[str],\n", "    **kwargs,\n", "):\n", "    # Inject the JSON schema into the prompt to assist the model.\n", "    schema = build_experiment_schema(score_fields=score_fields)\n", "    system_message = chevron.render(\n", "        prompt.strip(), {\"schema\": schema, \"examples\": few_shot_examples}, warn=True\n", "    )\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": f\"Query: {query}\"},\n", "    ]\n", "\n", "    # We use the legacy function choices format for now, because fine-tuning still requires it.\n", "    return dict(\n", "        model=model,\n", "        temperature=0,\n", "        messages=messages,\n", "        functions=function_choices(),\n", "        function_call={\"name\": \"QUERY\"},\n", "    )\n", "\n", "\n", "def format_output(completion):\n", "    try:\n", "        function_call = completion.choices[0].message.function_call\n", "        arguments = json.loads(function_call.arguments)[\"value\"]\n", "        match = arguments.pop(\"type\").lower() == \"match\"\n", "        return FunctionCallOutput(match=match, **arguments)\n", "    except Exception as e:\n", "        return FunctionCallOutput(error=str(e))\n", "\n", "\n", "GRADER = \"gpt-4\"  # Used by AutoScorer to grade the model outputs\n", "\n", "\n", "def make_task(model, prompt, score_fields):\n", "    async def task(input):\n", "        completion_kwargs = build_completion_kwargs(\n", "            query=input,\n", "            model=model,\n", "            prompt=prompt,\n", "            score_fields=score_fields,\n", "        )\n", "        return format_output(await client.chat.completions.create(**completion_kwargs))\n", "\n", "    return task\n", "\n", "\n", "async def run_eval(experiment_name, prompt, model, score_fields=SCORE_FIELDS):\n", "    task = make_task(model, prompt, score_fields)\n", "    await braintrust.<PERSON>(\n", "        name=PROJECT_NAME,\n", "        experiment_name=experiment_name,\n", "        data=dataset,\n", "        task=task,\n", "        scores=[SQLScorer(), AutoScorer(**openai_opts, model=GRADER)],\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's try it on one example before running an eval.\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["FunctionCallOutput(match=False, filter=\"(name) = 'foo'\", sort=None, explanation=\"Filtered for experiments where the name is 'foo'.\", error=None)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["args = build_completion_kwargs(\n", "    query=list(dataset)[0][\"input\"],\n", "    model=\"gpt-3.5-turbo\",\n", "    prompt=short_prompt,\n", "    score_fields=SCORE_FIELDS,\n", ")\n", "response = await client.chat.completions.create(**args)\n", "format_output(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We're ready to run our evals! Let's use `gpt-3.5-turbo` for both.\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Short Prompt is running at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Short%20Prompt\n", "AI Search Cookbook [experiment_name=Short Prompt] (data): 45it [00:00, 73071.50it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "99bec878db934a2aaaa6012731991673", "version_major": 2, "version_minor": 0}, "text/plain": ["AI Search Cookbook [experiment_name=Short Prompt] (tasks):   0%|          | 0/45 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Short Prompt compared to Long Prompt 2.0:\n", "46.28% (-21.68%) 'SQLScorer'       score\t(10 improvements, 25 regressions)\n", "15.00% (-36.52%) 'exact_match'     score\t(2 improvements, 7 regressions)\n", "40.89% (-32.19%) 'sort'            score\t(0 improvements, 4 regressions)\n", "16.67% (+01.96%) 'roundtrip_match' score\t(2 improvements, 3 regressions)\n", "69.36% (-04.67%) 'filter'          score\t(6 improvements, 10 regressions)\n", "60.00% (-22.22%) 'function_choice' score\t(5 improvements, 15 regressions)\n", "70.00% (-16.67%) 'valid_clause'    score\t(1 improvements, 0 regressions)\n", "43.33% (-12.22%) 'AutoScorer'      score\t(9 improvements, 15 regressions)\n", "\n", "4.54s (-210.10%) 'duration'\t(28 improvements, 17 regressions)\n", "\n", "See results for Short Prompt at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Short%20Prompt\n"]}], "source": ["await run_eval(\"Short Prompt\", short_prompt, \"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Long Prompt is running at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Long%20Prompt\n", "AI Search Cookbook [experiment_name=Long Prompt] (data): 45it [00:00, 35385.02it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9e64d73e60064512b6cb32f97c748e0e", "version_major": 2, "version_minor": 0}, "text/plain": ["AI Search Cookbook [experiment_name=Long Prompt] (tasks):   0%|          | 0/45 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Long Prompt compared to Short Prompt:\n", "67.99% (+21.71%) 'SQLScorer'       score\t(21 improvements, 5 regressions)\n", "50.00% (+35.00%) 'exact_match'     score\t(6 improvements, 1 regressions)\n", "71.92% (+31.02%) 'sort'            score\t(3 improvements, 0 regressions)\n", "03.12% (-13.54%) 'roundtrip_match' score\t(1 improvements, 2 regressions)\n", "71.53% (+02.17%) 'filter'          score\t(10 improvements, 5 regressions)\n", "77.78% (+17.78%) 'function_choice' score\t(9 improvements, 1 regressions)\n", "84.38% (+14.38%) 'valid_clause'    score\t(1 improvements, 1 regressions)\n", "55.56% (+12.22%) 'AutoScorer'      score\t(9 improvements, 4 regressions)\n", "\n", "5.90s (+136.66%) 'duration'\t(11 improvements, 34 regressions)\n", "\n", "See results for Long Prompt at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Long%20Prompt\n"]}], "source": ["await run_eval(\"Long Prompt\", long_prompt, \"gpt-3.5-turbo\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## View the results in Braintrust\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The evals will generate a link to the experiment page. Click into an experiment to view the results!\n", "\n", "If you've just been following along, you can [check out some sample results here](). Type some searches into the search bar to see AI search in action. :)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Braintrust Project Page](./assets/project-page-sql.png)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fine-tuning\n", "\n", "Let's try to fine-tune the model with an exceedingly short prompt. We'll use the same dataset and scoring functions, but we'll change the prompt to be more concise. To start, let's play with one example:\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name is foo\n", "{\n", "  \"sort\": null,\n", "  \"error\": null,\n", "  \"match\": false,\n", "  \"filter\": \"name = 'foo'\",\n", "  \"explanation\": \"I interpret the query as a string equality filter on the \\\"name\\\" column. The query does not have any sort semantics, so there is no sort.\"\n", "}\n"]}], "source": ["first = list(dataset.fetch())[0]\n", "print(first[\"input\"])\n", "print(json.dumps(first[\"expected\"], indent=2))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["QUERY\n", "{'value': {'explanation': \"The query refers to the 'name' field in the \"\n", "                          \"'experiments' table, so I used ILIKE to check if \"\n", "                          \"the name contains 'foo'. I wrapped the filter in \"\n", "                          'parentheses and used ILIKE for case-insensitive '\n", "                          'matching.',\n", "           'filter': \"name ILIKE 'foo'\",\n", "           'sort': None,\n", "           'type': 'SQL'}}\n"]}], "source": ["from dataclasses import asdict\n", "from pprint import pprint\n", "\n", "long_prompt_args = build_completion_kwargs(\n", "    query=first[\"input\"],\n", "    model=\"gpt-3.5-turbo\",\n", "    prompt=long_prompt,\n", "    score_fields=SCORE_FIELDS,\n", ")\n", "output = await client.chat.completions.create(**long_prompt_args)\n", "function_call = output.choices[0].message.function_call\n", "print(function_call.name)\n", "pprint(json.loads(function_call.arguments))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Great! Now let's turn the output from the dataset into the tool call format that [OpenAI expects](https://platform.openai.com/docs/guides/fine-tuning/fine-tuning-examples).\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'QUERY',\n", " 'arguments': '{\"value\": {\"type\": \"SQL\", \"filter\": \"name = \\'foo\\'\", \"explanation\": \"I interpret the query as a string equality filter on the \\\\\"name\\\\\" column. The query does not have any sort semantics, so there is no sort.\"}}'}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["def transform_function_call(expected_value):\n", "    return {\n", "        \"name\": \"QUERY\",\n", "        \"arguments\": json.dumps(\n", "            {\n", "                \"value\": {\n", "                    \"type\": (\n", "                        expected_value.get(\"function\")\n", "                        if expected_value.get(\"function\")\n", "                        else \"MATCH\" if expected_value.get(\"match\") else \"SQL\"\n", "                    ),\n", "                    **{\n", "                        k: v\n", "                        for (k, v) in expected_value.items()\n", "                        if k in (\"filter\", \"sort\", \"explanation\") and v is not None\n", "                    },\n", "                }\n", "            }\n", "        ),\n", "    }\n", "\n", "\n", "transform_function_call(first[\"expected\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This function also works on our few shot examples:\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': 'QUERY',\n", " 'arguments': '{\"value\": {\"type\": \"SQL\", \"filter\": \"(metrics->>\\'accuracy\\')::NUMERIC < 0.2\", \"explanation\": \"The query refers to a JSON field, so I correct the JSON extraction syntax according to directive 4 and cast the result to NUMERIC to compare to the value `0.2` as per directive 9.\"}}'}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["transform_function_call(few_shot_examples[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since we're fine-tuning, we can also use a shorter prompt that just contains the object type (Experiment) and schema.\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["FINE_TUNING_PROMPT_FILE = \"./assets/fine_tune.tmpl\"\n", "\n", "with open(FINE_TUNING_PROMPT_FILE) as f:\n", "    fine_tune_prompt = f.read()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [{'role': 'system',\n", "   'content': 'Table: experiments\\n\\n<Schema>\\n{\"$defs\": {\"ExperimentGitState\": {\"properties\": {\"commit\": {\"description\": \"Git commit hash. Any prefix of this hash at least 7 characters long should be considered an exact match, so use a substring filter rather than string equality to check the commit, e.g. `(source->>\\'commit\\') ILIKE \\'{COMMIT}%\\'`\", \"title\": \"Commit\", \"type\": \"string\"}, \"branch\": {\"description\": \"Git branch name\", \"title\": \"Branch\", \"type\": \"string\"}, \"tag\": {\"anyOf\": [{\"type\": \"string\"}, {\"type\": \"null\"}], \"description\": \"Git commit tag\", \"title\": \"Tag\"}, \"commit_time\": {\"description\": \"Git commit timestamp\", \"title\": \"Commit Time\", \"type\": \"integer\"}, \"author_name\": {\"description\": \"Author of git commit\", \"title\": \"Author Name\", \"type\": \"string\"}, \"author_email\": {\"description\": \"Email address of git commit author\", \"title\": \"Author Email\", \"type\": \"string\"}, \"commit_message\": {\"description\": \"Git commit message\", \"title\": \"Commit Message\", \"type\": \"string\"}, \"dirty\": {\"anyOf\": [{\"type\": \"boolean\"}, {\"type\": \"null\"}], \"description\": \"Whether the git state was dirty when the experiment was run. If false, the git state was clean\", \"title\": \"Dirty\"}}, \"required\": [\"commit\", \"branch\", \"tag\", \"commit_time\", \"author_name\", \"author_email\", \"commit_message\", \"dirty\"], \"title\": \"ExperimentGitState\", \"type\": \"object\"}}, \"properties\": {\"id\": {\"description\": \"Experiment ID, unique\", \"title\": \"Id\", \"type\": \"string\"}, \"name\": {\"description\": \"Name of the experiment\", \"title\": \"Name\", \"type\": \"string\"}, \"last_updated\": {\"description\": \"Timestamp marking when the experiment was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.\", \"title\": \"Last Updated\", \"type\": \"integer\"}, \"creator\": {\"additionalProperties\": {\"type\": \"string\"}, \"description\": \"Information about the experiment creator\", \"title\": \"Creator\", \"type\": \"object\"}, \"source\": {\"allOf\": [{\"$ref\": \"#/$defs/ExperimentGitState\"}], \"description\": \"Git state that the experiment was run on\"}, \"metadata\": {\"description\": \"Custom metadata provided by the user. Ignore this field unless the query mentions metadata or refers to a metadata key specifically\", \"title\": \"Metadata\", \"type\": \"object\"}, \"avg_sql_score\": {\"anyOf\": [{\"type\": \"number\"}, {\"type\": \"null\"}], \"title\": \"Avg Sql Score\"}, \"avg_factuality_score\": {\"anyOf\": [{\"type\": \"number\"}, {\"type\": \"null\"}], \"title\": \"Avg Factuality Score\"}}, \"required\": [\"id\", \"name\", \"last_updated\", \"creator\", \"source\", \"metadata\", \"avg_sql_score\", \"avg_factuality_score\"], \"title\": \"Experiment\", \"type\": \"object\"}\\n</Schema>'},\n", "  {'role': 'user', 'content': 'Query: name is foo'},\n", "  {'role': 'assistant',\n", "   'function_call': {'name': 'QUERY',\n", "    'arguments': '{\"value\": {\"type\": \"SQL\", \"filter\": \"name = \\'foo\\'\", \"explanation\": \"I interpret the query as a string equality filter on the \\\\\"name\\\\\" column. The query does not have any sort semantics, so there is no sort.\"}}'}}],\n", " 'functions': [{'name': 'QUERY',\n", "   'description': 'Break down the query either into a MATCH or SQL call',\n", "   'parameters': {'$defs': {'Match': {'properties': {'type': {'const': 'MATCH',\n", "        'default': 'MATCH',\n", "        'title': 'Type'},\n", "       'explanation': {'description': 'Explanation of why I called the MATCH function',\n", "        'title': 'Explanation',\n", "        'type': 'string'}},\n", "      'required': ['explanation'],\n", "      'title': 'Match',\n", "      'type': 'object'},\n", "     'SQL': {'properties': {'type': {'const': 'SQL',\n", "        'default': 'SQL',\n", "        'title': 'Type'},\n", "       'filter': {'anyOf': [{'type': 'string'}, {'type': 'null'}],\n", "        'description': 'SQL filter clause',\n", "        'title': 'Filter'},\n", "       'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}],\n", "        'description': 'SQL sort clause',\n", "        'title': 'Sort'},\n", "       'explanation': {'description': 'Explanation of why I called the SQL function and how I chose the filter and/or sort clauses',\n", "        'title': 'Explanation',\n", "        'type': 'string'}},\n", "      'required': ['filter', 'sort', 'explanation'],\n", "      'title': 'SQL',\n", "      'type': 'object'}},\n", "    'properties': {'value': {'anyOf': [{'$ref': '#/$defs/Match'},\n", "       {'$ref': '#/$defs/SQL'}],\n", "      'title': 'Value'}},\n", "    'required': ['value'],\n", "    'title': 'Query',\n", "    'type': 'object'}}]}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["def build_expected_messages(query, expected, prompt, score_fields):\n", "    args = build_completion_kwargs(\n", "        query=first[\"input\"],\n", "        model=\"gpt-3.5-turbo\",\n", "        prompt=fine_tune_prompt,\n", "        score_fields=score_fields,\n", "    )\n", "    function_call = transform_function_call(expected)\n", "    return {\n", "        \"messages\": args[\"messages\"]\n", "        + [{\"role\": \"assistant\", \"function_call\": function_call}],\n", "        \"functions\": args[\"functions\"],\n", "    }\n", "\n", "\n", "build_expected_messages(\n", "    first[\"input\"], first[\"expected\"], fine_tune_prompt, SCORE_FIELDS\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's construct messages from our train split and few-shot examples, and then fine-tune the model.\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["49\n"]}, {"data": {"text/plain": ["{'messages': [{'role': 'system',\n", "   'content': 'Table: experiments\\n\\n<Schema>\\n{\"$defs\": {\"ExperimentGitState\": {\"properties\": {\"commit\": {\"description\": \"Git commit hash. Any prefix of this hash at least 7 characters long should be considered an exact match, so use a substring filter rather than string equality to check the commit, e.g. `(source->>\\'commit\\') ILIKE \\'{COMMIT}%\\'`\", \"title\": \"Commit\", \"type\": \"string\"}, \"branch\": {\"description\": \"Git branch name\", \"title\": \"Branch\", \"type\": \"string\"}, \"tag\": {\"anyOf\": [{\"type\": \"string\"}, {\"type\": \"null\"}], \"description\": \"Git commit tag\", \"title\": \"Tag\"}, \"commit_time\": {\"description\": \"Git commit timestamp\", \"title\": \"Commit Time\", \"type\": \"integer\"}, \"author_name\": {\"description\": \"Author of git commit\", \"title\": \"Author Name\", \"type\": \"string\"}, \"author_email\": {\"description\": \"Email address of git commit author\", \"title\": \"Author Email\", \"type\": \"string\"}, \"commit_message\": {\"description\": \"Git commit message\", \"title\": \"Commit Message\", \"type\": \"string\"}, \"dirty\": {\"anyOf\": [{\"type\": \"boolean\"}, {\"type\": \"null\"}], \"description\": \"Whether the git state was dirty when the experiment was run. If false, the git state was clean\", \"title\": \"Dirty\"}}, \"required\": [\"commit\", \"branch\", \"tag\", \"commit_time\", \"author_name\", \"author_email\", \"commit_message\", \"dirty\"], \"title\": \"ExperimentGitState\", \"type\": \"object\"}}, \"properties\": {\"id\": {\"description\": \"Experiment ID, unique\", \"title\": \"Id\", \"type\": \"string\"}, \"name\": {\"description\": \"Name of the experiment\", \"title\": \"Name\", \"type\": \"string\"}, \"last_updated\": {\"description\": \"Timestamp marking when the experiment was last updated. If the query deals with some notion of relative time, like age or recency, refer to this timestamp and, if appropriate, compare it to the current time `get_current_time()` by adding or subtracting an interval.\", \"title\": \"Last Updated\", \"type\": \"integer\"}, \"creator\": {\"additionalProperties\": {\"type\": \"string\"}, \"description\": \"Information about the experiment creator\", \"title\": \"Creator\", \"type\": \"object\"}, \"source\": {\"allOf\": [{\"$ref\": \"#/$defs/ExperimentGitState\"}], \"description\": \"Git state that the experiment was run on\"}, \"metadata\": {\"description\": \"Custom metadata provided by the user. Ignore this field unless the query mentions metadata or refers to a metadata key specifically\", \"title\": \"Metadata\", \"type\": \"object\"}, \"avg_sql_score\": {\"anyOf\": [{\"type\": \"number\"}, {\"type\": \"null\"}], \"title\": \"Avg Sql Score\"}, \"avg_factuality_score\": {\"anyOf\": [{\"type\": \"number\"}, {\"type\": \"null\"}], \"title\": \"Avg Factuality Score\"}}, \"required\": [\"id\", \"name\", \"last_updated\", \"creator\", \"source\", \"metadata\", \"avg_sql_score\", \"avg_factuality_score\"], \"title\": \"Experiment\", \"type\": \"object\"}\\n</Schema>'},\n", "  {'role': 'user', 'content': 'Query: name is foo'},\n", "  {'role': 'assistant',\n", "   'function_call': {'name': 'QUERY',\n", "    'arguments': '{\"value\": {\"type\": \"MATCH\", \"explanation\": \"According to directive 2, a query entirely wrapped in quotes should use the MATCH function.\"}}'}}],\n", " 'functions': [{'name': 'QUERY',\n", "   'description': 'Break down the query either into a MATCH or SQL call',\n", "   'parameters': {'$defs': {'Match': {'properties': {'type': {'const': 'MATCH',\n", "        'default': 'MATCH',\n", "        'title': 'Type'},\n", "       'explanation': {'description': 'Explanation of why I called the MATCH function',\n", "        'title': 'Explanation',\n", "        'type': 'string'}},\n", "      'required': ['explanation'],\n", "      'title': 'Match',\n", "      'type': 'object'},\n", "     'SQL': {'properties': {'type': {'const': 'SQL',\n", "        'default': 'SQL',\n", "        'title': 'Type'},\n", "       'filter': {'anyOf': [{'type': 'string'}, {'type': 'null'}],\n", "        'description': 'SQL filter clause',\n", "        'title': 'Filter'},\n", "       'sort': {'anyOf': [{'type': 'string'}, {'type': 'null'}],\n", "        'description': 'SQL sort clause',\n", "        'title': 'Sort'},\n", "       'explanation': {'description': 'Explanation of why I called the SQL function and how I chose the filter and/or sort clauses',\n", "        'title': 'Explanation',\n", "        'type': 'string'}},\n", "      'required': ['filter', 'sort', 'explanation'],\n", "      'title': 'SQL',\n", "      'type': 'object'}},\n", "    'properties': {'value': {'anyOf': [{'$ref': '#/$defs/Match'},\n", "       {'$ref': '#/$defs/SQL'}],\n", "      'title': 'Value'}},\n", "    'required': ['value'],\n", "    'title': 'Query',\n", "    'type': 'object'}}]}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["train_records = [r for r in records if r[\"metadata\"][\"split\"] == \"train\"] + [\n", "    {\"input\": r[\"query\"], \"expected\": r} for r in few_shot_examples\n", "]\n", "all_expected_messages = [\n", "    build_expected_messages(r[\"input\"], r[\"expected\"], fine_tune_prompt, SCORE_FIELDS)\n", "    for r in train_records\n", "]\n", "\n", "print(len(all_expected_messages))\n", "all_expected_messages[1]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import io\n", "\n", "# Use the direct OpenAI client, not a proxy\n", "sync_client = openai.OpenAI(\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\", \"<Your OpenAI API Key>\"),\n", "    base_url=\"https://api.openai.com/v1\",\n", ")\n", "\n", "file_string = \"\\n\".join(json.dumps(messages) for messages in all_expected_messages)\n", "file = sync_client.files.create(\n", "    file=io.BytesIO(file_string.encode()), purpose=\"fine-tune\"\n", ")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["job = sync_client.fine_tuning.jobs.create(training_file=file.id, model=\"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "start = time.time()\n", "job_id = job.id\n", "while True:\n", "    info = sync_client.fine_tuning.jobs.retrieve(job_id)\n", "    if info.finished_at is not None:\n", "        break\n", "    print(f\"{time.time() - start:.0f}s elapsed\", end=\"\\t\")\n", "    print(str(info), end=\"\\r\")\n", "    time.sleep(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["info = sync_client.fine_tuning.jobs.retrieve(job_id)\n", "fine_tuned_model = info.fine_tuned_model\n", "fine_tuned_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ft_prompt_args = build_completion_kwargs(\n", "    query=first[\"input\"],\n", "    model=fine_tuned_model,\n", "    prompt=fine_tune_prompt,\n", "    score_fields=SCORE_FIELDS,\n", ")\n", "del ft_prompt_args[\"temperature\"]\n", "print(ft_prompt_args)\n", "output = await client.chat.completions.create(**ft_prompt_args)\n", "print(output)\n", "print(format_output(output))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Experiment Fine tuned model is running at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Fine%20tuned%20model\n", "AI Search Cookbook [experiment_name=Fine tuned model] (data): 45it [00:00, 15835.53it/s]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "14d724e71f384465bda2536fb61f9fb2", "version_major": 2, "version_minor": 0}, "text/plain": ["AI Search Cookbook [experiment_name=Fine tuned model] (tasks):   0%|          | 0/45 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "Fine tuned model compared to Long Prompt:\n", "77.78% (-) 'function_choice' score\t(8 improvements, 8 regressions)\n", "75.93% (-08.45%) 'valid_clause'    score\t(0 improvements, 2 regressions)\n", "30.00% (-20.00%) 'exact_match'     score\t(2 improvements, 9 regressions)\n", "48.09% (-23.44%) 'filter'          score\t(5 improvements, 15 regressions)\n", "53.44% (-18.47%) 'sort'            score\t(1 improvements, 4 regressions)\n", "32.22% (-23.33%) 'AutoScorer'      score\t(7 improvements, 18 regressions)\n", "05.36% (+02.23%) 'roundtrip_match' score\t(1 improvements, 1 regressions)\n", "48.22% (-19.77%) 'SQLScorer'       score\t(10 improvements, 25 regressions)\n", "\n", "79.41s (+7350.58%) 'duration'\t(0 improvements, 45 regressions)\n", "\n", "See results for Fine tuned model at https://www.braintrust.dev/app/braintrust.dev/p/AI%20Search%20Cookbook/Fine%20tuned%20model\n"]}], "source": ["await run_eval(\"Fine tuned model\", fine_tune_prompt, fine_tuned_model)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}