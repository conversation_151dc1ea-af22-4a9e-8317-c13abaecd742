name = "proxy"
main = "src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat_v2"]

kv_namespaces = [
    # Configure this id to map to the id returned from
    #   wrangler kv:namespace create ai-proxy
    { binding = "ai_proxy", id = "<YOUR_KV_ID>" },
]

[durable_objects]
bindings = [
    { name = "METRICS_AGGREGATOR", class_name = "PrometheusMetricAggregator" },
]

[[migrations]]
tag = "v1"                                   # Should be unique for each entry
new_classes = ["PrometheusMetricAggregator"] # Array of new classes

# Variable bindings. These are arbitrary, plaintext strings (similar to environment variables)
# Note: Use secrets to store sensitive data.
# Docs: https://developers.cloudflare.com/workers/platform/environment-variables
[vars]
# You should not need to edit this
BRAINTRUST_APP_URL = "https://www.braintrust.dev"
PROMETHEUS_SCRAPE_USER="admin"
PROMETHEUS_SCRAPE_PASSWORD="<YOUR_PASSWORD>"

[env.staging.vars]
BRAINTRUST_APP_URL = "https://www.braintrust.dev"
# These are not real credentials, just populated to suppress a wrangler warning.
PROMETHEUS_SCRAPE_USER="admin"
PROMETHEUS_SCRAPE_PASSWORD="password"

[env.staging]
kv_namespaces = [
    # Configure this id to map to the id returned from
    #   wrangler kv:namespace create ai-proxy
    { binding = "ai_proxy", id = "<YOUR_KV_ID>" },
]

[env.staging.observability]
enabled = true
head_sampling_rate = 1.0 # Sample 100% of staging logs.

[env.staging.durable_objects]
bindings = [
    { name = "METRICS_AGGREGATOR", class_name = "PrometheusMetricAggregator" },
]
