{"name": "@braintrust/ai-proxy-wrangler", "version": "0.0.0", "private": true, "main": "./dist/lib.mjs", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev --port 8787 --inspector-port 9299", "start": "wrangler dev", "watch": "tsup --watch --dts", "build": "tsup --clean --dts"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241022.0", "itty-router": "^3.0.12", "tsup": "^8.4.0", "typescript": "^5.0.4", "wrangler": "^3.107.3"}, "dependencies": {"braintrust": "^0.3.7", "@braintrust/proxy": "workspace:*", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta#cd8a9251dcfb0cba0d7b0501e9ff36c915f5090f", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-metrics": "^1.18.1", "dotenv": "^16.3.1", "zod": "3.25.34"}}