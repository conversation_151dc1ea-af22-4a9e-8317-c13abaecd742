{"name": "braintrust-proxy", "repository": "https://github.com/braintrustdata/braintrust-proxy", "license": "MIT", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@upstash/ratelimit": "^0.4.3", "@vercel/examples-ui": "^1.0.5", "@vercel/kv": "^0.2.2", "@braintrust/proxy": "workspace:*", "next": "14.2.3", "react": "latest", "react-dom": "latest"}, "devDependencies": {"@types/node": "^17.0.45", "@types/react": "latest", "autoprefixer": "^10.4.14", "eslint": "^8.36.0", "eslint-config-next": "canary", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "turbo": "^1.8.5", "typescript": "4.7.4"}}