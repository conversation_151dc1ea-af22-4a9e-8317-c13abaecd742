{"repository": "https://github.com/vercel/examples.git", "license": "MIT", "private": true, "workspaces": ["apis/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "clean": "turbo run clean", "test": "vitest run"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-turbo": "latest", "turbo": "^2.5.6", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9"}, "packageManager": "pnpm@8.15.5", "resolutions": {"zod": "3.25.34"}}