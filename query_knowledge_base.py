import braintrust
from pydantic import BaseModel
from typing import List, Dict, Any


class QueryKnowledgeBaseInput(BaseModel):
    query: str
    max_results: int = 5
    include_metadata: bool = True


class KnowledgeBaseResult(BaseModel):
    source_id: str
    title: str
    content: str
    url: str = ""
    relevance_score: float = 0.0
    metadata: Dict[str, Any] = {}


def query_knowledge_base(query: str, max_results: int = 5, include_metadata: bool = True) -> List[KnowledgeBaseResult]:
    """
    Dummy implementation of query_knowledge_base tool.
    This tool would normally search the knowledge base for relevant information.
    """
    print(f"Querying knowledge base with: {query}")
    print(f"Max results: {max_results}")
    
    # Return dummy knowledge base results
    dummy_results = [
        KnowledgeBaseResult(
            source_id="kb_001",
            title="Getting Started Guide",
            content=f"This is dummy content related to your query: '{query}'. In a real implementation, this would contain relevant documentation or knowledge base articles.",
            url="https://docs.together.ai/getting-started",
            relevance_score=0.95,
            metadata={"category": "documentation", "last_updated": "2024-01-15"} if include_metadata else {}
        ),
        KnowledgeBaseResult(
            source_id="kb_002", 
            title="API Reference",
            content=f"Additional dummy content for query: '{query}'. This would typically contain API documentation or technical details.",
            url="https://docs.together.ai/api-reference",
            relevance_score=0.87,
            metadata={"category": "api", "last_updated": "2024-01-10"} if include_metadata else {}
        ),
        KnowledgeBaseResult(
            source_id="kb_003",
            title="Troubleshooting Guide", 
            content=f"Troubleshooting information related to: '{query}'. This would contain common issues and solutions.",
            url="https://docs.together.ai/troubleshooting",
            relevance_score=0.72,
            metadata={"category": "troubleshooting", "last_updated": "2024-01-08"} if include_metadata else {}
        )
    ]
    
    # Return only the requested number of results
    return dummy_results[:max_results]


project = braintrust.projects.create(name="pedro-project1")

query_knowledge_base_tool = project.tools.create(
    handler=query_knowledge_base,
    name="Query Knowledge Base",
    slug="query-knowledge-base",
    description="Search the knowledge base for information relevant to customer inquiries and support requests.",
    parameters=QueryKnowledgeBaseInput,
    if_exists="replace",
)
