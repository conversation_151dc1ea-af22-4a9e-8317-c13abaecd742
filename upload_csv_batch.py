#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to upload a large CSV file to Braintrust dataset in batches.
This script processes the longmemeval_m_full.csv file and uploads 25 records at a time.
"""

import argparse
import csv
import json
import sys
import time
from typing import Any, Dict, List

import braintrust

# Increase CSV field size limit for large fields
csv.field_size_limit(sys.maxsize)


def try_json(s: str) -> Any:
    """Try to parse a string as JSON, return the string if it fails."""
    try:
        return json.loads(s)
    except (json.JSONDecodeError, TypeError):
        return s


def process_csv_in_batches(
    csv_file_path: str,
    project_name: str,
    dataset_name: str,
    batch_size: int = 25,
    input_field: str = "input",
    expected_field: str = "expected", 
    metadata_field: str = "metadata"
) -> None:
    """
    Process CSV file and upload to Braintrust dataset in batches.
    
    Args:
        csv_file_path: Path to the CSV file
        project_name: Braintrust project name
        dataset_name: Braintrust dataset name
        batch_size: Number of records to process in each batch
        input_field: Name of the input column in CSV
        expected_field: Name of the expected column in CSV
        metadata_field: Name of the metadata column in CSV
    """
    
    # Initialize the dataset
    print(f"Initializing dataset '{dataset_name}' in project '{project_name}'...")
    dataset = braintrust.init_dataset(project=project_name, name=dataset_name)
    
    # Open and process the CSV file
    with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        
        # Verify required columns exist
        if input_field not in reader.fieldnames:
            raise ValueError(f"Input field '{input_field}' not found in CSV headers: {reader.fieldnames}")
        if expected_field not in reader.fieldnames:
            raise ValueError(f"Expected field '{expected_field}' not found in CSV headers: {reader.fieldnames}")
        if metadata_field not in reader.fieldnames:
            raise ValueError(f"Metadata field '{metadata_field}' not found in CSV headers: {reader.fieldnames}")
        
        print(f"CSV columns found: {reader.fieldnames}")
        print(f"Processing CSV in batches of {batch_size} records...")
        
        batch = []
        total_processed = 0
        batch_count = 0
        
        for row_num, row in enumerate(reader, start=1):
            try:
                # Parse the fields, handling JSON where appropriate
                input_data = try_json(row[input_field])
                expected_data = try_json(row[expected_field])
                metadata_data = try_json(row[metadata_field])
                
                # Ensure metadata is a dictionary
                if not isinstance(metadata_data, dict):
                    metadata_data = {"original_metadata": metadata_data}
                
                # Add any extra fields as metadata
                extra_metadata = {}
                for field_name, field_value in row.items():
                    if field_name not in [input_field, expected_field, metadata_field]:
                        extra_metadata[field_name] = try_json(field_value)
                
                # Merge extra metadata
                if extra_metadata:
                    metadata_data.update(extra_metadata)
                
                # Add to current batch
                batch.append({
                    'input': input_data,
                    'expected': expected_data,
                    'metadata': metadata_data
                })
                
                # Process batch when it reaches the specified size
                if len(batch) >= batch_size:
                    batch_count += 1
                    print(f"Processing batch {batch_count} (rows {total_processed + 1}-{total_processed + len(batch)})...")
                    
                    # Insert records in the current batch
                    for record in batch:
                        dataset.insert(
                            input=record['input'],
                            expected=record['expected'],
                            metadata=record['metadata']
                        )
                    
                    # Flush the batch to ensure it's uploaded
                    dataset.flush()
                    
                    total_processed += len(batch)
                    print(f"✓ Batch {batch_count} uploaded successfully. Total processed: {total_processed}")
                    
                    # Clear the batch and add a small delay to avoid overwhelming the API
                    batch = []
                    time.sleep(0.5)  # 500ms delay between batches
                    
            except Exception as e:
                print(f"Error processing row {row_num}: {e}")
                print(f"Row data: {row}")
                continue
        
        # Process any remaining records in the final batch
        if batch:
            batch_count += 1
            print(f"Processing final batch {batch_count} (rows {total_processed + 1}-{total_processed + len(batch)})...")
            
            for record in batch:
                dataset.insert(
                    input=record['input'],
                    expected=record['expected'],
                    metadata=record['metadata']
                )
            
            dataset.flush()
            total_processed += len(batch)
            print(f"✓ Final batch uploaded successfully. Total processed: {total_processed}")
    
    print(f"\n🎉 Upload complete! Total records processed: {total_processed}")
    print(f"Dataset summary:")
    print(dataset.summarize())


def main():
    """Main function to handle command line arguments and run the upload process."""
    parser = argparse.ArgumentParser(
        description="Upload CSV file to Braintrust dataset in batches",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python upload_csv_batch.py MyProject MyDataset
  python upload_csv_batch.py MyProject MyDataset --batch-size 50
  python upload_csv_batch.py MyProject MyDataset --csv-file custom_file.csv
        """
    )
    
    parser.add_argument("project_name", help="Braintrust project name")
    parser.add_argument("dataset_name", help="Braintrust dataset name")
    parser.add_argument(
        "--csv-file", 
        default="longmemeval_m_full.csv",
        help="Path to CSV file (default: longmemeval_m_full.csv)"
    )
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=25,
        help="Number of records to process in each batch (default: 25)"
    )
    parser.add_argument(
        "--input-field", 
        default="input",
        help="Name of the input column in CSV (default: input)"
    )
    parser.add_argument(
        "--expected-field", 
        default="expected",
        help="Name of the expected column in CSV (default: expected)"
    )
    parser.add_argument(
        "--metadata-field", 
        default="metadata",
        help="Name of the metadata column in CSV (default: metadata)"
    )
    
    args = parser.parse_args()
    
    try:
        process_csv_in_batches(
            csv_file_path=args.csv_file,
            project_name=args.project_name,
            dataset_name=args.dataset_name,
            batch_size=args.batch_size,
            input_field=args.input_field,
            expected_field=args.expected_field,
            metadata_field=args.metadata_field
        )
    except KeyboardInterrupt:
        print("\n⚠️  Upload interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
