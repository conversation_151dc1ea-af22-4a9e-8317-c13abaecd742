{"name": "@braintrust/stainless-deployment", "version": "0.0.0", "private": true, "bin": {"generate-stainless-config": "./dist/generate_stainless_config.js"}, "scripts": {"build": "tsup", "watch": "tsup --watch", "clean": "rm -r dist/*"}, "files": ["dist/**/*"], "devDependencies": {"@types/argparse": "^2.0.14", "@types/pluralize": "^0.0.30", "tsup": "^8.4.0", "tsx": "^3.14.0", "typescript": "^5.3.3"}, "dependencies": {"braintrust": "workspace:*", "@braintrust/typespecs": "workspace:*", "argparse": "^2.0.1", "pluralize": "^8.0.0", "yaml": "^2.3.4"}}