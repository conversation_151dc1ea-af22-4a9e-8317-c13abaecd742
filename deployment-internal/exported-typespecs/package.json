{"name": "@braintrust/exported-typespecs", "description": "Export a set of typespecs to an openapi schema for external consumption", "version": "0.0.0", "private": true, "scripts": {"build": "tsc --noEmit && tsup", "watch": "tsup --watch", "clean": "rm -r dist/*"}, "files": ["dist/**/*"], "dependencies": {"@asteasolutions/zod-to-openapi": "^6.3.1", "@braintrust/typespecs": "workspace:*"}, "devDependencies": {"@swc/core": "^1.13.3", "tsup": "^8.4.0", "tsx": "^3.14.0", "typescript": "^5.3.3"}}