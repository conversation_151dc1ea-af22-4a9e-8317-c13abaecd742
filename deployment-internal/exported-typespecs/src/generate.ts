import { OpenApiGeneratorV31 } from "@asteasolutions/zod-to-openapi";
import { extendZodWithOpenApi } from "@asteasolutions/zod-to-openapi";
import { z } from "zod";
import {
  X_OPENAPI_OUTPUT_MARK_REQUIRED,
  X_OPENAPI_OUTPUT_STRIP_NULL,
  anyModelParamsSchema,
  apiSpecEventObjectSchemas,
  apiSpecObjectSchemas,
  asyncScoringControlSchema,
  attachmentReferenceSchema,
  attachmentStatusSchema,
  braintrustModelParamsSchema,
  callEventSchema,
  chatCompletionOpenAIMessageParamSchema,
  chatCompletionToolSchema,
  eventObjectType as eventObjectTypes,
  extendedSavedFunctionIdSchema,
  functionIdSchema,
  gitMetadataSettingsSchema,
  graphDataSchema,
  graphEdgeSchema,
  graphNodeSchema,
  ifExistsEnum,
  invokeFunctionSchema,
  invokeParentSchema,
  messageRoleSchema,
  objectReferenceSchema,
  objectTypes,
  promptDataSchema,
  promptOptionsSchema,
  promptSessionEventSchema,
  responseFormatJsonSchemaSchema,
  responseFormatSchema,
  runEvalSchema,
  sseConsoleEventDataSchema,
  sseProgressEventDataSchema,
  streamingModeEnum,
  toolFunctionDefinitionSchema,
} from "@braintrust/typespecs";

extendZodWithOpenApi(z);

function main() {
  const allSchemas: z.ZodTypeAny[] = [];

  // Add control plane object types.
  for (const objectType of objectTypes.options) {
    const s = apiSpecObjectSchemas[objectType];
    if (s.object) {
      allSchemas.push(s.object);
    }
  }

  // Add data plane object types.
  for (const eventObjectType of eventObjectTypes.options) {
    const s = apiSpecEventObjectSchemas[eventObjectType];
    if (s.event) {
      allSchemas.push(s.event);
    }
  }

  // A few additional schemas used in various places.
  allSchemas.push(
    anyModelParamsSchema,
    asyncScoringControlSchema,
    attachmentReferenceSchema,
    attachmentStatusSchema,
    braintrustModelParamsSchema,
    callEventSchema,
    chatCompletionOpenAIMessageParamSchema,
    chatCompletionToolSchema,
    extendedSavedFunctionIdSchema,
    functionIdSchema,
    gitMetadataSettingsSchema,
    graphDataSchema,
    graphEdgeSchema,
    graphNodeSchema,
    ifExistsEnum,
    invokeFunctionSchema,
    invokeParentSchema,
    messageRoleSchema,
    objectReferenceSchema,
    promptDataSchema,
    promptOptionsSchema,
    promptSessionEventSchema,
    responseFormatJsonSchemaSchema,
    responseFormatSchema,
    runEvalSchema,
    sseConsoleEventDataSchema,
    sseProgressEventDataSchema,
    streamingModeEnum,
    toolFunctionDefinitionSchema,
  );

  const generator = new OpenApiGeneratorV31(allSchemas);
  const doc = postprocessGeneratedSpec(
    generator.generateDocument({
      openapi: "3.1.0",
      info: {
        version: "1.0.0",
        title: "Braintrust exported typespecs",
        description: "Publicly exported typespecs for the Braintrust API.",
        license: {
          name: "Apache 2.0",
        },
        "x-internal-git-sha": process.env.GIT_COMMIT
          ? process.env.GIT_COMMIT
          : undefined,
      },
    }),
  );
  console.log(JSON.stringify(doc, null, 2));
}

main();

// Post-processing functions.

function postprocessGeneratedSpec(rawSpec: unknown): unknown {
  const spec = z
    .object({
      components: z
        .object({
          schemas: z.record(z.string(), z.unknown()),
        })
        .passthrough(),
    })
    .passthrough()
    .parse(rawSpec);

  // Sort the schemas by name.
  spec.components.schemas = (() => {
    const entries = Object.entries(spec.components.schemas);
    entries.sort((a, b) => a[0].localeCompare(b[0]));
    return Object.fromEntries(entries);
  })();

  // Run post-processing subroutines on every schema object.
  for (const [name, schema] of Object.entries(spec.components.schemas)) {
    spec.components.schemas[name] = postProcessSchema(schema);
  }

  return spec;
}

function postProcessSchema(schema: unknown): unknown {
  if (!(schema instanceof Object)) {
    return schema;
  }

  // Apply post-processing to the toplevel object, then recurse through the
  // sub-objects.
  schema = processStripNull(schema);
  schema = processMarkRequired(schema);

  if (schema instanceof Object) {
    for (const [key, value] of Object.entries(schema)) {
      Object.assign(schema, { [key]: postProcessSchema(value) });
    }
  }

  return schema;
}

// Remove the "null" type from any schemas containing the
// X_OPENAPI_OUTPUT_STRIP_NULL property.
function processStripNull(rawSchema: unknown): unknown {
  const parsedSchema = z
    .object({
      type: z.array(z.string()),
      [X_OPENAPI_OUTPUT_STRIP_NULL]: z.literal(true),
    })
    .passthrough()
    .safeParse(rawSchema);

  if (parsedSchema.success) {
    const {
      type: schemaTypes,
      "x-openapi-output-strip-null": _,
      ...schemaRest
    } = parsedSchema.data;
    const newTypes = schemaTypes.filter((t) => t !== "null");
    if (newTypes.length >= 1) {
      rawSchema = {
        type: newTypes.length === 1 ? newTypes[0] : newTypes,
        ...schemaRest,
      };
    }
  }

  return rawSchema;
}

// Mark all properties with the X_OPENAPI_OUTPUT_MARK_REQUIRED property as
// required.
function processMarkRequired(rawSchema: unknown): unknown {
  const parsedSchema = z
    .object({
      type: z.literal("object"),
      properties: z.record(z.string(), z.record(z.string(), z.unknown())),
      required: z.array(z.string()).optional(),
    })
    .passthrough()
    .safeParse(rawSchema);

  if (parsedSchema.success) {
    const schema = parsedSchema.data;
    for (const [field, props] of Object.entries(schema.properties)) {
      if (props[X_OPENAPI_OUTPUT_MARK_REQUIRED] === true) {
        if (!schema.required) {
          schema.required = [];
        }
        if (!schema.required.includes(field)) {
          schema.required.push(field);
        }
        delete props[X_OPENAPI_OUTPUT_MARK_REQUIRED];
      }
    }
    rawSchema = schema;
  }

  return rawSchema;
}
