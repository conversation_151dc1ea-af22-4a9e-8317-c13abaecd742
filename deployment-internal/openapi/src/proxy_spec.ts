import z from "zod";
import { registerCORSPath, errorResponses } from "./common_spec";
import { type OpenAPIRegistry } from "@asteasolutions/zod-to-openapi";

// This is copy/pasted from proxy/packages/proxy/schema/secrets.ts.
export const proxyLoggingParamSchema = z
  .object({
    project_name: z.string(),
    compress_audio: z.boolean().default(true),
  })
  .describe(
    "If present, proxy will log requests to the given Braintrust project name.",
  );

export const credentialsRequestSchema = z
  .object({
    model: z
      .string()
      .nullish()
      .describe(
        "Granted model name. Null/undefined to grant usage of all models.",
      ),
    ttl_seconds: z
      .number()
      .max(60 * 60 * 24)
      .default(60 * 10)
      .describe("TTL of the temporary credential. 10 minutes by default."),
    logging: proxyLoggingParamSchema.nullish(),
  })
  .describe("Payload for requesting temporary credentials.");

const proxyEndpoints = [
  {
    path: "chat/completions",
    description: `Proxy a chat/completions request to the specified model, converting its format as needed. Will cache if temperature=0 or seed is set.`,
    summary: `Proxy chat/completions`,
    bodyDescription: `See the [OpenAI docs](https://platform.openai.com/docs/api-reference/chat/create) for details.`,
  },
  {
    path: "completions",
    description: `Proxy a completions request to the specified model, converting its format as needed. Will cache if temperature=0 or seed is set.`,
    summary: `Proxy completions`,
    bodyDescription: `See the [OpenAI docs](https://platform.openai.com/docs/api-reference/completions/create) for details.`,
  },
  {
    path: "auto",
    description: `Proxy a request to either chat/completions or completions automatically based on the model. Will cache if temperature=0 or seed is set.`,
    summary: `Proxy a model to chat/completions or completions automatically`,
    bodyDescription: `The chat/completions or completions payload (depending on the model)`,
  },
  {
    path: "embeddings",
    description: `Proxy an embeddings request to the specified model, converting its format as needed. Will cache automatically.`,
    summary: `Proxy embeddings`,
    bodyDescription: `See the [OpenAI docs](https://platform.openai.com/docs/api-reference/embeddings/create) for details.`,
  },
  {
    path: "credentials",
    description: `Create a temporary credential which can access the proxy for a limited time. The temporary credential will be allowed to make requests on behalf of the Braintrust API key (or model provider API key) provided in the \`Authorization\` header. See [docs](/docs/guides/proxy#temporary-credentials-for-end-user-access) for code examples.`,
    summary: `Create temporary credential`,
    request: {
      body: {
        description: `The temporary credential will be restricted according to the request body.`,
        content: {
          "application/json": { schema: credentialsRequestSchema },
        },
        required: true,
      },
    },
    responses: {
      200: {
        description: `Successfully created temporary credential`,
        content: {
          "application/json": { schema: z.object({ key: z.string() }) },
        },
      },
      400: {
        description: errorResponses[400].description,
        content: {
          "text/plain": { schema: z.string() },
        },
      },
    },
  },
  {
    path: "{path+}",
    description: `Any requests which do not match the above paths will be proxied directly to the OpenAI API.`,
    summary: `Proxy any OpenAI request (fallback)`,
    bodyDescription: `The request body`,
    parameters: [
      {
        name: "path+",
        in: "path",
        required: true,
        schema: { type: "array", items: { type: "string" } },
        description: "The path to proxy",
      } as const,
    ],
  },
];

export function registerProxyEndpoints({
  registry,
  security,
}: {
  registry: OpenAPIRegistry;
  security: { [name: string]: string[] }[];
}) {
  for (const {
    path,
    description,
    summary,
    bodyDescription,
    request,
    parameters,
    responses,
  } of proxyEndpoints) {
    const operationId =
      "proxy" +
      path
        .split("/")
        .map((part, index) =>
          index === 0 ? part : part.charAt(0).toUpperCase() + part.slice(1),
        )
        .join("");
    registry.registerPath({
      operationId,
      method: "post",
      path: `/v1/proxy/${path}`,
      tags: ["Proxy"],
      description,
      summary,
      request: request ?? {
        body: {
          description: bodyDescription,
          content: {
            "application/json": { schema: z.any() },
          },
          required: true,
        },
      },
      parameters,
      responses: responses ?? {
        200: {
          description:
            "Proxy response (supports both streaming and non-streaming formats)",
          content: {
            "application/json": { schema: z.any() },
          },
        },
      },
      security,
    });
    registerCORSPath(registry, {
      path: `/v1/proxy/${path}`,
      operationId: `optionsProxy${operationId}`,
      parameters,
    });
  }
}
