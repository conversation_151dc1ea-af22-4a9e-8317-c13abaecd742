import z from "zod";
import isEqual from "lodash.isequal";
import {
  X_OPENAPI_OUTPUT_MARK_REQUIRED,
  X_OPENAPI_OUTPUT_STRIP_NULL,
} from "@braintrust/typespecs";

const nullableTrueSchema = z.strictObject({
  nullable: z.literal(true),

  // The nullable-true case may have additional properties, such as
  // `description`, that should be copied over. This can happen if the zod
  // schema contains `someSchema.nullish().describe("...")`.
  title: z.string().optional(),
  description: z.string().optional(),
});

export function cleanupV3Spec(o: unknown) {
  if (!(o && typeof o === "object")) {
    return o;
  }

  // As a special-case, when zod unions are marked nullish, the generator will
  // add a clause `{ "nullable": true }` to the union. This is incorrect because
  // by leaving out the `type`, it permits any type in the union. We patch this
  // here by detecting such cases and replacing it with an placeholder nullable
  // object.
  if ("anyOf" in o && Array.isArray(o.anyOf)) {
    const variants = o["anyOf"];
    const isNullableTrue = (x: unknown) => isEqual(x, { nullable: true });
    const hasNullableTrue = variants.find(isNullableTrue);
    const filteredVariants = variants.filter(
      (x: unknown) => !isNullableTrue(x),
    );
    if (hasNullableTrue) {
      filteredVariants.push({ type: "null" });
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    const transformedVariants = filteredVariants.map((x: any) => {
      if (!(x instanceof Object)) {
        return x;
      }
      const newX = { ...x };
      // The golang stainless SDK does not support variant types with
      // additionalProperties.
      if (newX["type"] === "object" && newX["additionalProperties"]) {
        newX["x-stainless-skip"] = ["go"];
      }
      return newX;
    });
    o["anyOf"] = transformedVariants;
  }

  // Similar to the above case. When any kind of zod schema is marked nullish,
  // such as `someSchema.nullish().description("...")`, the OpenAPI converter
  // will generate `allOf` with separate items for `someSchema` and another item
  // with `{ "nullable": true }` and perhaps a description. If possible, we
  // coalesce the `nullable` flag to the `someSchema` object.
  if ("allOf" in o && Array.isArray(o.allOf)) {
    const variants = o.allOf;
    const isNullableTrue = (x: unknown) =>
      nullableTrueSchema.safeParse(x).success;
    const hasNullableTrue = variants.find(isNullableTrue);
    const filteredVariants = variants.filter(
      (x: unknown) => !isNullableTrue(x),
    );
    if (hasNullableTrue) {
      delete o.allOf;

      if (filteredVariants.length === 0) {
        // Degenerate `allOf` with only one case. This should not happen.
        Object.assign(o, hasNullableTrue);
      }
      if (filteredVariants.length === 1) {
        // This is the primary case.
        Object.assign(o, filteredVariants[0], hasNullableTrue);
      } else {
        Object.assign(o, {
          anyOf: [{ allOf: filteredVariants }, hasNullableTrue],
        });
      }
    }
  }

  // Some validators require that when a type has an `enum` restriction and
  // `nullable: true`, that `null` be one of the cases in the enum. We add this
  // in if isn't already the case.
  if (
    "nullable" in o &&
    o.nullable &&
    "enum" in o &&
    Array.isArray(o.enum) &&
    !o.enum.includes(null)
  ) {
    o.enum.push(null);
  }

  // Remove extra headers we don't consider in this generator.
  Object.assign(o, {
    [X_OPENAPI_OUTPUT_STRIP_NULL]: undefined,
    [X_OPENAPI_OUTPUT_MARK_REQUIRED]: undefined,
  });

  if (process.env.BRAINTRUST_TYPESPECS_MODE === "stainless") {
    cleanupV3SpecStainless(o);
  }

  for (const v of Object.values(o)) {
    cleanupV3Spec(v);
  }

  return o;
}

// Stainless-specific cleanups to the openAPI spec.
function cleanupV3SpecStainless(o: object) {
  // For any open type definitions which just consist of `"nullable": true`,
  // stainless requires adding the additional property `"x-stainless-any":
  // true`.
  {
    const oKeys = Object.keys(o).filter((x) => x !== "description");
    if (oKeys.length === 1 && oKeys[0] === "nullable") {
      Object.assign(o, { "x-stainless-any": true });
    }
  }
}
