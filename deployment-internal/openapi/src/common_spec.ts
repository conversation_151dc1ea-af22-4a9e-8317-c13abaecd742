import {
  OpenAPIRegistry,
  ResponseConfig,
} from "@asteasolutions/zod-to-openapi";
import { z } from "zod";

export interface CORSPath {
  path: string;
  operationId: string;
  requestParams?: z.AnyZodObject;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  parameters?: any[]; // I can't figure out how to hoist this type out
}

export function registerCORSPath(registry: OpenAPIRegistry, path: CORSPath) {
  const headerObject = { schema: { type: "string" } } as const;
  registry.registerPath({
    operationId: path.operationId,
    method: "options",
    path: path.path,
    description: "Enable CORS",
    summary: `Enable CORS (\`${path.path}\`)`,
    security: [],
    tags: ["CORS"],
    request: {
      params: path.requestParams,
    },
    parameters: path.parameters,
    responses: {
      200: {
        description: "Response for CORS method",
        headers: {
          ["Access-Control-Allow-Credentials"]: headerObject,
          ["Access-Control-Allow-Headers"]: headerObject,
          ["Access-Control-Allow-Methods"]: headerObject,
          ["Access-Control-Allow-Origin"]: headerObject,
          ["Access-Control-Max-Age"]: headerObject,
        },
        content: {},
      },
      400: errorResponses[400],
    },
  });
}

export function registerBearerAuth(registry: OpenAPIRegistry) {
  return registry.registerComponent("securitySchemes", "bearerAuth", {
    type: "http",
    scheme: "bearer",
    bearerFormat: "API key or JWT",
    description:
      "Most Braintrust endpoints are authenticated by providing your API key as a header `Authorization: Bearer [api_key]` to your HTTP request. You can create an API key in the Braintrust [organization settings page](https://www.braintrustdata.com/app/settings?subroute=api-keys).",
  });
}

// Descriptions taken from https://stripe.com/docs/api/errors.
export const errorResponses: Record<
  400 | 401 | 403 | 429 | 500,
  ResponseConfig
> = {
  400: {
    description:
      "The request was unacceptable, often due to missing a required parameter",
    content: {
      "text/plain": { schema: z.string() },
      "application/json": { schema: z.any() },
    },
  },
  401: {
    description: "No valid API key provided",
    content: {
      "text/plain": { schema: z.string() },
      "application/json": { schema: z.any() },
    },
  },
  403: {
    description: "The API key doesn’t have permissions to perform the request",
    content: {
      "text/plain": { schema: z.string() },
      "application/json": { schema: z.any() },
    },
  },
  429: {
    description:
      "Too many requests hit the API too quickly. We recommend an exponential backoff of your requests",
    content: {
      "text/plain": { schema: z.string() },
      "application/json": { schema: z.any() },
    },
  },
  500: {
    description: "Something went wrong on Braintrust's end. (These are rare.)",
    content: {
      "text/plain": { schema: z.string() },
      "application/json": { schema: z.any() },
    },
  },
};

export function schemaHasRequired(schema: unknown): boolean {
  return false;
}
