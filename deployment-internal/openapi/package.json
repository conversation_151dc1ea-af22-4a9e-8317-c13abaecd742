{"name": "@braintrust/openapi-deployment", "version": "0.0.0", "private": true, "bin": {"generate-openapi-spec": "./dist/generate-openapi-spec/generate_openapi_spec.js", "generate-openapi-spec-stainless": "./dist/generate-openapi-spec-stainless/generate_openapi_spec.js"}, "scripts": {"build": "tsc --noEmit && tsup", "watch": "tsup --watch", "clean": "rm -r dist/*"}, "files": ["dist/**/*"], "devDependencies": {"@types/argparse": "^2.0.14", "@types/lodash.isequal": "^4.5.8", "tsup": "^8.4.0", "tsx": "^3.14.0", "typescript": "^5.3.3"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^6.3.1", "braintrust": "workspace:*", "@braintrust/typespecs": "workspace:*", "argparse": "^2.0.1", "lodash.isequal": "^4.5.0", "yaml": "^2.3.4", "zod": "^3.25.34"}}