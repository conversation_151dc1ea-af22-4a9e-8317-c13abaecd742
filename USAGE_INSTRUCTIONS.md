# Upload Instructions for longmemeval_m_full.csv

## Quick Start

To upload your `longmemeval_m_full.csv` file to Braintrust in batches of 25 records:

```bash
# Make sure you have your Braintrust API key set
export BRAINTRUST_API_KEY="your-api-key-here"

# Run the upload script
python upload_csv_batch.py "LongMemEval" "Full Dataset"
```

This will:
- Create a project called "LongMemEval" (if it doesn't exist)
- Create a dataset called "Full Dataset" within that project
- Upload all 500 records from your CSV file in batches of 25
- Take approximately 10-15 minutes to complete

## Files Created

1. **`upload_csv_batch.py`** - Main upload script
2. **`test_csv_parsing.py`** - Test script to verify CSV parsing
3. **`README_upload_script.md`** - Detailed documentation
4. **`USAGE_INSTRUCTIONS.md`** - This file

## Your CSV File Structure

Your `longmemeval_m_full.csv` file contains:
- **500 data rows** (plus 1 header row)
- **File size**: 2.5GB
- **Columns**: `input`, `expected`, `metadata`
- **Format**: All fields contain JSON data with complex nested structures

### Sample Data Structure

**Input field** contains:
- `partner_user_id`: User identifier
- `question_id`: Unique question ID
- `question`: The actual question text
- `question_date`: When the question was asked
- `question_type`: Type of question
- `haystack_dates`: Array of relevant dates
- `haystack_session_ids`: Array of session IDs
- `haystack_sessions`: Array of conversation sessions (very large)

**Expected field** contains:
- `answer`: The expected answer
- `answer_session_ids`: Session IDs for the answer

**Metadata field** contains:
- `user_id`: User identifier
- `id`: Record identifier

## Customization Options

### Different batch sizes
```bash
# Upload in batches of 10 (slower but more conservative)
python upload_csv_batch.py "LongMemEval" "Full Dataset" --batch-size 10

# Upload in batches of 50 (faster but uses more memory)
python upload_csv_batch.py "LongMemEval" "Full Dataset" --batch-size 50
```

### Different project/dataset names
```bash
python upload_csv_batch.py "MyProject" "MyDataset"
```

### Different CSV file
```bash
python upload_csv_batch.py "LongMemEval" "Full Dataset" --csv-file "other_file.csv"
```

## Monitoring Progress

The script provides detailed progress updates:

```
Initializing dataset 'Full Dataset' in project 'LongMemEval'...
CSV columns found: ['input', 'expected', 'metadata']
Processing CSV in batches of 25 records...
Processing batch 1 (rows 1-25)...
✓ Batch 1 uploaded successfully. Total processed: 25
Processing batch 2 (rows 26-50)...
✓ Batch 2 uploaded successfully. Total processed: 50
...
Processing batch 20 (rows 476-500)...
✓ Batch 20 uploaded successfully. Total processed: 500
🎉 Upload complete! Total records processed: 500
```

## Error Handling

- If individual rows fail, the script continues with the next row
- Progress is saved after each batch, so you can resume if interrupted
- Use Ctrl+C to stop the upload gracefully

## Performance Tips

1. **For large files like yours**: Use the default batch size of 25
2. **If you have memory constraints**: Reduce batch size to 10-15
3. **If you have a fast connection**: You can try batch size 50
4. **For very slow connections**: Use batch size 10 with longer delays

## Verification

After upload, you can verify the data in the Braintrust web interface:
1. Go to your Braintrust dashboard
2. Navigate to the "LongMemEval" project
3. Open the "Full Dataset" dataset
4. You should see all 500 records with their complex JSON structures

## Troubleshooting

### Common Issues

1. **API Key not set**:
   ```bash
   export BRAINTRUST_API_KEY="your-key-here"
   ```

2. **Memory issues**: Reduce batch size
   ```bash
   python upload_csv_batch.py "LongMemEval" "Full Dataset" --batch-size 10
   ```

3. **Network timeouts**: The script includes automatic retries and delays

4. **CSV parsing errors**: The script handles large fields automatically

### Getting Help

Run the script with `--help` to see all options:
```bash
python upload_csv_batch.py --help
```

## Estimated Time

For your 500-record, 2.5GB file:
- **Batch size 10**: ~20-25 minutes
- **Batch size 25**: ~10-15 minutes  
- **Batch size 50**: ~8-12 minutes

The actual time depends on your internet connection speed and Braintrust API response times.
